module ItemElasticsearchable
  extend ActiveSupport::Concern

  included do
    include Elasticsearch::Model
    # include Elasticsearch::Model::Callbacks
    after_save :es_index

    settings index: { number_of_shards: 1, number_of_replicas: 0 } do
      mappings dynamic: 'false' do
        indexes :name, type: 'text', analyzer: 'english' do
          indexes :keyword, type: 'keyword'
        end
        indexes :description, type: 'text', analyzer: 'english' do
          indexes :keyword, type: 'keyword'
        end
        indexes :price, type: 'float'
        indexes :item_category_name, type: 'keyword'
        indexes :innovation_score, type: 'float'
        indexes :restaurant_chain_count, type: 'integer'
        indexes :search_uniq_key, type: 'keyword'
        indexes :brand_ids, type: 'integer'
        indexes :ingredient_ids, type: 'integer'
        indexes :gpt_ingredient_ids, type: 'integer'
        indexes :has_image, type: 'boolean'
        indexes :city_id, type: 'integer'
        indexes :type, type: 'keyword'
        indexes :image_url, type: 'text'


        indexes :menu, type: 'object' do
          indexes :id, type: 'integer'
          indexes :state, type: 'keyword'
          indexes :quarter, type: 'integer'
          indexes :year, type: 'integer'
        end

        indexes :restaurant, type: 'object' do
          indexes :id, type: 'integer'
          indexes :business_name, type: 'text'
          indexes :address, type: 'text'
          indexes :city, type: 'text'
          indexes :city_id, type: 'integer'
          indexes :state, type: 'text'
          indexes :state_id, type: 'integer'
          indexes :zip_code, type: 'keyword'
          indexes :chain_id, type: 'integer'
          indexes :chain_name, type: 'text'
          indexes :yelp_categories, type: 'keyword'
          indexes :category_name, type: 'keyword'
          indexes :restaurant_type, type: 'keyword'
          indexes :quarterly_sample, type: 'boolean'
          indexes :price, type: 'text'
          indexes :opening_date, type: 'date', format: 'yyyy-MM-dd'
        end

        indexes :region_ids, type: 'integer'
        indexes :report_ids, type: 'integer'
        indexes :menu_item_dashboard_ids, type: 'integer'

        # Meta Data Tags
        indexes :score, type: 'float'
        indexes :gmi, type: 'keyword'
        indexes :cuisine_type, type: 'keyword'
        indexes :day_part, type: 'keyword'
        indexes :menu_type, type: 'keyword'
        indexes :dietary_tags, type: 'keyword'
        indexes :taste_profile, type: 'keyword'
        indexes :texture, type: 'keyword'
        indexes :prep_methods, type: 'keyword'
        indexes :meta_data_category, type: 'keyword'
        indexes :meta_data_sub_category, type: 'keyword'

      end
    end unless Rails.env.test?

    def as_indexed_json(options = {})
      data = {
        name: name.to_s,
        description: description.to_s,
        price: price.to_f,
        item_category_name: item_category_name || 'unknown', # Ensure no null values
        restaurant_chain_count: get_chain_restaurant_count,
        search_uniq_key: get_search_uniq_key,
        innovation_score: innovation_score.to_f,
        brand_ids: brands.approved.pluck(:id),
        ingredient_ids: ingredients.approved.pluck(:id),
        gpt_ingredient_ids: new_gpt_ingredients.pluck(:id),
        has_image: yelp_images.approved.present?,
        city_id: city&.id.to_i,
        image_url: image_url.to_s,
        type: type.to_s,
        menu: {
          id: menu&.id,
          state: menu&.state,
          quarter: menu&.quarter,
          year: menu&.year
        },
        restaurant: {
          id: restaurant&.id,
          business_name: restaurant&.business_name,
          address: restaurant&.address,
          city: restaurant&.city_name,
          city_id: restaurant&.city&.id,
          state: restaurant&.state_name,
          state_id: restaurant&.city&.state&.id,
          zip_code: restaurant&.zip_code,
          chain_id: restaurant&.chain_id,
          chain_name: restaurant&.chain&.name,
          yelp_categories: restaurant&.yelp_categories,
          restaurant_type: restaurant&.restaurant_type,
          quarterly_sample: restaurant&.quarterly_sample,
          price: restaurant&.price
        },
        region_ids: regions.distinct.pluck(:id),
        report_ids: reports.distinct.pluck(:id),
        menu_item_dashboard_ids: menu_item_dashboards.distinct.pluck(:id)
      }
      if item_meta_datum.present?
        data = data.merge(
          {
            :gmi => item_meta_datum.gmi,
            :score => item_meta_datum.score,
            :cuisine_type => item_meta_datum.cuisine_type,
            :day_part => item_meta_datum.day_part,
            :menu_type => item_meta_datum.menu_type,
            :dietary_tags => item_meta_datum.dietary_tags,
            :taste_profile => item_meta_datum.taste_profile,
            :texture => item_meta_datum.texture,
            :prep_methods => item_meta_datum.prep_methods,
            :meta_data_category => item_meta_datum.category,
            :meta_data_sub_category => item_meta_datum.sub_category,
          }
        )
      end
      data
    end
  end

  def get_chain_restaurant_count
    return restaurant_chain_count if restaurant_chain_count.present?
    return 1 unless chain

    chain.total_locations&.positive? ? chain.total_locations : chain.restaurants_count
  end

  def get_search_uniq_key
    return search_uniq_key if search_uniq_key.present?
    return if restaurant.nil?
    restaurant_identifier = if restaurant.chain_id.nil?
                              "restaurant##{restaurant.id}"
                            else
                              "chain##{restaurant.chain_id}"
                            end

    combined_string = "#{restaurant_identifier} #{name.to_s.strip.downcase}"
    combined_string[0, 255]
  end

  def es_index
    begin
      if searchable
        __elasticsearch__.index_document
      else
        __elasticsearch__.delete_document
      end
    rescue StandardError => error
      pp error.message
    end
  end

  class_methods do
    def search(query, search_options = {})
      # TODO: get only unique items by search_uniq_key

      search_definition = {
        track_total_hits: true,
        query: {
          bool: {
            must: [],
            should: [],
            must_not: [],
            filter: [] # { exists: { field: 'item_category_name' } }
          }
        },
        aggregations: {}
      }
      # QUERY ################################################################
      # Apply query
      search_definition[:query][:bool].merge!(query_definition(query, search_options[:user_search_options])) if query.present? && query != '*'

      # FILTERS ################################################################
      filters = search_options[:filters] || {}
      q_filters = filters[:q_filters] || []

      # Apply category filters
      if filters[:category].present? && filters[:category] != 'all'
        q_filters << case filters[:category]
                     when 'innovation'
                       { range: { innovation_score: { gte: 0.7 } } }
                     when 'brand', 'brands'
                       { exists: { field: 'brand_ids' } }
                     when 'ingredients'
                       { exists: { field: 'ingredient_ids' } }
                     when 'gpt_ingredients'
                       { exists: { field: 'gpt_ingredient_ids' } }
                     when 'images'
                       { term: { has_image: true } }
                     else
                       { term: { item_category_name: filters[:category] } }
                     end
      end

      # Apply additional filters
      q_filters << { terms: { brand_ids: [filters[:brand_id]] } } if filters[:brand_id].present?

      q_filters << { terms: { ingredient_ids: [filters[:ingredient_id]] } } if filters[:ingredient_id].present?

      q_filters << { terms: { region_ids: [filters[:region_id]] } } if filters[:region_id].present?

      q_filters << { terms: { menu_item_dashboard_ids: filters[:menu_item_dashboard_ids] } } if filters[:menu_item_dashboard_ids].present?

      q_filters << { terms: { "restaurant.id": [filters[:restaurant_id]].flatten } } if filters[:restaurant_id].present?

      q_filters << { terms: { "restaurant.chain_id": [filters[:chain_id]] } } if filters[:chain_id].present?

      q_filters << { terms: { city_id: filters[:city_ids] } } if filters[:city_ids].present?

      q_filters << { terms: { "restaurant.zip_code": [filters[:zip_code]] } } if filters[:zip_code].present?

      q_filters << { terms: { "menu.year": [filters[:year]] } } if filters[:year].present?

      q_filters << { terms: { "menu.quarter": [filters[:quarter]] } } if filters[:quarter].present?

      q_filters << { terms: { "menu.state": ["approved"] } }

      q_filters << { range: { restaurant_chain_count: { gte: filters[:restaurant_range].first, lte: filters[:restaurant_range].last } } } if filters[:restaurant_range].present?

      search_definition[:query][:bool][:filter].push(*q_filters)

      # AGGREGATIONS ################################################################
      # Apply aggregations

      if search_options[:top_categories].present?
        search_definition[:query][:bool][:must_not] << { term: { item_category_name: 'unknown' } }

        search_definition[:aggregations][:top_categories] = {
          terms: { field: 'item_category_name.keyword', size: 5 }
        }
      end

      # SORTING ################################################################
      # Apply sorting
      order_by = search_options[:order_by].to_a.first # { innovation_score: 'desc' } || { restaurant_chain_count: 'asc' }
      if order_by.present?
        search_definition[:sort] = [
          { order_by.first => { order: order_by.second || 'asc' } }
        ]
      end

      # DISTINCT
      # Only get distinct results by name
      if search_options[:distinct].present?
        search_definition[:collapse] = {
          field: "name.keyword"
        }
      end


      # PAGINATION ################################################################
      # Apply pagination
      paginate_params = search_options[:paginate_params]
      page = paginate_params&.dig(:page)
      per_page = paginate_params&.dig(:per_page)
      if page.present? || per_page.present?
        search_definition[:size] = per_page
        search_definition[:from] = (page.to_i - 1) * per_page.to_i if page&.to_i&.positive?
      end
      if ENV['SEARCH_LOGGING'].present?
        pp "--- ES SEARCH DEFINITION START ---"
        pp search_definition
        # pp "--- search_options ---"
        # pp search_options
        pp "--- END ---"
      end
      __elasticsearch__.search(search_definition)
    end

    # search_options = {
    #   filters: { category: 'brand', brand_id: 1, city_ids: [3, 4, 5], restaurant_range: (10..100) },
    #   order_by: { innovation_score: 'desc' },
    #   page: 1,
    #   per_page: 30
    # }
    # results = Item.search('apple').records
    # results = Item.search('pizza', filters)
    # results.records.each do |item|
    #   puts item.name
    # end

    # Item.search('*').results.total

    def with_both_ingredients(ingredients_a_name, ingredients_b_name, quarter: nil, year: nil, restaurant_ids: [])
      filters = [
        {
          "bool": {
            "should": [
              {
                "multi_match": {
                  "query": ingredients_a_name,
                  "fields": ["name^2", "description"],
                  "type": "phrase",
                  "slop": 0
                }
              },
              {
                "multi_match": {
                  "query": ingredients_b_name,
                  "fields": ["name^2", "description"],
                  "type": "phrase",
                  "slop": 0
                }
              },
              {
                "match": {
                  "quarter": quarter
                }
              },
              {
                "match": {
                  "year": year
                }
              },
            ],
            "minimum_should_match": 2
          }
        }
      ]
      # filters.push(
      #   {
      #     "match": {
      #       "restaurant.restaurant_type": restaurant_type
      #     }
      #   }
      # )
      query = {
        track_total_hits: true,
        query: {
          bool: {
            must: [],
            should: [],
            filter: filters
          }
        }
      }
      Item.__elasticsearch__.search(query)
    end

    def es_category_distribution(query: nil, limit: 5, quarter: TimePeriod.last.quarter, year: TimePeriod.last.year)

      should_matches = [
        {
          "multi_match": {
            "query": query,
            "fields": ["name^2", "description"],
            "type": "phrase",
            "slop": 0
          }
        },
        {
          "match": {
            "menus.quarter": quarter
          }
        },
        {
          "match": { "menus.year": year }
        }
      ]
      body = {
        size: 0,
        query: {
          "bool": {
            "should": should_matches
          }
        },
        aggs: {
          categories: {
            terms: {
              field: "restaurant.yelp_categories",
              size: limit,
              order: { _count: "desc" }
            }
          },
          total_items: {
            value_count: {
              field: "name.keyword"
            }
          }
        }
      }
      response = Item.__elasticsearch__.search(body)
      aggregations = response.response["aggregations"]
      total_items = aggregations['total_items']['value'].to_i + aggregations['categories']['sum_other_doc_count']
      aggregations['categories']['buckets'].map do |result|
        items_count = result['doc_count'].to_i
        percent = (items_count.to_f / total_items).to_f * 100
        {
          :name => result['key'],
          :items_count => items_count,
          :total_items_count => total_items,
          :percentage => percent.to_f.round(2)
        }
      end
    end

    def es_with_images(query: "", quarter: TimePeriod.last.quarter, year: TimePeriod.last.year, page: 1, per_page: 20)
      must_matches = [
        {
          "multi_match": {
            "query": query,
            "fields": ["name^2", "description"],
            "type": "phrase",
            "slop": 0
          }
        },
        {
          exists: { field: 'image_url' }
        }
      ]

      # should matches are or matches by default ... todo: should move to must match but seems too strict
      should_matches = [
        {
          "match": {
            "menus.quarter": quarter
          }
        },
        {
          "match": { "menus.year": year }
        },
        {
          "match": { "menus.state": "approved" }
        }
      ]
      must_not_matches = [
        {
          "match": {
            "image_url": "[nil]"
          }
        }
      ]
      body = {
        from: (page.to_i - 1) * per_page.to_i,
        size: per_page.to_i,
        query: {
          "bool": {
            "must": must_matches,
            "should": should_matches,
            "must_not": must_not_matches
          }
        },
        "collapse": {
          "field": "search_uniq_key"
        }
      }
      Item.__elasticsearch__.search(body)
    end

    def es_innovation_score(quarter: TimePeriod.last.quarter, year: TimePeriod.last.year, page: 1, per_page: 20)
      must_matches = [
        {
          exists: { field: 'innovation_score' }
        }
      ]

      should_matches = [
        {
          "match": {
            "menus.quarter": quarter
          }
        },
        {
          "match": { "menus.year": year }
        },
        {
          "match": { "menus.state": "approved" }
        },
        {
          exists: { field: 'image_url' }
        }
      ]
      must_not_matches = [
        {
          "match": {
            "image_url": "[nil]"
          }
        }
      ]
      body = {
        from: (page.to_i - 1) * per_page.to_i,
        size: per_page.to_i,
        query: {
          "bool": {
            "must": must_matches,
            "should": should_matches,
            "must_not": must_not_matches
          }
        },
        sort: [
          {
            "innovation_score": "desc"
          }
        ],
        "collapse": {
          "field": "search_uniq_key"
        }
      }
      Item.__elasticsearch__.search(body)
    end

    private

    def query_definition(query, user_search_options)
      exact_match = user_search_options&.[](:exact_match) == '1'
      all_words_present = user_search_options&.[](:some_words_present) != '1'
      include_substrings = user_search_options&.[](:include_substrings) == '1'

      must = []
      multi_match = {
        query: query,
        fields: %w[name^2 description],
        boost: 1.0
      }
      # Handle exact match by using phrase matching
      multi_match[:type] = 'phrase' if exact_match

      # Handle some words present by using match any
      multi_match[:operator] = 'and' if all_words_present

      # Handle include substrings by using wildcard matching
      must << if include_substrings
                { bool: { should: [
                  { multi_match: multi_match },
                  { wildcard: { "name.keyword" => "*#{query}*" } },
                  { wildcard: { "description.keyword" => "*#{query}*" } }
                ] } }
              else
                { multi_match: multi_match }
              end
      { must: must }
    end

  end
end
