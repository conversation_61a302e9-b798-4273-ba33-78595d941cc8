class RetailInsightsDashboard < ApplicationRecord
  include DashboardNewsFetchable

  belongs_to :ingredient

  has_many :quarters

  has_many :products, through: :quarters
  has_many :images, through: :products

  def init_quarters
    TimePeriod.last_year.each do |tp|
      quarters.find_or_create_by(time_period_id: tp.id, retail_insights_dashboard_id: id)
    end
  end

  def search_products(use_es = false)
    @products ||= if use_es
      Product.es_search(query, { exact_match: '1' })
    else
      Product.pg_search(query, { exact_match: '1' }).with_dashboard_enabled
    end
  end

  def assign_products
    init_quarters
    quarters.each do |q|
      Quarters::AssignProductsWorker.perform_async q.id
    end
  end

  def mentions(use_es = false)
    w = WidgetDatum.find_or_create_by(:widget_type => __method__, :retail_insights_dashboard_id => id)
    if w&.data.nil?
      w.data = set_mentions(use_es)
      w.save
    end
    w.data
  end

  def set_widget_datums
    ["mentions", "top_categories", "top_retailers", "top_brands"].each do |widget_data|
      w = WidgetDatum.find_or_create_by(:widget_type => widget_data, :retail_insights_dashboard_id => id)
      case widget_data
      when "mentions"
        w.data = set_mentions
      when "top_categories"
        w.data = set_top_categories
      when "top_retailers"
        w.data = set_top_retailers
      when "top_brands"
        w.data = set_top_brands
      else

      end
      w.save
    end
  end

  def set_mentions(use_es = false)
    mentions_by_quarter = Product
                            .from(search_products(use_es), :products)
                            .joins(:product_catalogs)
                            .select('count(DISTINCT(products.sku)) as count, concat(product_catalogs.year, \' Q\', product_catalogs.quarter) as quarter')
                            .group('product_catalogs.quarter, product_catalogs.year')
                            .order('product_catalogs.year, product_catalogs.quarter')

    counts = mentions_by_quarter.map(&:count)
    avg_growth = counts.length > 1 ? ((counts[-1] - counts[-2]).to_f / counts[-2] * 100).to_i : 0

    update(growth_percent: avg_growth) if avg_growth != growth_percent

    {
      total_count: mentions_by_quarter.sum(&:count),
      data: mentions_by_quarter.map { { name: _1.quarter, count: _1.count } },
      avg_growth:
    }
  end

  # todo: Verify with Sergei N if this is right
  def set_growth_percent
    self.growth_percent = mentions[:avg_growth].to_f.round(2)
    save
  end

  def top_categories(use_es = false)
    w = WidgetDatum.find_or_create_by(:widget_type => __method__, :retail_insights_dashboard_id => id)
    if w&.data.nil?
      w.data = set_top_categories(use_es)
      w.save

    end
    w.data
  end

  def set_top_categories(use_es = false)
    top_categories = Product
                       .from(search_products(use_es), :products)
                       .joins(:product_category)
                       .select('DISTINCT ON (product_categories.name) product_categories.name as name, COUNT(products.id) as count')
                       .group('product_categories.name')
                       .order('product_categories.name, count desc')
                       .limit(10)
                       .map { { name: _1.name, count: _1.count, id: _1.id } }
                       .sort_by { -_1[:count] }

    top_categories.map do |category|
      {
        name: category[:name],
        y: (category[:count].to_f / top_categories.sum { _1[:count] } * 100).round(2)
      }
    end
  end

  def top_brands(use_es = false)
    w = WidgetDatum.find_or_create_by(:widget_type => __method__, :retail_insights_dashboard_id => id)
    if w&.data.nil?
      w.data = set_top_brands(use_es)
      w.save
    end
    w.data
  end

  def set_top_brands(use_es = false)
    Product
      .from(search_products(use_es), :products)
      .select("count(DISTINCT(products.sku)) as count, LOWER(TRIM(brand)) as brand, AVG(price) as avg_price")
      .where.not(brand: nil)
      .group("LOWER(TRIM(brand))")
      .order("count desc")
      .limit(5)
      .map { { name: self.class.to_title_case(_1.brand), count: _1.count, price: _1.avg_price.to_f.round(2) } }
  end

  def top_retailers(use_es = false)
    w = WidgetDatum.find_or_create_by(:widget_type => __method__, :retail_insights_dashboard_id => id)
    if w&.data.nil?
      w.data = set_top_retailers
      w.save
    end
    w.data
  end

  def set_top_retailers(use_es = false)
    Product
      .from(search_products(use_es), :products)
      .joins(:retailer)
      .select('count(DISTINCT(products.sku)) as count, retailers.name as name')
      .group('retailers.name')
      .order('count desc')
      .limit(5)
      .map { { name: _1.name, count: _1.count } }
  end

  def top_retailers_by_banner_name(use_es = false)
    w = WidgetDatum.find_or_create_by(:widget_type => __method__, :retail_insights_dashboard_id => id)
    if w&.data.nil?
      w.data = set_top_retailers_by_banner_name
      w.save
    end
    w.data
  end

  def set_top_retailers_by_banner_name(use_es = false, limit = 5)
    Product
      .from(search_products(use_es), :products)
      .where.not(additional_fields: nil)
      .where.not("additional_fields->>'banner_name' IS NULL")
      .group("additional_fields->>'banner_name'")
      .select(
        "additional_fields->>'banner_name' AS name",
        "COUNT(DISTINCT products.sku) AS count",
        "AVG(products.price) AS avg_price"
      )
      .order("count DESC")
      .limit(limit)
      .map { { name: _1.name, count: _1.count, price: _1.avg_price.to_f.round(2) } }
  end

  def board_products(params)
    # Use the es_search method with exact matching
    result = Product.es_search(query, params.merge(exact_match: '1'))

    products = Product.where(id: result.map { _1['_id'] })
      .includes(:images, :retailer)
      .select('
        products.upc,
        products.name,
        products.id,
        products.price,
        products.created_at,
        product_categories.name as category,
        products.retailer_id,
        products.listing_url
      ')
      .joins(:product_category)
      .left_outer_joins(:images)

    {
      data: products,
      meta: {
        recordsTotal: result.response['hits']['total']['value']
      }
    }
  end

  def top_skus
    products.includes(:images, :retailer).limit(10)
  end

  def self.to_title_case(str)
    str&.split&.map(&:capitalize)&.join(' ')
  end
end
