class IngredientPairingTrend < ApplicationRecord
  include Quarterable
  include Filterable

  belongs_to :ingredient_a, class_name: "Ingredient"
  belongs_to :ingredient_b, class_name: "Ingredient"

  delegate :menu_categories, to: :ingredient_a, :prefix => true

  has_many :quarters, as: :quarterable

  validates :ingredient_a_id, uniqueness: { scope: :ingredient_b_id }

  before_save :set_name

  scope :filter_by_category, lambda { |category_names|
    where("category IN (:names)", names: [category_names].flatten)
  }

  def set_name
    return if ingredient_a.nil?
    return if ingredient_b.nil?
    self.name = "#{ingredient_a.name} - #{ingredient_b.name}"
  end

  def self.init_pairs
    Ingredient.is_pairing_trend.order(:name).find_each do |a|
      a.init_pairing_trends
    end
  end

  def calc_pairing_trend_count
    init_quarters
    quarters.each do |q|
      next if q.is_complete
      q.calc_pairing_trend_count
    end
  end

  def calc_pairing_count(quarter, year)
    # old w/o elastic search
    # menus = Menu.where(quarter: quarter, year: year).approved
    # name_a = ingredient_a.name
    # name_b = ingredient_b.name
    # items = Item.where(:menu_id => menus).with_all_ingredients("#{name_a} #{name_b}")
    # items.count
    Item.with_both_ingredients(ingredient_a.name, ingredient_b.name, quarter: quarter, year: year).total_count
  end

  def self.init_pairings_trends
    Ingredient.is_pairing_trend.each do |i|
      i.init_pairing_trends
    end
  end

  def self.calc_pairing_trend_counts
    IngredientPairingTrend.all.each do |pairing_trend|
      pairing_trend.calc_pairing_trend_count
    end
  end

  def self.update_percents
    IngredientPairingTrend.all.find_each do |pairing_trend|
      pairing_trend.quarters.each do |quarter|
        quarter.calc_pairing_trend_percents
      end
    end
  end

  def self.update_from_last_q!
    query = <<-SQL_QUERY
WITH LastQuarter AS (
                      SELECT DISTINCT ON (quarterable_id)
quarterable_id,
  pairing_trend_count,
  pairing_trend_percent,
  pairing_trend_change
FROM quarters
WHERE quarterable_type = 'IngredientPairingTrend'  -- Ensure correct polymorphic type
ORDER BY quarterable_id, time_period_id DESC  -- Prioritizes latest created_at per trend
)

UPDATE ingredient_pairing_trends ipt
SET
pairing_count = lq.pairing_trend_count,
  pairing_percent = lq.pairing_trend_percent,
  change = lq.pairing_trend_change
FROM LastQuarter lq
WHERE ipt.id = lq.quarterable_id;
    SQL_QUERY

    ActiveRecord::Base.connection.execute(query)
  end

  # also gets rid of duplicate entries
  def self.ingredient_pairing_trends_table
    query = <<-SQL_QUERY
i1.guid as ingredient_a_guid,
i2.guid as ingredient_b_guid,
i1.name as ingredient_a_name,
i2.name as ingredient_b_name,
category,
CONCAT(i1.name, ' - ', i2.name) as name,
MAX(pairing_percent) as pairing_percent,
MAX(change) as change
    SQL_QUERY
    join_query_1 = "JOIN ingredients i1 ON i1.id = LEAST(CAST(ingredient_a_id AS INTEGER) , CAST(ingredient_b_id AS INTEGER))"
    join_query_2 = "JOIN ingredients i2 ON i2.id = GREATEST(CAST(ingredient_a_id AS INTEGER) , CAST(ingredient_b_id AS INTEGER))"
    IngredientPairingTrend.select(query).joins(join_query_1).joins(join_query_2).group("i1.name, i2.name, i1.guid, i2.guid, category")
  end

end
