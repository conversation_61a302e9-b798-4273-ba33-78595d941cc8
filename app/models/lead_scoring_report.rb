class LeadScoringReport < ApplicationRecord

  belongs_to :restaurant
  belongs_to :menu
  belongs_to :customer

  has_many :lead_scoring_report_items

  validates :guid, :uniqueness => true, :allow_nil => true

  validates :menu_id, uniqueness: { scope: :customer_id }

  delegate :price, :to => :restaurant, :prefix => true, :allow_nil => true
  delegate :yelp_categories, :to => :restaurant, :prefix => true, :allow_nil => true

  delegate :menu_survey, :to => :menu, :allow_nil => true

  scope :approved, -> { joins(:menu).where(menus: { state: "approved" }).distinct }

  before_validation :set_type_for_customer


  BLACKLIST_WORDS = ["salad"]

  ENTREE_SCORES = [
    [0,0,0,5],
    [0,0,10,20],
    [0,10,40,50],
    [0,20,50,80],
    [20,30,50,75],
    [30,50,80,100],
    [10,50,80,100],
    [10,50,80,100],
    [20,40,60,100],
  ]

  APPETIZER_SCORES = [
    [0,0,0,5],
    [0,0,5,10],
    [0,5,10,15],
    [0,10,15,20],
    [10,15,20,25],
    [15,20,30,35],
    [5,15,20,25],
    [5,15,20,25],
    [10,20,25,35]
  ]

  SUSHI_SCORES = [
    [0,0,0,5],
    [0,0,5,10],
    [0,5,10,15],
    [0,10,15,20],
    [10,15,20,25],
    [10,15,20,35],
    [10,15,20,25],
    [10,15,20,25],
  ]


  after_create do
    set_guid
  end

  def compute_score
    raise StandardError.new("Customer required.") if customer.nil?
    raise StandardError.new("Menu required.") if menu.nil?
    if restaurant.nil?
      self.restaurant_id = menu.restaurant&.id
      save
    end

    menu.items.each do |item|
      score = check_score(item, item.detect_category)
      report_item = LeadScoringReportItem.find_or_create_by(:item_id => item.id, :lead_scoring_report_id => id)
      report_item.course_category = item.detect_category
      report_item.restaurant_id = item.restaurant&.id

      found_blacklist = check_blacklist(item)
      if found_blacklist.present?
        report_item.blacklist_names = found_blacklist
        report_item.is_blacklist = true
        report_item.score = 0
      else
        report_item.blacklist_names = []
        report_item.is_blacklist = false
        report_item.score = score
      end
      report_item.save!
    end

    self.subtotal = lead_scoring_report_items.map{|report_item|report_item.score.to_f}.sum
    add_bonus
    self.total_score = subtotal + bonus

    menu.create_menu_survey
    save!
    generate_report_csv
  end

  def add_bonus
  end


  COURSE_CATEGORY_HASH = {
    "entree": "entrees",
    "appetizer": "apps",
    "sushi": "sushi"
  }

  def check_score(item, course_category="entrees")
    score = 0

    return if course_category.nil?
    mapped_category = COURSE_CATEGORY_HASH[course_category.to_sym]

    return if mapped_category.nil?

    item_name = item.name.to_s.downcase.strip
    item_description = item.description.to_s.downcase.strip

    LeadItem.send(mapped_category).each do |l|

      score_name = l.name.to_s.downcase.strip
      if item_name.match(/#{score_name}/) || item_description.match(/#{score_name}/)
        ItemLeadItem.find_or_create_by(:item_id => item.id, :lead_item_id => l.id)
        if mapped_category == "entrees"
          if item.price.to_f > 0 && item.price.to_f <= 20
            score += l.zero_to_20
          elsif item.price.to_f > 20 && item.price.to_f <= 30
            score += l.twenty_to_30
          elsif item.price.to_f > 30 && item.price.to_f <= 40
            score += l.thirty_to_40
          elsif item.price.to_f > 40
            score += l.over_40
          elsif item.price.blank?
            # lookup score for restaurant $ sign
            res = item.restaurant
            if l.color_weight.present? && res.price.present?
              score += lookup_score_for_weight(l.color_weight, mapped_category, res.price)
            end
          end
        elsif mapped_category == "apps" || mapped_category == "sushi"
          if item.price.to_f > 0 && item.price.to_f <= 10
            score += l.zero_to_10
          elsif item.price.to_f > 10 && item.price.to_f <= 20
            score += l.ten_to_20
          elsif item.price.to_f > 20 && item.price.to_f <= 30
            score += l.twenty_to_30
          elsif item.price.to_f > 30
            score += l.over_30
          elsif item.price.blank?
            # lookup score for restaurant $ sign
            res = item.restaurant
            if l.color_weight.present? && res.price.present?
              score += lookup_score_for_weight(l.color_weight, mapped_category, res.price)
            end
          end
        end
      end
    end
    score
  end

  def lookup_score_for_weight(color_weight, mapped_category, restaurant_price)
    color_index = color_weight - 1
    scores = table_for_category(mapped_category)
    score_row = scores[color_index]
    score_index = index_for_dollar_sign(restaurant_price)
    score_row[score_index]
  end

  def table_for_category(mapped_category)
    case mapped_category
    when "entrees"
      ENTREE_SCORES
    when "apps"
      APPETIZER_SCORES
    else
      SUSHI_SCORES
    end
  end

  # index can be 0,1,2,3
  def index_for_dollar_sign(restaurant_price)
    case restaurant_price
    when "$"
      0
    when "$$"
      1
    when "$$$"
      2
    when "$$$$"
      3
    when "$$$$$"
      3
    else
      0
    end
  end

  def generate_report_csv
    headers = %w[business_name hf_id net_yield_code_c type menu_category menu_item menu_description menu_price found score ]
    customers = restaurant.hf_customers
    customer_business_name = customers.pluck(:business_name).join(", ")
    business_name = customer_business_name.present? ? customer_business_name : restaurant.business_name
    customer_ids = customers.pluck(:customer_id).join(", ")
    net_yield_code_c = customers.pluck(:net_yield_code_c).join(", ")
    data = lead_scoring_report_items.order('id').map do |ri|
      [
         business_name,
         customer_ids,
         net_yield_code_c,
         ri.item&.detect_category,
         ri.item&.category,
         ri.item&.name,
         ri.item&.description,
         ri.item&.price&.to_f&.round(2),
         ri.item&.found_lead_items,
         ri.score
      ]
    end
    report_url = CsvExportService.new(name: "lead-scoring-report-#{SecureRandom.hex(5)}", headers: headers, data: data ).call
    self.report_url = report_url
    save
  end

  def self.recompute_all
    all.find_each do |l|
      LeadScoringReports::RecomputeScoreWorker.perform_async l.id
    end
  end

  def self.delete_duplicates
    results = LeadScoringReport.group('menu_id').having("COUNT(*) > 1").select('array_agg("id") as dupe_ids')
    results.map &:dupe_ids
    results.each do |r|
      dupe_ids = r.dupe_ids
      dupe_ids.shift
      LeadScoringReport.where(:id => dupe_ids).destroy_all
    end
  end

  def post_to_ftp
    return if report_url.blank?
    hf_helper = HfHelper.new
    hf_helper.upload(report_url)
  end


  # todo: rename Honolulu Fish Only
  def self.generate_lead_scoring_report
    return if Customer.hf.nil?
    begin
      file_name = "#{SecureRandom.hex(10)}_restaurant_scores.csv"
      tempfile = Tempfile.new('foo')

      CSV.open(tempfile, "wb") do |csv|
        csv << %w[id net_yield_code_c score link]

        restaurant_ids = LeadScoringReport.where(customer_id: Customer.hf).pluck(:restaurant_id).uniq
        hf_customers = HfCustomer.where(:restaurant_id => restaurant_ids).where.not(:net_yield_code_c => nil)
        hf_customers.joins(:restaurant).find_each do |h|
          next if h.lead_scoring_report.nil?
          l = h.lead_scoring_report
          menu = l.menu
          next if menu.nil?
          next if menu.state.to_s != "approved"
          next if menu.items.count.zero?

          menu_survey = l.menu_survey
          csv << [h.customer_id,
                  h.net_yield_code_c,
                  l.total_score,
                  l.link_url]
        end
      end

      aws_manager = AwsS3Manager.new
      obj = aws_manager.store_file(file_name, tempfile.read)
      CloudfrontHelper.signed_url(obj.public_url)
    rescue StandardError => error
      pp error.message
    end
  end

  def self.generate_lead_scoring_report_v2
    return if Customer.hf.nil?
    begin
      file_name = "#{SecureRandom.hex(10)}_restaurant_scoresV2.csv"
      tempfile = Tempfile.new('foo')

      CSV.open(tempfile, "wb") do |csv|
        csv << %w[id net_yield_code_c score link entree_high_price entree_low_price tuna_entree_price tuna_appetizer_price salmon_entree_price scallop_entree_price fish_appetizers_count fish_entrees_count is_chicken is_foi_gras is_ahi survey_date]

        restaurant_ids = LeadScoringReport.where(customer_id: Customer.hf).pluck(:restaurant_id).uniq
        hf_customers = HfCustomer.where(:restaurant_id => restaurant_ids).where.not(:net_yield_code_c => nil)
        hf_customers.joins(:restaurant).find_each do |h|
          next if h.lead_scoring_report.nil?
          l = h.lead_scoring_report
          menu = l.menu
          next if menu.nil?
          next if menu.state.to_s != "approved"
          next if menu.items.count.zero?

          menu_survey = l.menu_survey
          csv << [h.customer_id,
                  h.net_yield_code_c,
                  l.total_score,
                  l.link_url,
                  menu_survey&.entree_high_price&.to_f&.round(2),
                  menu_survey&.entree_low_price&.to_f&.round(2),
                  menu_survey&.tuna_entree_price&.to_f&.round(2),
                  menu_survey&.tuna_appetizer_price&.to_f&.round(2),
                  menu_survey&.salmon_entree_price&.to_f&.round(2),
                  menu_survey&.scallop_entree_price&.to_f&.round(2),
                  menu_survey&.fish_appetizers_count&.to_f&.round(2),
                  menu_survey&.fish_entrees_count&.to_f&.round(2),
                  menu_survey&.is_chicken,
                  menu_survey&.is_foi_gras,
                  menu_survey&.is_ahi,
                  menu_survey&.updated_at&.strftime("%m/%d/%Y")
          ]
        end
      end

      aws_manager = AwsS3Manager.new
      obj = aws_manager.store_file(file_name, tempfile.read)
      CloudfrontHelper.signed_url(obj.public_url)
    rescue StandardError => error
      pp error.message
    end
  end

  def self.post_lead_scoring_report
    report_url = LeadScoringReport.generate_lead_scoring_report
    return if report_url.blank?
    hf_helper = HfHelper.new
    upload_name = Rails.env.production? ? "restaurant_scores.csv" : "restaurant_scores_dev.csv"
    hf_helper.upload(report_url, upload_name: upload_name)
  end

  def self.post_lead_scoring_report_v2
    report_url = LeadScoringReport.generate_lead_scoring_report_v2
    return if report_url.blank?
    hf_helper = HfHelper.new
    upload_name = Rails.env.production? ? "restaurant_scoresV2.csv" : "restaurant_scores_devV2.csv"
    hf_helper.upload(report_url, upload_name: upload_name)
  end

  def set_guid
    return if guid.present?
    self.guid = SecureRandom.uuid
    self.save
  end

  def link_url
    "#{ENV['HOST_URL']}/lead_scoring_reports/#{guid}"
  end

  def check_blacklist(item)
    item_name = item.name.to_s.downcase.strip
    item_description = item.description.to_s.downcase.strip
    found_blacklist = []
    BLACKLIST_WORDS.each do |blacklist_word|
      if item_name.match(/#{blacklist_word}/) || item_description.match(/#{blacklist_word}/)
        found_blacklist.push(blacklist_word)
      end
    end
    found_blacklist
  end

  def set_type_for_customer
    return if customer.nil?
    case customer.name.to_s.downcase.strip
    when "marukan"
      self.type = "LeadScoringReport::Marukan"
    else
      self.type = (type.to_s === "LeadScoringReport::Basic") ? "LeadScoringReport::Basic" : "LeadScoringReport"
    end
  end

  def is_type?(klass)
    type == klass
  end

end
