module CustomerInsights
  extend ActiveSupport::Concern

  included do
    def self.most_talked_about(page: 1, per_page: 5, sort: nil, search: nil)
      base_query = select('
        distinct ingredients.id,
        ingredients.guid,
        ingredients.name,
        ingredients.pen_rate,
        ingredients.pen_rate as penetration,
        menu_item_dashboards.positive_percentage,
        menu_item_dashboards.negative_percentage,
        menu_item_dashboards.neutral_percentage,
        ingredients.change_1y,
        ingredients.change_1y as foodservice_growth,
        FIRST_VALUE(menu_categories.name) OVER(partition by ingredients.name) as menu_category_name,
        COALESCE(menu_item_dashboards.yearly_growth, 0) as social_mentions,
        COALESCE(retail_insights_dashboards.growth_percent, 0) as retail_change
      ')
      .joins(:menu_item_dashboard)
      .joins(:menu_categories)
      .left_joins(:retail_insights_dashboard)

      if search.present?
        base_query = base_query.where('ingredients.name ILIKE ?', "%#{search}%")
      end

      if sort.present?
        field, order_direction = parse_sort_param(sort)
        if field && order_direction
          base_query = base_query.order("#{field} #{order_direction} NULLS LAST")
        end
      else
        base_query = base_query.order('ingredients.pen_rate DESC NULLS LAST')
      end

      paginated = base_query.limit(per_page).offset(((page || 1).to_i - 1) * per_page.to_i)
      total_count = base_query.except(:limit, :offset, :order).count(:all)

      [paginated, total_count]
    end

    def self.most_liked(page: 1, per_page: 5, sort: nil, search: nil)
      base_query =
        joins('LEFT JOIN consumer_sentiment_likes ON consumer_sentiment_likes.ingredient_id = ingredients.id AND consumer_sentiment_likes.score IN (4, 5)')
        .joins(:menu_item_dashboard)
        .joins(:menu_categories)
        .left_joins(:retail_insights_dashboard)
        .group('ingredients.id, ingredients.guid, ingredients.name, ingredients.pen_rate, ingredients.menu_adoption, menu_item_dashboards.positive_percentage, menu_item_dashboards.negative_percentage, menu_item_dashboards.neutral_percentage, ingredients.change_1y, ingredients.change_1y, menu_categories.name, menu_item_dashboards.yearly_growth, retail_insights_dashboards.growth_percent')
        .select('
          distinct ingredients.id,
          ingredients.guid,
          ingredients.name,
          ingredients.pen_rate,
          ingredients.pen_rate as penetration,
          ingredients.menu_adoption,
          menu_item_dashboards.positive_percentage,
          menu_item_dashboards.negative_percentage,
          menu_item_dashboards.neutral_percentage,
          ingredients.change_1y,
          ingredients.change_1y as foodservice_growth,
          FIRST_VALUE(menu_categories.name) OVER(partition by ingredients.name) as menu_category_name,
          COALESCE(menu_item_dashboards.yearly_growth, 0) as social_mentions,
          COALESCE(retail_insights_dashboards.growth_percent, 0) as retail_change,
          count(consumer_sentiment_likes.id) as likes_count
        ')

      if search.present?
        base_query = base_query.where('ingredients.name ILIKE ?', "%#{search}%")
      end

      if sort.present?
        field, order_direction = parse_sort_param(sort)
        if field && order_direction
          base_query = base_query.order("#{field} #{order_direction} NULLS LAST")
        end
      else
        base_query = base_query.order('likes_count DESC NULLS LAST')
      end

      paginated = base_query.limit(per_page).offset(((page || 1).to_i - 1) * per_page.to_i)

      total_count = base_query.except(:limit, :offset, :order).length

      [paginated, total_count]
    end
  end
end
