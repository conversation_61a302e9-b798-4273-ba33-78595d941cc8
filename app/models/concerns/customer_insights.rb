module CustomerInsights
  extend ActiveSupport::Concern

  included do
    def self.most_talked_about(page = 1, per_page = 5)
      base_query = select('
        distinct ingredients.id,
        ingredients.guid,
        ingredients.name,
        ingredients.pen_rate,
        menu_item_dashboards.positive_percentage,
        menu_item_dashboards.negative_percentage,
        menu_item_dashboards.neutral_percentage,
        ingredients.change_1y,
        FIRST_VALUE(menu_categories.name) OVER(partition by ingredients.name) as menu_category_name,
        COALESCE(menu_item_dashboards.yearly_growth, 0) as social_mentions,
        COALESCE(retail_insights_dashboards.growth_percent, 0) as retail_change
      ')
      .joins(:menu_item_dashboard)
      .joins(:menu_categories)
      .left_joins(:retail_insights_dashboard)
      .order('ingredients.pen_rate DESC NULLS LAST')

      paginated = base_query.limit(per_page).offset(((page || 1).to_i - 1) * per_page.to_i)
      total_count = base_query.except(:limit, :offset, :order).count(:all)

      [paginated, total_count]
    end
  end
end
