module ProductSearchable
  extend ActiveSupport::Concern

  included do
    include Elasticsearch::Model
    include ProductElasticsearchAggregations
    after_save :es_index

    settings index: { number_of_shards: 1 } do
      mappings dynamic: 'false' do
        indexes :name, type: 'text'
        indexes :upc, type: 'keyword'
        indexes :price, type: 'float'
        indexes :created_at, type: 'date'
        indexes :retailer_id, type: 'text'
        indexes :retailer_name, type: 'keyword'
        indexes :brand, type: 'keyword'
        indexes :l0, type: 'keyword'
        indexes :l1, type: 'keyword'
        indexes :l2, type: 'keyword'
        indexes :uom, type: 'keyword'
        indexes :pack_size, type: 'keyword'
        indexes :sku, type: 'keyword'
        indexes :listing_url, type: 'keyword'
        indexes :week_date, type: 'date'
        indexes :composition, type: 'text'
        indexes :composition, type: 'text'
        indexes :year, type: 'integer'
        indexes :quarter, type: 'integer'
        indexes :store_id, type: 'integer'
        indexes :product_catalogs, type: 'nested' do
          indexes :year, type: 'integer'
          indexes :quarter, type: 'integer'
        end
        indexes :product_categories, type: 'nested' do
          indexes :name, type: 'text'
        end
        indexes :retailer, type: 'nested' do
          indexes :name, type: 'text'
          indexes :dashboard_enabled, type: 'boolean'
        end
        indexes :images, type: 'nested' do
          indexes :url, type: 'text'
        end
        indexes :additional_fields, type: 'object', dynamic: 'true' do
          indexes :banner_name, type: 'keyword'
        end
      end
    end

    def as_indexed_json(options = {})
      {
        name: name,
        upc: upc,
        price: price,
        created_at: created_at,
        retailer_id: retailer_id,
        retailer_name: retailer&.name,
        product_catalogs: product_catalogs.map { { year: _1.year, quarter: _1.quarter } },
        product_categories: product_category.as_json,
        week_date: week_date&.to_date || Date.parse("2024-06-30"),
        retailer: retailer.as_json,
        images: images.map(&:as_json),
        brand: brand,
        l0: l0,
        l1: l1,
        l2: l2,
        uom: uom,
        pack_size: pack_size,
        sku: sku,
        additional_fields: additional_fields,
        listing_url: listing_url,
        composition: composition,
        year: year,
        quarter: quarter
      }
    end

    def self.es_search(q, params)
      query = {
        query: {
          bool: {
            must: [],
            should: [],
            filter: [
              {
                nested: {
                  path: 'retailer',
                  query: {
                    bool: {
                      must: [{ term: { 'retailer.dashboard_enabled' => true } }]
                    }
                  }
                }
              }
            ]
          }
        },
        sort: [
          { (params['order_column'].presence || params[:sort]&.dig(:column) || 'created_at') => { order: params['order_dir'].presence || params[:sort]&.dig(:direction) || 'desc' } }
        ],
        size: params[:per_page] || 10,
        from: (params[:page].to_i - 1) * (params[:per_page].to_i || 10)
      }

      if params[:l_category_exist].present?
        query[:query][:bool][:filter] << { exists: { field: (params[:l_category_exist]) } }
      end

      if params[:sku].present?
        query[:query][:bool][:filter] << { exists: { field: 'sku' } }
      end

      if q.present?
        if params[:exact_match] == '1'
          query[:query][:bool][:must] << { match_phrase: { name: { query: q, slop: 0 } }}
        else
          query[:query][:bool][:must] << { match: { name: q } }
        end
      end

      if params[:category_name].present?
        query[:query][:bool][:must] << { nested: { path: 'product_categories', query: { match: { 'product_categories.name': params[:category_name] } } } }
      end

      if params[:brand_id].present?
        query[:query][:bool][:must] << { match: { brand: params[:brand_id] } }
      end

      if params[:year].present? && params[:quarter].present?
        query[:query][:bool][:must] << { nested: { path: 'product_catalogs', query: { match: { 'product_catalogs.year': params[:year] } } } }
        query[:query][:bool][:must] << { nested: { path: 'product_catalogs', query: { match: { 'product_catalogs.quarter': params[:quarter] } } } }
      end

      if params[:retailer_name].present?
        query[:query][:bool][:must] << { match: { 'retailer_name': params[:retailer_name] } }
      end

      if params[:search].present?
        query[:query][:bool][:must] << { match: { name: params[:search] } }
      end

      Product.__elasticsearch__.search(query)
    end

    def es_index
      # begin
      #   if searchable
      #     __elasticsearch__.index_document
      #   else
      #     __elasticsearch__.delete_document
      #   end
      # rescue StandardError => error
      #   pp error.message
      # end
    end
  end
end
