module ProductElasticsearchAggregations
  extend ActiveSupport::Concern

  SEARCH_FIELDS = ["name^2", "description"].freeze
  DEFAULT_OPERATOR = "AND".freeze
  ANALYZER = "simple".freeze

  class_methods do
    def top_retailers_by_banner_name_es(name:, limit: 5)
      search_query = {
        size: 0,
        query: {
          bool: {
            must: [],
            filter: [
              { exists: { field: "sku" } },
              { exists: { field: "additional_fields.banner_name" } }
            ]
          }
        },
        aggs: {
          by_banner_name: {
            terms: {
              field: "additional_fields.banner_name",
              size: limit
            },
            aggs: {
              unique_skus: {
                cardinality: {
                  field: "sku"
                }
              },
              avg_price: {
                avg: {
                  field: "price"
                }
              }
            }
          }
        }
      }

      add_name_filter(search_query, name)

      response = __elasticsearch__.search(search_query)
      response.aggregations["by_banner_name"]["buckets"]
    end

    def top_manufacturers_es(name: nil, limit: 5)
      query = {
        query: {
          bool: {
            must: [],
            filter: [
              { exists: { field: 'sku' } },
              { exists: { field: 'price' } },
              { exists: { field: 'brand' } }
            ]
          }
        },
        size: 0,
        aggs: {
          by_brand: {
            terms: {
              field: 'brand',
              size: limit,
              order: { unique_sku_count: 'desc' }
            },
            aggs: {
              unique_sku_count: {
                cardinality: {
                  field: 'sku'
                }
              },
              avg_price: {
                avg: {
                  field: 'price'
                }
              }
            }
          }
        }
      }

      add_name_filter(query, name)

      result = Product.__elasticsearch__.search(query)
      result.response.dig('aggregations', 'by_brand', 'buckets')
    end

    def top_categories_chart_data_es(name:, limit: 7, level: 'l1', store_ids: nil)
      query = {
        query: {
          bool: {
            must: [],
            filter: [
              { exists: { field: 'sku' } },
              { exists: { field: level } }
            ]
          }
        },
        size: 0,
        aggs: {
          by_category: {
            terms: {
              field: level,
              size: limit,
              order: { _count: 'desc' }
            },
            aggs: {
              unique_skus: {
                cardinality: {
                  field: 'sku'
                }
              }
            }
          }
        }
      }

      add_name_filter(query, name)

      query[:query][:bool][:filter] << { terms: { store_id: Array(store_ids) } } if store_ids.present?

      result = Product.__elasticsearch__.search(query)
      result.response.dig('aggregations', 'by_category', 'buckets')
    end

    def top_l2_growth_by_ingredient_es(name: nil, limit: 6, store_ids: [], quarter: nil)
      current_year = Time.now.year
      previous_year = current_year - 1
      years = [previous_year, current_year]

      # Determine default quarter if not provided
      quarter_equal = "q#{Time.now.quarter}".to_sym if quarter.nil?

      query = {
        query: {
          bool: {
            must: [],
            filter: [
              { exists: { field: "sku" } },
              { exists: { field: "l2" } },
              { terms: { year: years } },
              { bool: { must_not: { term: { l2: "other" } } } }
            ]
          }
        },
        size: 0,
        aggs: {
          by_year: {
            terms: { field: "year" },
            aggs: {
              by_quarter: {
                terms: { field: "quarter" },
                aggs: {
                  by_l2: {
                    terms: { field: "l2", size: 100 },
                    aggs: {
                      unique_skus: { cardinality: { field: "sku" } },
                      product_count: { value_count: { field: "sku" } }
                    }
                  }
                }
              }
            }
          }
        }
      }

      add_name_filter(query, name)

      if store_ids&.any?
        query[:query][:bool][:filter] << { terms: { store_id: store_ids } }
      end

      result = Product.__elasticsearch__.search(query)
      buckets = result.response.dig("aggregations", "by_year", "buckets")

      growth_data = {}
      buckets.each do |year_bucket|
        year = year_bucket["key"]
        year_bucket.dig("by_quarter", "buckets").each do |quarter_bucket|
          quarter_num = quarter_bucket["key"]
          quarter_key = "#{year}_q#{quarter_num}"
          quarter_bucket.dig("by_l2", "buckets").each do |l2_bucket|
            category = l2_bucket["key"]
            product_count = l2_bucket.dig("product_count", "value")
            growth_data[category] ||= {}
            growth_data[category][quarter_key] = { value: product_count }
          end
        end
      end

      # binding.pry
      # Save a copy of original growth_data
      original_growth_data = growth_data.dup

      # Filter categories based on product count in the specified or current quarter
      # and ensure categories have significant product counts
      if quarter.present? || quarter_equal
        target_quarter = quarter || quarter_equal
        growth_data.select! do |category, quarters|
          has_significant_count = quarters.any? { |_, data| data[:value] >= 100 }
          target_quarter_filter = quarters.none? do |quarter_key, data|
            quarter_key.match?("_#{target_quarter}") &&
              data[:value].positive? &&
              data[:value] < (quarter.present? ? 100 : 10)
          end
          has_significant_count && target_quarter_filter
        end

        # If growth_data is empty, restore categories that were filtered out
        if growth_data.empty?
          growth_data = original_growth_data
        end
      end

      result = growth_data.map do |category, quarters|
        sorted_quarters = quarters.keys.sort_by { |q| match = q.match(/(\d+)_q(\d+)/); [match[1].to_i, match[2].to_i] }
        quarters_data = {}
        previous_count = 0
        all_percents = []

        sorted_quarters.each do |quarter|
          value = quarters[quarter][:value]
          percent = previous_count.zero? ? 0.0 : ((value - previous_count) * 100.0 / previous_count).round(2)
          quarters_data[quarter] = { value: value, percent: percent, previous_count: previous_count }
          all_percents << percent if !previous_count.zero? && percent > 0
          previous_count = value
        end

        average_growth = if all_percents.any?
                           max_growth = all_percents.max
                           other_percents = all_percents - [max_growth]
                           other_avg = other_percents.any? ? other_percents.sum / other_percents.size : 0.0
                           (0.5 * max_growth + 0.5 * other_avg).round(2)
                         else
                           0.0
                         end

        {
          category_name: category.titleize,
          quarters: quarters_data,
          average_growth: average_growth
        }
      end

      result = result.reject { |entry| entry[:average_growth] == 0.0 }
      result.sort_by { |item| -item[:average_growth] }.first(limit)
    end

    def retail_growth_es(name: nil, unique_skus: true, growth_prediction: nil)
      current_year = Time.now.year
      years = [current_year - 1, current_year]

      agg = if unique_skus
              { unique_skus: { cardinality: { field: "sku" } } }
            else
              { product_count: { value_count: { field: "sku" } } }
            end

      query = {
        query: {
          bool: {
            must: [],
            filter: [{ exists: { field: "sku" } }, { terms: { year: years } }]
          }
        },
        size: 0,
        aggs: {
          by_year: {
            terms: { field: "year" },
            aggs: {
              by_quarter: {
                terms: { field: "quarter" },
                aggs: agg
              }
            }
          }
        }
      }

      add_name_filter(query, name)

      result = Product.__elasticsearch__.search(query)
      buckets = result.response.dig("aggregations", "by_year", "buckets")

      data = {}
      buckets.each do |year_bucket|
        year = year_bucket["key"]
        year_bucket.dig("by_quarter", "buckets").each do |quarter_bucket|
          quarter_num = quarter_bucket["key"]
          quarter_name = "Q#{quarter_num}"
          count = if unique_skus
                    quarter_bucket.dig("unique_skus", "value") || 0
                  else
                    quarter_bucket.dig("product_count", "value") || 0
                  end
          data[year] ||= {}
          data[year][quarter_name] = count unless count.zero?
        end
      end

      quarters_with_data = []
      data.each do |year, quarters|
        quarters.each do |quarter, count|
          quarters_with_data << { name: "#{quarter} #{year}", count: count } unless count.zero?
        end
      end
      quarters_with_data.sort_by! { |q| [q[:name].split.last.to_i, q[:name].split.first[1].to_i] }

      avg_growth = if quarters_with_data.length >= 2
                     first_count = quarters_with_data.first[:count].to_f
                     last_count = quarters_with_data.last[:count].to_f
                     ((last_count - first_count) / first_count * 100).round(2)
                   else
                     0
                   end

      {
        total_count: quarters_with_data.sum { |q| q[:count] },
        quarters: quarters_with_data,
        growth_percent: avg_growth,
        growth_prediction: growth_prediction
      }
    end

    private

    def add_name_filter(query, name)
      return unless name.present?

      query[:query][:bool][:must] << {
        query_string: {
          query: escape_query(name),
          fields: SEARCH_FIELDS,
          default_operator: DEFAULT_OPERATOR,
          analyzer: ANALYZER
        }
      }
    end

    def escape_query(value)
      value.to_s.gsub(/([+-\=&|!(){}\[\]^"~*?:\\\/])/) { "\\#{$1}" }
    end
  end
end