class User < ApplicationRecord
  rolify
  include Filterable
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :validatable, :jwt_authenticatable, jwt_revocation_strategy: JwtDenylist

  validates :email, presence: true
  validates :password, presence: { :on => :create }
  validates :password, confirmation: true

  after_create :set_customer_role
  belongs_to :customer

  has_many :approved_menus, class_name: 'Menu', primary_key: 'email', foreign_key: 'approved_by'
  has_many :saved_reports, class_name: 'Report::Saved'

  has_many :chats

  scope :filter_by_customer_id, ->(customer_id) { where(customer_id: customer_id) }

  has_many :favorites
  has_many :favorite_ingredients, through: :favorites, source: :ingredient

  def set_customer_role
    return unless customer_id.present?

    add_role :customer

  end

  def admin?
    has_cached_role?(:admin)
  end

  def customer?
    has_cached_role?(:customer)
  end

  def demo?
    has_cached_role?(:demo)
  end

  def is_dole?
    customer&.name&.to_s&.downcase == "dole"
  end

  def access?(feature)
    return true if admin?

    feature_to_key_mapping = {
      'Report' => 'reports',
      'Content' => 'contents',
      'Retailer' => 'grocery',
      'MenuItemDashboard' => 'dashboards',
      'LeadScoringReport' => 'lead_scoring_reports'
    }
    key = feature_to_key_mapping[feature.to_s] || feature

    return customer&.access_features&.include?(key) if Customer::ACCESS_FEATURE_OPTIONS.include?(key)

    false
  end

  def access_region?(region_id)
    customer ? customer.access_region?(region_id) : true
  end

  def create_new_jwt_token
    scope = :user
    payload = Warden::JWTAuth::UserEncoder.new.call(self, scope, nil)
    payload[0]
  end

  def self.decode_jwt_token(token)
    jwt_payload = JWT.decode(token, ENV['DEVISE_JWT_SECRET_KEY']).first
    if JwtDenylist.where(:jti => jwt_payload['jti']).count > 0
      raise JWT::VerificationError
    end
    user_id = jwt_payload['sub']
    user_id
  rescue JWT::VerificationError => error
    Rails.logger.error("Invalid Token:#{error.message}")
  end

  def self.ban_token(token)
    jwt_payload = JWT.decode(token, ENV['DEVISE_JWT_SECRET_KEY'], true).first
    jti = jwt_payload['jti']
    exp = Time.at(jwt_payload['exp'])
    JwtDenylist.create!(jti: jti, exp: exp)
  end

end
