class Restaurant < ApplicationRecord
  include PgSearch::Model
  include RestaurantSearchable
  # include Elasticsearch::Model
  # include Elasticsearch::Model::Callbacks

  include Filterable
  include ScraperUrlsHelper

  belongs_to :city
  has_many :regions, through: :city
  belongs_to :chain, counter_cache: true
  # belongs_to :zip_code # TODO

  has_one :county, :through => :city
  has_one :state, :through => :city

  has_many :menus, dependent: :destroy
  has_many :restaurant_categories
  has_many :categories, :through => :restaurant_categories

  has_many :restaurant_projects
  has_many :projects, :through => :restaurant_projects

  has_many :items, :through => :menus
  has_many :normalized_items, :through => :items
  has_many :item_categories, :through => :normalized_items

  has_many :scrapper_results, -> { where.not(scrapper_type: 'location') }, class_name: 'ScrapperResult'
  has_one :location_scrapper_result, -> { where(scrapper_type: 'location') }, class_name: 'ScrapperResult'
  has_many :yelp_images

  has_many :ch_w_customers

  has_many :log_events

  has_many :lead_scoring_reports

  has_many :hf_customers

  has_many :hours_of_operations

  has_many :restaurant_versions

  attribute :restaurant_type, :string
  enum restaurant_type: {
    other: 'other',
    qsr: 'qsr',
    fast_casual: 'fast_casual',
    mid_scale: 'mid_scale',
    casual_dining: 'casual_dining',
    fine_dining: 'fine_dining' }, _prefix: true

  validates_uniqueness_of :address, scope: [:business_name, :city_name], :allow_nil => true

  validates :guid, :uniqueness => true, :allow_nil => true

  before_save :validate_city

  scope :from_city, -> (city_ids){ where(city: city_ids) }
  scope :with_city, -> (city_name){ joins(:city).where('lower(cities.name) = ?', city_name.to_s.downcase )}
  scope :with_price, -> { where.not(price: nil) }

  scope :filter_by_state, ->(state) { left_joins(:state).where(states: state) }
  scope :filter_by_county, ->(county) { left_joins(:county).where(counties: county) }
  scope :filter_by_city, ->(city) { left_joins(:city).where(cities: city) }
  scope :filter_by_region, ->(region) { left_joins(:regions).where(regions: { id: region }) }
  scope :filter_by_chain, ->(chain) { left_joins(:chain).where(chains: chain) }

  scope :with_restaurant_count, lambda { |column_name = 'restaurant_count'|
    all.left_joins(:restaurant => [:chain])
       .select("items.*, (#{restaurant_count_sql}) AS #{column_name}")
  }

  scope :decorate_with_restaurant_count, lambda { |_cities = nil|
    restaurant_count_sql = "SELECT count(id) FROM restaurants r where r.chain_id = restaurants.chain_id"
    if (cities = [*_cities].compact.presence)
      restaurant_count_sql+= " AND r.city_id IN (#{cities.map(&:id).join(',')})"
    else
      # joins(:chain)
      restaurant_count_sql = "COALESCE(chains.total_locations, #{restaurant_count_sql})"
    end
    select("(#{restaurant_count_sql}) as restaurant_count")
  }

  scope :decorate_with_recent_menu_id, lambda { |_cities = nil|
    cities = [*_cities].compact
    city_filter = ''
    city_filter += "AND r.city_id IN (#{cities.map(&:id).join(',')})" if cities.present?

    recent_menu_id_sql = <<-SQL
      (SELECT m.id FROM menus m WHERE m.state = 'approved' AND m.restaurant_id IN (
          SELECT r.id FROM restaurants r WHERE r.chain_id = chains.id #{city_filter}
        ) ORDER BY m.id DESC LIMIT 1)
    SQL
    # joins(:chain)
    select("restaurants.*, #{recent_menu_id_sql} as recent_menu_id")
  }

  scope :uniq_with_locations_count, lambda { |range: nil|
    restaurant_ids = grouped_by_chain_with_locations_count(range: range).select("MAX(id) as id")
    restaurants = where(:id => restaurant_ids)
    if !range || 1.in?(range)
      unchained_restaurant_ids = where(chain_id: nil).group(:business_name).select("MAX(id) as id")
      restaurants = restaurants.or(where(:id => unchained_restaurant_ids))
    end
    restaurants
  }

  scope :all_with_locations_count, lambda { |range: nil|
    chain_ids = grouped_by_chain_with_locations_count(range: range).select(:chain_id)
    restaurants = where(:chain_id => chain_ids)
    restaurants = restaurants.or(where(chain_id: nil)) if !range || 1.in?(range)
    restaurants
  }

  scope :with_approved_menus, -> (time_periods = nil) {
    menus = time_periods ? time_periods.menus : Menu.approved
    where(id: menus.select(:restaurant_id))
  }

  scope :pull_quarterly, -> { where(pull_quarterly: true) }
  scope :with_file_type, ->(file_type){ where('restaurants.menu_url ~* ?', file_type) }
  scope :with_pdf, ->{ with_file_type('pdf') }

  scope :quarterly_dataset, lambda {
    menus = Menu.approved
    Restaurant.joins(:menus).where(:menus => {:id => menus }).pull_quarterly.select('DISTINCT ON(restaurants.business_name) restaurants.*').order('restaurants.business_name')
  }

  scope :quarterly_sample, -> { where(:quarterly_sample => true) }

  scope :top_500, -> {
    chains = Chain.where.not(:total_locations => nil).order('total_locations DESC').limit(500)
    menus = Menu.approved
    query = Restaurant.left_joins(:menus).joins(:chain).where(:chains => {:id => chains}).where(:menus => {:id => menus}).select('DISTINCT ON(chain_id) restaurants.*')
    Restaurant.where(:id => query.map(&:id))
  }

  CW_CATEGORIES = %w(
    greek
    american
    italian
    mexican
    mediterranean
    french
    korea
    japanese
    indian
    thai
  )

  def self.with_similarity_address(address)
    return none if address.blank?

    address = address.strip
    threshold = 0.7 # Adjust threshold according to your needs
    res = where("address ILIKE ?", "#{address}%") # ILIKE query for addresses that start with the given string
    sanitized_address = ActiveRecord::Base.connection.quote(address) # Sanitize input for safe SQL interpolation
    res2_ids = select(:id).where("similarity(address, ?) > ?", address, threshold).order(Arel.sql('similarity(address, ' + sanitized_address + ') DESC')) # Similarity query, adjusted to return only IDs

    where(id: res).or(where(id: res2_ids)) # Combine results using OR, focusing on unique IDs
  end

  def self.with_similar_name_address(business_name, address, zip_code)
    where("similarity(lower(business_name), ?) > 0.78", business_name.to_s.downcase.strip).where("similarity(lower(address), ?) > 0.78", address.to_s.downcase.strip).where("similarity(lower(zip_code), ?) > 0.35", zip_code.to_s.downcase.strip)
  end

  def self.with_similar_name_address_city(business_name, address, city_name)
    where("similarity(lower(business_name), ?) > 0.78", business_name.to_s.downcase.strip).where("similarity(lower(address), ?) > 0.78", address.to_s.downcase.strip).where(:city_name => city_name)
  end

  def self.unique_restaurants_total
    uniq_with_locations_count.count
  end

  def self.count_over_100
    uniq_with_locations_count(range: 101..).count
  end

  def self.count_between_10_100
    uniq_with_locations_count(range: 10..100).count
  end

  def self.count_less_than_10
    uniq_with_locations_count(range: ..9).count
  end

  def self.grouped_by_chain_with_locations_count(range: nil)
    min = range&.begin || 1
    max = range&.end

    query = where.not(chain_id: nil).group(:chain_id)
    if max
      query.having('COUNT(*) >= ? AND COUNT(*) <= ?', min, max)
    else
      query.having('COUNT(*) >= ?', min)
    end
    # grouped_by_chain_with_locations_count(range: 10..100).select(:chain_id)
    # grouped_by_chain_with_locations_count(range: 10..100).select("MIN(id) as id")
  end

  after_create do
    set_guid
    chain&.set_restaurant_count_to_items
  end

  def validate_city
    return if city_id.present?
    return if city_name.blank? || state_name.blank?
    s = State.where("lower(abbrev) = ?", state_name).first
    if s
      c = City.find_or_create_by(:name => city_name, :state_id => s&.id)
      self.city_id = c.id
    end
  end

  pg_search_scope :pg_search, lambda { |_query, params={}|
    tsearch = {}
    params = params.to_h.with_indifferent_access

    query = _query.gsub(/\s+/, '<=>') if params[:exact_match] == '1'
    tsearch[:dictionary] = "english"  if params[:include_substrings] == '1'
    tsearch[:any_word] = true         if params[:some_words_present] == '1'

    search_params = {
      against: %i[business_name],
      query: query || _query
    }
    search_params[:using] = { tsearch: tsearch } if tsearch.present?
    search_params
  }

  # Item.pg_search('pizza').count
  # Item.where("(to_tsvector('simple', coalesce(items.name::text, '')) || to_tsvector('simple', coalesce(items.description::text, ''))) @@ to_tsquery('simple', ''' ' || 'pizza' || ' ''')").count

  def add_restaurant_to_chain(chain_id, menu_id = nil)
    chain_to_add = Chain.find chain_id

    raise StandardError.new("Chain must have same country code.") if (chain_to_add.country_id != self.city&.country_id)

    self.chain_id = chain_id
    self.save

    if menu_id.present?
      menu = Menu.find menu_id
      approved_menu = chain.menus.approved.where(:quarter => menu.quarter, :year => menu.year).where.not(:id => menu.id).last
      if approved_menu.nil?
        raise StandardError.new("No menu found w/ approved quarter and year. Please scrap or repost.")
      end
      menu.copy_and_approve_from(approved_menu)
    end
  end

  def is_potential_chain?
    potential_chain.present?
  end

  def potential_chain
    Chain.where("lower(chains.name) = ?", self.business_name.to_s.downcase.strip).first
  end

  def possible_potential_chain
    chain || potential_chain || Chain.search_by_name(business_name).first rescue nil
  end

  def init_menu(quarter: Menu.current_quarter, year: Menu.current_year)
    Menu.find_or_create_by(:restaurant_id => id, :quarter => quarter, :year => year, :state => "waiting_for_url")
  end

  def self.post_hits(quarter: Menu.current_quarter, year: Menu.current_year)
    self.all.each do |restaurant|
      # ignore posting for data for chef warehouse customers for now
      # https://trello.com/c/fcsEnejW/961-add-cw-check-when-uploading-q4
      # next if restaurant.cw_customer_id.present?

      mturk_hit_service = MturkHitService.new

      menu = Menu.find_or_create_by(:restaurant_id => restaurant.id, :quarter => quarter, :year => year)
      if restaurant.pull_quarterly
        next if restaurant.menus.approved.where(:quarter => quarter, :year => year).count > 0
      end

      if (restaurant.menu_url.present? && restaurant.menu_url.to_s.match(/http/)) || menu.scrap_by_chick_fil_a_api?
        related_menus = Menu.where(:menu_url => restaurant.menu_url, :quarter => quarter, :year => year).where.not(:id => menu.id)
        if related_menus.count > 0
          menu.related_menu_ids = related_menus.pluck(:id).uniq
          menu.failure_reason = "Menu has the same URL as another."
          menu.save
          next
        end
        if restaurant.chain.present? && restaurant.chain.menus.approved.where(:quarter => quarter, :year => year).where.not(:id => menu.id).count > 0
          chain = restaurant.chain
          if chain&.country_id == restaurant.city&.country_id
            restaurant.add_restaurant_to_chain(chain.id, menu.id)
            next
          end
        end
        matching_chains = Chain.where('lower(name) = ?', restaurant.business_name.to_s.downcase).where(:country_id => restaurant.city&.country_id)
        if matching_chains.count > 1 && Menu.approved.left_joins(:restaurant => [:chain]).where(:chains => {:id => matching_chains}).where(:quarter => quarter, :year => year).count > 0
          menu.failure_reason = "Menu might be part of a chain."
          menu.save
          next
        end
        if menu.state.to_s === "waiting_for_url"
          menu.state = "scanned"
          menu.save
        end
        if menu.state.to_s === "scanned"
          if menu.scrapper_class.present? && ENV['SCRAPING_ON'].present?
            ScrapMenuWorker.perform_async menu.id
          elsif menu.menu_url.to_s.match(/\.(jpe?g|png|gif|pdf)$/i)
            PulseExtractWorker.perform_async menu.id
          else
            mturk_hit_service.post_menu_hit(menu)
          end
        end
      else
        menu = Menu.find_or_create_by(:restaurant_id => restaurant.id, :quarter => quarter, :year => year)
        menu.state = "waiting_for_url"
        menu.save
      end
    end
  end

  def yh_alias_from_url
    return if url.blank?
    re = URI.decode_uri_component(url).match(/biz\/(?<yh_alias>.+)/)
    if re&.length == 2
      yh_alias = re[:yh_alias]
      yh_alias = yh_alias.split("/")[0]
      yh_alias.split("?")[0].to_s.downcase
    end
  end

  def yelp_match
    return if url.blank?
    yh_alias = yh_alias_from_url
    return if yh_alias.blank?
    return if zip_code.blank?
    yh = YelpHelper.new
    params = { limit: 25, term: yh_alias, offset: 0, radius: 24140 }

    # using zip code search todo: Refactor this
    resp = yh.client.search(zip_code, params)
    matches = resp.businesses.select{|r|r.alias.to_s.downcase.strip == yh_alias}
    if matches.empty?
      params = { limit: 50, term: yh_alias.split("-").first, offset: 0, radius: 40000 }
      resp = yh.client.search(zip_code, params)
      matches = resp.businesses.select{|r|r.alias.to_s.downcase.strip == yh_alias}
    end
    if matches.empty?
      params = { limit: 50, term: yh_alias.split("-").first(2).join(" "), offset: 0, radius: 40000 }
      resp = yh.client.search(zip_code, params)
      matches = resp.businesses.select{|r|r.alias.to_s.downcase.strip == yh_alias}
    end

    # using address
    if city_name.present? || state_name.present?
      if matches.empty?
        params = { limit: 25, term: yh_alias, offset: 0, radius: 24140 }
        resp = yh.client.search("#{city_name},#{state_name}", params)
        matches = resp.businesses.select{|r|r.alias.to_s.downcase.strip == yh_alias}
      end
      if matches.empty?
        params = { limit: 50, term: yh_alias.split("-").first, offset: 0, radius: 40000 }
        resp = yh.client.search("#{address} #{city_name},#{state_name}", params)
        matches = resp.businesses.select{|r|r.alias.to_s.downcase.strip == yh_alias}
      end
      if matches.empty?
        params = { limit: 50, term: yh_alias.split("-").first(2).join(" "), offset: 0, radius: 40000 }
        resp = yh.client.search("#{city_name},#{state_name}", params)
        matches = resp.businesses.select{|r|r.alias.to_s.downcase.strip == yh_alias}
      end
    end

    return if matches.empty?
    matches[0]
  end

  def sync_with_yelp(force: false)
    return if self.alias.present? unless force
    match = yelp_match
    return if match.nil?

    self.alias = match.alias
    self.price = match.price
    self.phone = match.phone
    self.review_count = match.review_count
    self.rating = match.rating
    self.image_url = match.image_url
    self.display_address = match.location.display_address.to_a if display_address.blank?
    self.city_name = match.location.city.to_s.downcase if city_name.blank?
    self.state_name = match.location.state.to_s.downcase if state_name.blank?
    categories_map = match.categories.map{|cat|cat.title.to_s.downcase.strip }
    self.yelp_categories = categories_map
    categories_map.each do |cat|
      c = Category.find_or_create_by(:name => cat)
      RestaurantCategory.create(:restaurant_id => self.id, category_id: c.id)
    end
    self.save
  end

  def sync_with_yelp_api(force: false)
    return if self.alias.present? unless force
    return if yh_alias_from_url.blank?
    yh = YelpHelper.new
    res = yh.restaurant(yh_alias_from_url.to_s.remove_non_ascii)

    self.alias = res["alias"]
    self.price = res["price"]
    self.phone = res["phone"]
    self.review_count = res["review_count"]
    self.rating = res["rating"]
    self.image_url = res["image_url"]

    location = res["location"]
    if location.present?
      self.address = location["address1"].to_s.downcase.strip if address.blank?
      self.city_name = location["city"].to_s.downcase.strip if city_name.blank?
      self.state_name = location["state"].to_s.downcase.strip if state_name.blank?
      self.display_address = location["display_address"]
    end

    categories_map = res["categories"].to_a.map{|cat|cat["title"].to_s.downcase.strip }
    self.yelp_categories = categories_map
    categories_map.each do |cat|
      c = Category.find_or_create_by(:name => cat)
      RestaurantCategory.create(:restaurant_id => self.id, category_id: c.id)
    end
    save(:validate => false)
  end

  def self.sync_with_yelp
    all.each(&:sync_with_yelp)
  end

  def sync_hours_of_operation(_hours=nil)
    hours ||= begin
      return if self.alias.blank?
      yh = YelpHelper.new
      yh.hours_of_operation(self.alias.to_s.remove_non_ascii)
    end
    if hours.to_a.length > 0
      hours_of_operations.destroy_all
      hours.each do |hour|
        HoursOfOperation.find_or_create_by(
          :restaurant_id => id,
          :day => hour["day"],
          :is_overnight => hour["is_overnight"],
          :start_time => hour["start"],
          :end_time => hour["end"]
        )
      end
    end
  end

  def self.domain(url)
    return if !url.to_s.match(URI::DEFAULT_PARSER.make_regexp)
    begin
      url = url.to_s.gsub(/\n/, "")
      uri = URI.parse(url)
      uri.host.to_s.downcase.strip
    rescue URI::InvalidURIError => error
      pp url
      pp error.message
      ""
    end
  end

  def self.auto_create_chains
    restaurants = all.joins(:city).where.not(:domain => [nil, ""]).order(:business_name, :domain).select("lower(business_name) as business_name, domain, cities.country_id as country_id").group(:business_name, :domain, :country_id).having("count(*) > 1")
    restaurants.each do |r|
      business_name = r.business_name.to_s.downcase.strip
      next if business_name.blank?
      # pp business_name

      matches = Restaurant.where("lower(restaurants.business_name) = ?", business_name).where.not(:domain => [nil, ""])
      if matches.count > 1 && matches.where(:chain_id => nil).count > 0
        c = Chain.find_or_create_by!(:name => business_name, :country_id => r.country_id)
        matches.where(:chain_id => nil).each do |m|
          next if m.chain_id.present?
          next if m.city&.country_id != c.country_id
          m.add_restaurant_to_chain(c.id)
          menus = m.menus.where.not(:state => %w[approved failed])
          menus.each do |menu|
            if menu.items.empty? && m.menus.approved.where(:quarter => menu.quarter, :year => menu.year).count > 0
              m.add_restaurant_to_chain(c.id, menu.id)
            end
          end
        end
      end
    end
  end

  def biz_url(category = "food")
    "https://www.yelp.com/biz_photos/#{self.alias}?tab=#{category}"
  end

  def scrap_yelp_images
    if self.alias.blank?
      sync_with_yelp_api
      reload
    end
    return if self.alias.blank?
    categories = ['food', 'drink']
    begin
      categories.each do |c|
        biz_cat_url = biz_url(c)
        res = YelpImageScraper.new(biz_cat_url).images
        if res && res[:page_info]
          page_info = res[:page_info]
          current_page = page_info[:current_page]
          last_page = page_info[:last_page]
          s_init = ScrapperResult.find_or_create_by(:restaurant_id => id, :page_number => 1, :url => biz_cat_url, :klass => "YelpImageScraper")
          s_init.yelp_category = c
          s_init.response = res
          s_init.save

          if last_page.to_i > 1
            ((current_page.to_i + 1)..(last_page.to_i)).each do |page|
              start = (page.to_i - 1) * 30
              new_url = "#{biz_cat_url}&start=#{start}"
              new_res = YelpImageScraper.new(new_url).images

              sr = ScrapperResult.find_or_create_by(:restaurant_id => id, :page_number => page, :url => new_url, :klass => "YelpImageScraper")
              sr.yelp_category = c
              sr.response = new_res
              sr.save
            end
          end
        end
      end
    rescue WebScrapper::WebScraperError => error
      Rails.logger.error(error.message)
    end


    convert_results_to_yelp_images
    yelp_images.reload
    # fetch_yelp_dates
  end

  def convert_results_to_yelp_images
    scrapper_results.where(:klass => "YelpImageScraper").each do |sr|
      response = sr.response
      next if response.nil?

      response["images"].to_a.each do |sr_image|
        y = YelpImage.find_or_create_by(:restaurant_id => id, :src => sr_image['src'], :alt => sr_image['alt'], :width => sr_image['width'], :height => sr_image['height'], :yelp_photo_id => sr_image['photo_id'])
        y.yelp_category = sr.yelp_category
        y.save
      end
    end
  end

  def fetch_yelp_dates
    yelp_images.each do |yi|
      yi.fetch_image_date
    end
  end



  def review_count_display
    return if review_count.to_i.zero?
    "#{review_count} reviews"
  end

  def check_city
    if city_name.present? && city.blank?
      params = { :name => city_name }
      state = State.find_by(:abbrev => state_name)
      params[:state_id] = state.id if state.present?
      self.city = City.find_by(params)
    end
  end

  def check_display_address
    if display_address.blank?
      # street_address = address
      self.city_name = city_name.presence || city&.name
      self.state_name = state_name.presence || city&.state&.abbrev
      self.country = country.presence || city&.country&.abbrev.presence
      self.zip_code = zip_code.presence || Geocoder.search("#{address}, #{city_name}, #{state_name}, #{country}").select { |l| l.postal_code.present? }.first&.postal_code

      self.display_address = [address, city_name, state_name, zip_code, country].compact
    end
    display_address
  end

  def full_address
    check_display_address
    "#{address}, #{city_name}, #{state_name} #{zip_code} #{country}".to_s.strip
  end

  def set_menu_url
    return if !url
    yh = YelpImageScraper.new(url)
    res = yh.menu_url
    self.menu_url = res[:menu_url]
    self.save
  end

  def last_menu
    menus.order('menus.year, menus.quarter').where(:state => ["approved", "failed"]).last
  end

  def set_guid
    return if guid.present?
    self.guid = SecureRandom.uuid
    self.save
  end

  def set_yelp_from_google
    return if url.present? && url.to_s.match(/yelp/)
    result = GoogleSearchHelper.find_address business_name, address: address, zip: zip_code
    if result.present? && result.to_s.match(/yelp/) && !result.to_s.match(/search/)
      self.url = result
      save
    end
    self.ran_google_yelp = true
    save
  end

  def self.merge_restaurants(restaurant, restaurants_to_merge = [])
    restaurants_to_merge.each do |r|
      next if r.id === restaurant.id
      r.menus.each do |m|
        m.restaurant_id = restaurant.id
        m.items.update(:restaurant_id => restaurant.id)
        m.save(:validate => false)
      end
      r.destroy
    end

  end

  def translate(translate_from: "", translate_to: "English")
    if translate_from.blank?
      if city&.country&.language.present?
        translate_from = city&.country&.language
      end
    end
    Restaurants::TranslateBusinessNameWorker.perform_async id, translate_from, translate_to
  end

  def filtered_categories
    CW_CATEGORIES.select do |c|
      yelp_categories.to_a.any?{|yc|
        yc.to_s.match(/#{c}/)
      }
    end.uniq.flatten.compact
  end

  def self.distribution
    uniq_chain_count = all.joins(:chain).select('DISTINCT ON(chain_id) restaurants.*').group('restaurants.id').having('COUNT(chains.id) > 0').length
    total_restaurants_count = all.distinct.count
    chain_count = all.joins(:chain).distinct.count
    individual_count = all.where(:chain_id => nil).distinct.count
    {
      :chain_count => chain_count,
      :uniq_chain_count => uniq_chain_count,
      :individual_count => individual_count,
      :total_restaurants_count => total_restaurants_count
    }
  end

  def self.get_distribution(key="price")
    groups = all.group_by{|x| x.send(key.to_sym)}
    result = groups.transform_values do |restaurants|
      (restaurants.count / all.count.to_f * 100).round(2)
    end
    result.sort_by { |key, _| key.to_s }.to_h
  end

  def self.cuisine_distribution
    restaurants = RestaurantCategory.joins(:restaurant).where(:restaurant_id => all)
    groups = restaurants.group_by{|rc|
      rc.category&.filtered_name
    }
    result = groups.transform_values do |restaurants|
      (restaurants.count / all.count.to_f * 100).round(2)
    end
    result.sort_by { |key, _| key.to_s }.to_h
  end

  def save_version(event="restaurant_update")
    RestaurantVersion.create(:restaurant_id => id, :restaurant_data => restaurant_json, :event => event)
  end

  def restaurant_json
    self.as_json(
      :only => [:business_name,
                :address,
                :address_2,
                :city_name,
                :state_name,
                :zip_code,
                :url,
                :menu_url,
                :doordash_url,
                :open_table_url,
                :uber_eats_url,
                :all_menus_url]
    )
  end

  # Restaurant.calculate_penetration_rates("green tea", 3, 2023, {city_id: 165})
  # Restaurant.calculate_penetration_rates("green tea", 3, 2023, {restaurant_type: "qsr})
  def self.calc_penetration_rate(query, quarter, year, additional_filters={})
    menus = Menu.where(quarter: quarter, year: year).approved

    total_restaurants = Restaurant.joins(menus: :items).where(menus: { id: menus })
    if additional_filters.present?
      total_restaurants = total_restaurants.where(additional_filters)
    end

    restaurants_with_item = total_restaurants.where("LOWER(items.name) LIKE '%#{query}%' OR LOWER(items.description) LIKE '%#{query}%'")

    restaurants_with_item_count = restaurants_with_item.distinct.count
    total_restaurants_count = total_restaurants.distinct.count

    Rails.logger.info("Query: #{query}")
    Rails.logger.info("Quarter: #{quarter} Year: #{year}")
    Rails.logger.info("Additional Filters: #{additional_filters}")
    Rails.logger.info("Restaurants with Item: #{restaurants_with_item_count}")
    Rails.logger.info("Total Restaurants: #{total_restaurants_count}")

    penetration_rate = (restaurants_with_item_count.to_f / total_restaurants_count * 100).round(2)
    Rails.logger.info("Penetration Rate: #{penetration_rate}")
    penetration_rate
  end

  # elastic search version
  def self.calc_penetration_rate_es(query, quarter, year, city_ids:[], restaurant_type: nil)
    restaurants_with_item_count = Menu.search_with_item(quarter, year, query: query, city_ids: city_ids, restaurant_type: restaurant_type).total_count
    total_restaurants_count = Menu.search_with_item(quarter, year, city_ids: city_ids, restaurant_type: restaurant_type).total_count

    additional_filters = {
      :city_id => city_ids,
      :restaurant_type => restaurant_type
    }

    Rails.logger.info("Query: #{query}")
    Rails.logger.info("Quarter: #{quarter} Year: #{year}")
    Rails.logger.info("Additional Filters: #{additional_filters}")
    Rails.logger.info("Restaurants with Item: #{restaurants_with_item_count}")
    Rails.logger.info("Total Restaurants: #{total_restaurants_count}")

    penetration_rate = (restaurants_with_item_count.to_f / total_restaurants_count * 100).round(2)
    Rails.logger.info("Penetration Rate: #{penetration_rate}")
    if penetration_rate.nan?
      penetration_rate = 0
    end
    penetration_rate
  end


  def self.calc_pen_rate_in_batches(query, quarter, year, additional_filters={})
    menus = menus_for_quarter_year(quarter, year)

    total_with_item = []
    total_restaurants = []

    menus.in_batches do |menu_batch|
      restaurant_batch = restaurants_for_menu_batch(menu_batch)
      if additional_filters.present?
        restaurant_batch = restaurant_batch.where(additional_filters)
      end

      restaurants_with_item = restaurant_batch.where("LOWER(items.name) LIKE '%#{query}%' OR LOWER(items.description) LIKE '%#{query}%'").select('DISTINCT ON(restaurants.id) restaurants.id')

      total_with_item.push(restaurants_with_item)

      total_restaurants.push(restaurant_batch.select('DISTINCT ON(restaurants.id) restaurants.id'))
    end

    restaurants_with_item_count = total_with_item.flatten.uniq.count
    total_restaurants_count = total_restaurants.flatten.uniq.count

    Rails.logger.info("Query: #{query}")
    Rails.logger.info("Quarter: #{quarter} Year: #{year}")
    Rails.logger.info("Additional Filters: #{additional_filters}")
    Rails.logger.info("Restaurants with Item: #{restaurants_with_item_count}")
    Rails.logger.info("Total Restaurants: #{total_restaurants_count}")

    penetration_rate = (restaurants_with_item_count.to_f / total_restaurants_count * 100).round(2)
    Rails.logger.info("Penetration Rate: #{penetration_rate}")
    penetration_rate
  end

  def self.menus_for_quarter_year(quarter, year)
    cache_key = "menus_#{quarter}_#{year}"
    Rails.cache.fetch(cache_key, expires_in: 24.hours) do
      Menu.where(quarter: quarter, year: year).approved
    end
  end

  def self.restaurants_for_menu_batch(menu_batch)
    cache_key = Digest::MD5.hexdigest("restaurants_#{menu_batch.to_sql}")
    Rails.cache.fetch(cache_key, expires_in: 24.hours) do
      Restaurant.joins(menus: :items).where(menus: { id: menu_batch })
    end
  end

end
