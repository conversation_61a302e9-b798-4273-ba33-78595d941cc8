class LeadScoringReport::Basic < LeadScoringReport

  def self.init_lead_items(customer_id)
    customer = Customer.find customer_id
    raise StandardError.new("Customer required.") if customer.nil?

    items = customer.lead_scoring_item_list.watch_list
    items.each do |item|
      LeadItem.find_or_create_by(:name => item, :customer_id => customer.id)
    end
  end

  def check_score(item, course_category = nil)
    score = 0

    item_name = item.name.to_s.downcase.strip
    item_description = item.description.to_s.downcase.strip
    LeadItem.with_customer(customer.id).each do |l|
      score_name = l.name.to_s.downcase.strip
      if item_name.match(/#{score_name}/) || item_description.match(/#{score_name}/)
        ItemLeadItem.find_or_create_by(:item_id => item.id, :lead_item_id => l.id)
        score += 5
      end
    end
    score
  end


end
