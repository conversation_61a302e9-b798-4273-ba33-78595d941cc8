class Quarter < ApplicationRecord # MenuItemDashboardQuarter
  belongs_to :menu_item_dashboard
  belongs_to :retail_insights_dashboard
  belongs_to :time_period

  belongs_to :quarterable, polymorphic: true


  # has_many :item_quarters, dependent: :delete_all
  # has_many :items, :through => :item_quarters

  delegate :query, :to => :menu_item_dashboard
  delegate :menus, :year, :quarter, :name, :to => :time_period

  has_many :quarter_products
  has_many :products, through: :quarter_products

  default_scope { order('quarters.time_period_id') }

  enum menu_adoption: {
    emergence: 'emergence',
    growth: 'growth',
    mainstream: 'mainstream',
    mature: 'mature'
  }

  before_save :set_menu_adoption


  scope :last_year, ->{ joins(:time_period).where(:time_periods => {:id => TimePeriod.last_year}).order(Arel.sql("time_periods.year, time_periods.quarter")) }

  # for use with csv
  def quarterly_row
    [menu_item_dashboard.id, menu_item_dashboard.name, id, year, quarter, time_period_id, items_count, menus_count, restaurant_count, restaurants_with_items_count, pen_rate]
  end

  def time_period_menus
    Menu.for_all_periods(TimePeriod.last_year)
  end

  def restaurants
    Rails.cache.fetch("restaurants_from_last_year", expires_in: 1.day) do
      Restaurant.left_joins(:menus).where(:menus => {:id => time_period_menus}).distinct
    end
  end

  def restaurant_with_items_ids
    menus = Menu.left_joins(:items).where(:items => {:id => search_items})
    (menus.pluck(:restaurant_id) & restaurants.pluck(:id))
  end


  def assign_items
    set_restaurants_with_items_count
  end

  def search_items(db_search: true)
    if db_search
      @search_items ||= Item.joins(:menu).where(:menus => {:quarter => quarter, :year => year, :restaurant_id => restaurants}).pg_search(menu_item_dashboard.query, :exact_match => '1').uniq_by_chain_and_name2
    else
      filters = { quarter: quarter, year: year, restaurant_id: restaurants.ids }
      elastic_service = Search::ElasticService.new(params: { q: menu_item_dashboard.query, filters: filters, per_page: 10000 })
      # ids = elastic_service.search.results.map { |r| r['_id'] }
      elastic_service.search.records
    end
  end

  def items
    Item.where(:id => search_items.pluck(:id))
  end

  def restaurants_with_items
    @restaurants_with_items ||= Restaurant.where(:id => restaurant_with_items_ids)
  end

  def menus_with_items
    @menus_with_items ||= Menu.left_joins(:restaurant).where(:restaurants => {:id => restaurants_with_items}).where(:quarter => quarter, :year => year).distinct.approved
  end

  def es_search_items_count
    Menu.search_with_item(quarter, year, query: menu_item_dashboard.query, restaurant_ids: restaurants.pluck(:id)).total_count
  end

  def set_restaurants_with_items_count

    self.items_count = es_search_items_count

    self.pen_rate = calc_pen_rate
    self.сalifornia_pen_rate = calc_pen_rate_for(Region.california)
    self.west_pen_rate = calc_pen_rate_for(Region.west)
    self.plains_pen_rate = calc_pen_rate_for(Region.plains)
    self.great_lakes_pen_rate = calc_pen_rate_for(Region.great_lakes)
    self.south_central_pen_rate = calc_pen_rate_for(Region.south_central)
    self.south_east_pen_rate = calc_pen_rate_for(Region.south_east)
    self.mid_south_pen_rate = calc_pen_rate_for(Region.mid_south)
    self.north_east_pen_rate = calc_pen_rate_for(Region.north_east)

    self.qsr_penetration = segment_pen_rate(:qsr)
    self.fast_casual_penetration = segment_pen_rate(:fast_casual)
    self.mid_scale_penetration = segment_pen_rate(:mid_scale)
    self.fine_dining_penetration = segment_pen_rate(:fine_dining)

    set_delta
    calc_change_1y
    save

    update_ingredient if menu_item_dashboard.recent_year.last.id == id
    self.is_complete = true
    save
  end

  def calc_menu_items
    raise StandardError.new("only use with Menu Items") if quarterable_type != "MenuItem"
    self.pen_rate = Restaurant.calc_penetration_rate_es(quarterable.name, quarter, year)
    calc_foodservice_growth
    save
  end

  def assign_products
    return if retail_insights_dashboard.nil?
    found_products = retail_insights_dashboard.search_products.left_joins(:product_catalogs).where(:product_catalogs => {:quarter => time_period.quarter, :year => time_period.year})
    (found_products - products).find_all do |product|
      QuarterProduct.find_or_create_by product_id: product.id, quarter_id: id
    end
  end

  def calc_pen_rate
    Restaurant.calc_penetration_rate_es(menu_item_dashboard.query,quarter,year)
  end

  def calc_pen_rate_for(region)
    city_ids = region.cities.map{|c|c.id}
    Restaurant.calc_penetration_rate_es(menu_item_dashboard.query,quarter, year, city_ids: city_ids)
  end

  def segment_pen_rate(segment)
    Restaurant.calc_penetration_rate_es(menu_item_dashboard.query, quarter, year, restaurant_type: segment)
  end

  def calc_menu_adoption
    if pen_rate.to_f >= 5
      Quarter.menu_adoptions[:mature]
    elsif pen_rate.to_f >= 3 && pen_rate.to_f < 5
      Quarter.menu_adoptions[:mainstream]
    elsif pen_rate.to_f >= 1 && pen_rate.to_f < 3
      Quarter.menu_adoptions[:growth]
    else
      Quarter.menu_adoptions[:emergence]
    end
  end

  def set_menu_adoption
    self.menu_adoption = calc_menu_adoption
  end


  def update_ingredient
    # for now need to update both places bc of dole ingredient / ingredient
    # remove after clarification
    menu_item_dashboard&.update_metrics
    menu_item_dashboard&.ingredient&.set_metrics
  end

  def calc_foodservice_growth
    raise StandardError.new("only use with Menu Items") if quarterable_type != "MenuItem"

    quarters = quarterable.quarters.order('quarters.time_period_id')
    current_index = quarters.index(self)
    return if current_index < 1

    prev_quarter = quarters[current_index - 1]
    return if prev_quarter.pen_rate.to_f.zero?
    self.delta_last_period = ((self.pen_rate.to_f - prev_quarter.pen_rate.to_f).to_f / prev_quarter.pen_rate.to_f * 100).to_f.round(2)
  end

  def set_delta
    quarters = menu_item_dashboard.quarters.order('quarters.time_period_id')
    current_index = quarters.index(self)
    return if current_index < 1

    prev_quarter = quarters[current_index - 1]
    return if prev_quarter.pen_rate.to_f.zero?
    self.delta_last_period = ((self.pen_rate.to_f - prev_quarter.pen_rate.to_f).to_f / prev_quarter.pen_rate.to_f * 100).to_f.round(2)
  end

  def calc_change_1y
    quarters = menu_item_dashboard.quarters.order('quarters.time_period_id')
    current_index = quarters.index(self)
    return if current_index < 4
    quarter_1y_ago = quarters[current_index - 4]
    return if quarter_1y_ago.pen_rate.to_f.zero?
    self.change_1y = ((self.pen_rate.to_f - quarter_1y_ago.pen_rate.to_f).to_f / quarter_1y_ago.pen_rate.to_f * 100).to_f.round(2)
  end

  def previous_quarter
    return if quarterable.nil?
    quarters = quarterable.quarters.order('quarters.time_period_id')
    current_index = quarters.index(self)
    return if current_index < 1
    quarters[current_index - 1]
  end

  def calc_pairing_trend_count
    return if quarterable_type.to_s != "IngredientPairingTrend"
    self.pairing_trend_count = quarterable.calc_pairing_count(quarter, year)
    self.is_complete = true
    save
  end

  # only use this calc after all total pair counts is set
  def calc_pairing_trend_percents
    total_pairs = Quarter.where(:quarterable_type => "IngredientPairingTrend").where(:time_period_id => time_period_id).sum(:pairing_trend_count).to_f
    if total_pairs != 0
      self.pairing_trend_percent = (pairing_trend_count.to_f / total_pairs.to_f) * 100

      prev_percent = previous_quarter&.pairing_trend_percent.to_f
      self.pairing_trend_change = pairing_trend_percent.to_f - prev_percent
      save
    end
  end

  private
end
