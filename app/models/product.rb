class Product < ApplicationRecord
  include PgSearch::Model
  include ProductSearchable

  include Elasticsearch::Model
  # include Elasticsearch::Model::Callbacks

  has_many :images
  has_many :pricing_data_points
  belongs_to :retailer, counter_cache: true
  belongs_to :store
  belongs_to :product_category
  belongs_to :product_subcategory

  normalizes :standard_ingredient_name, with: ->(name) {name.to_s.downcase.strip}

  has_neighbors :embedding

  has_neighbors :sim_embedding

  has_many :product_projects
  has_many :projects, through: :product_projects

  has_many :item_products
  has_many :items, :through => :item_products

  has_many :product_list_products
  has_many :product_lists, :through => :product_list_products

  has_many :product_catalog_products
  has_many :product_catalogs, :through => :product_catalog_products

  has_many :quarter_products
  has_many :quarters, :through => :quarter_products

  has_many :batch_assignments, as: :batchable, dependent: :destroy
  has_many :batch_ai_operations, through: :batch_assignments

  delegate :source, to: :retailer

  scope :customer_enabled, -> { joins(:retailer).where(retailers: { customer_enabled: true }) }
  scope :uniq_by_upc, -> { where(id: select('MAX(products.id) AS id').group(:upc)) }
  scope :by_category, ->(category_name) {
    if category_name.present?
      where('product_categories.name = ?', category_name)
    else
      all
    end
  }
  scope :with_dashboard_enabled, -> { joins(:retailer).where(retailers: { dashboard_enabled: true }) }

  scope :by_brand, ->(brand_id) {
    if brand_id.present?
      where('LOWER(products.brand) = ?', brand_id.downcase)
    else
      all
    end
  }

  scope :by_quarter, ->(year, quarter) {
    if year.present? && quarter.present?
      joins(:product_catalogs).where('product_catalogs.year = ? AND product_catalogs.quarter = ?', year, quarter)
    else
      all
    end
  }


  scope :by_retailer, ->(retailer_name) {
    if retailer_name.present?
      joins(:retailer).where('retailers.name = ?', retailer_name)
    else
      all
    end
  }

  scope :with_gpt_ingredient, ->(gpt_ingredient_id) { joins(:item_products).where(:item_products => {:gpt_ingredient_id => gpt_ingredient_id}).order("item_products.order_idx") }
  scope :non_food_excluded, -> { joins(:product_category).where.not('LOWER(TRIM(product_categories.name)) IN (?)', ProductCategory::EXCLUDED_CATEGORIES) }

  scope :without_claude_category_batch, -> {
    left_joins(batch_assignments: :batch_ai_operation)
      .where(batch_assignments: { id: nil })
      .or(
        left_joins(batch_assignments: :batch_ai_operation)
          .where.not(batch_ai_operations: { service_type: 'claude_category' })
          .where(batch_assignments: { batchable_type: 'Product' })).distinct }

  pg_search_scope :pg_search, lambda { |raw_query, params = {}|
    tsearch = {}
    params = params.to_h.with_indifferent_access

    query = raw_query.gsub(/\s+/, '<=>') if params[:exact_match] == '1'
    tsearch[:dictionary] = "english"  if params[:include_substrings] == '1'
    tsearch[:any_word] = true         if params[:some_words_present] == '1'

    search_params = {
      against: %i[name description],
      query: query || raw_query
    }
    search_params[:using] = { tsearch: tsearch } if tsearch.present?
    search_params
  }

  def self.search(q, cities: nil, search_options: {})
    products_sql = pg_search(q, search_options).with_pg_search_rank.to_sql
    products = Product.from("(#{products_sql}) AS products")
    products = products.uniq_by_upc.customer_enabled

    # if cities.present? # TODO q: do filtering by city?
    #   products = products.joins(:store)
    #   products = products.where(store: { city: cities })
    # end
    products.order(pg_search_rank: :desc)
  end

  def similar_products
    if upc.blank? && slug.present?
      Product.where(:slug => slug).select("DISTINCT ON(products.store_id) products.*")
    else
      Product.where(:upc => upc).select("DISTINCT ON(products.store_id) products.*")
    end
  end

  def set_embedding
    begin
      o = OpenAi::Embedding.new
      emb = o.text2embedding(attrs_to_embedding_str)
      self.embedding = emb[:embedding]
      self.save
    rescue StandardError => error
      pp error.message
    end
  end

  def attrs_to_embedding_str(attributes: ["name", "description"])
    attributes.map do |attr|
      normalized_attr = send(attr).to_s.downcase.strip
      "#{attr}: #{normalized_attr}"
    end.join(" | ")
  end

  def set_sim_embedding
    begin
      o = OpenAi::Embedding.new
      emb = o.text2embedding(standard_ingredient_name.to_s.downcase.strip)
      self.sim_embedding = emb[:embedding]
      self.save
    rescue StandardError => error
      pp error.message
    end
  end

  def self.vector_search(query="", limit: 5, retailer_id: nil, attribute: "embedding")
    begin
      embedding = query.to_embed
      products = Product.all
      if retailer_id.present?
        products = products.where(:retailer_id => retailer_id)
      end
      products.nearest_neighbors(attribute.to_sym, embedding, distance: "cosine").first(limit)
    rescue StandardError => error
      pp error.message
      return Product.none
    end
  end

  def n_distance_in(product_list_id)
    return if product_list_id.nil?

    match = product_list_products.where(:product_list_id => product_list_id).first
    return if match.nil?

    match.neighbor_distance.to_f.round(4)
  end

  def self.to_sheet
    file_name = "#{name}-#{SecureRandom.hex(5)}.csv"
    tempfile = Tempfile.new('foo')

    CSV.open(tempfile, "wb") do |csv|
      csv << ['id', 'name', 'description','price', 'upc', 'brand_name', 'pack_size', 'uom', 'category', 'subcategory']

      products = all.order("name, brand, upc")
      products.find_each do |p|
        csv << [p.id, p.name, p.description, p.price, p.upc, p.brand, p.pack_size, p.uom, p.product_category&.name, p.product_subcategory&.name]
      end
    end

    aws_manager = AwsS3Manager.new
    obj = aws_manager.store_file(file_name, tempfile.read)
    CloudfrontHelper.signed_url(obj.public_url)
  end

  def self.top_growing_categories(limit = 4)
    cache_key = "top_growing_categories_#{limit}"
    Rails.cache.fetch(cache_key, expires_in: 4.hours) do
      query = Product.l2_growth_query
      result = Product.__elasticsearch__.search(query)
      buckets = result.response.dig("aggregations", "by_catalog", "by_year_quarter", "buckets")

      data = {}
      all_years = Set.new

      buckets.each do |bucket|
        year = bucket["key"]["year"]
        quarter = bucket["key"]["quarter"]
        all_years << year

        l2_buckets = bucket.dig("to_root", "by_l2", "buckets") || []
        l2_buckets.each do |l2_bucket|
          l2 = l2_bucket["key"]
          count = l2_bucket["doc_count"]

          data[l2] ||= {}
          data[l2][year] ||= {}
          data[l2][year]["q#{quarter}".to_sym] = count
        end
      end

      all_quarters = [:q1, :q2, :q3, :q4]
      data.each do |l2, years_data|
        all_years.each do |year|
          years_data[year] ||= {}
          all_quarters.each { |q| years_data[year][q] ||= 0 }
        end
      end

      results = data.map do |l2, years_data|
        quarters_output = {}
        all_percents = []

        years_data.sort.each do |year, quarters|
          quarters_output[year.to_s] = {}
          prev = nil

          all_quarters.each do |q|
            curr = quarters[q]
            percent = if prev.nil?
                        0.0
                      elsif prev > 0
                        (((curr - prev) * 100.0) / prev).round(2)
                      else
                        0.0
                      end

            all_percents << percent if !prev.nil? && prev > 0

            quarters_output[year.to_s][q.to_s] = {
              value: curr,
              percent: percent,
              previous_count: prev || 0
            }

            prev = curr
          end
        end

        average_growth = all_percents.any? ? (all_percents.sum / all_percents.size).round(2) : 0.0

        {
          category_name: l2,
          quarters: quarters_output,
          average_growth: average_growth
        }
      end

      results.sort_by { |entry| -entry[:average_growth] }.first(limit)
    end
  end

  def self.white_space_opportunities(store_ids: nil, limit: 4, sort_by: 'saturation_percentage', sort_order: nil)
    m_c = 'l1' # metric category
    l0_sku_counts = Product.where.not(l0: nil, sku: nil, brand: nil)
    l0_sku_counts = l0_sku_counts.where(store_id: store_ids) if store_ids&.any?
    l0_sku_counts = l0_sku_counts.group('l0').select('l0, COUNT(DISTINCT sku) AS l0_unique_sku_count')

    metrics = Product.where.not(m_c => nil, sku: nil, brand: nil)
    metrics = metrics.where(store_id: store_ids) if store_ids&.any?
    metrics = metrics.group(m_c).select("#{m_c} AS category_name, COUNT(DISTINCT sku) AS unique_sku_count, COUNT(DISTINCT brand) AS unique_brands")

    valid_sort_fields = %w[saturation_percentage unique_brands category_name]
    sort_by = valid_sort_fields.include?(sort_by) ? sort_by : 'saturation_percentage'

    sort_order = %w[ASC DESC].include?(sort_order&.upcase) ? sort_order.upcase : ''

    order_clause = sort_order.present? ? "ORDER BY #{sort_by} #{sort_order}" : ''

    query = <<-SQL
    WITH l0_sku_counts AS (
      #{l0_sku_counts.to_sql}
    ),
    metrics AS (
      #{metrics.to_sql}
    )
    SELECT
      metrics.category_name,
      metrics.unique_sku_count,
      metrics.unique_brands,
      l0_sku_counts.l0_unique_sku_count,
      CASE
        WHEN l0_sku_counts.l0_unique_sku_count = 0 THEN 0
        ELSE ROUND((metrics.unique_sku_count * 100.0 / l0_sku_counts.l0_unique_sku_count), 2)
      END AS saturation_percentage
    FROM metrics
    JOIN products ON products.#{m_c} = metrics.category_name
    JOIN l0_sku_counts ON products.l0 = l0_sku_counts.l0
    WHERE metrics.category_name IS NOT NULL
      AND LOWER(metrics.category_name) != 'other'  -- Exclude "other" category
      #{store_ids&.any? ? "AND products.store_id IN (#{store_ids.join(',')})" : ''}
    GROUP BY metrics.category_name, metrics.unique_sku_count, metrics.unique_brands, l0_sku_counts.l0_unique_sku_count
    #{order_clause}
    LIMIT #{limit}
  SQL

    cache_key = "white_space_opportunities_#{store_ids&.join('_') || 'all'}_#{limit}_#{sort_by}_#{sort_order}"
    Rails.cache.fetch(cache_key, expires_in: 4.hours) do
      ActiveRecord::Base.connection.exec_query(query).map do |row|
        saturation = row['saturation_percentage'].to_f
        {
          category_name: row['category_name'],
          saturation: case
                      when saturation < 1 then 'Low'
                      when saturation <= 5 then 'Medium'
                      else 'High'
                      end,
          saturation_percent: "#{saturation}%",
          unique_brands: row['unique_brands']
        }
      end
    end
  end

  def self.top_retailers_banner_name_by_sku(collection: Product, limit: 5)
    raw = collection
            .reorder(nil)
            .where.not(l0: nil)
            .where.not(sku: nil)
            .where.not(additional_fields: nil)
            .where.not("additional_fields->>'banner_name' IS NULL")
            .group("additional_fields->>'banner_name'")
            .select("additional_fields->>'banner_name' AS banner_name",
                    "COUNT(products.id) AS products_with_l0",
                    "COUNT(DISTINCT products.sku) AS unique_skus")

    grouped = raw.group_by(&:banner_name).map do |banner_name, group|
      {
        name: banner_name,
        products_with_l0: group.sum { |r| r.products_with_l0.to_i },
        unique_skus: group.sum { |r| r.unique_skus.to_i }
      }
    end

    grouped.sort_by { |r| -r[:unique_skus] }.first(limit)
  end

  def self.top_retailers(limit: 5)
    cache_key = "top_retailers_banner_name_by_sku_aggregated_#{limit}"
    Rails.cache.fetch(cache_key, expires_in: 4.hours) do
      top_retailers_banner_name_by_sku(limit:)
    end
  end

  def self.top_l2_categories_by_store_ids(collection: Product, store_ids: [], limit: 6, quarter: :q4)
    query = collection.reorder(nil)
                      .where.not(l2: nil, sku: nil)
                      .where.not("LOWER(products.l2) = ?", "other")
                      .where("week_date ~ ?", '^\d{4}-\d{2}-\d{2}$')
    query = query.where(store_id: store_ids) if store_ids&.any?

    counts = query.group('products.l2', 'EXTRACT(YEAR FROM CAST(week_date AS date))', 'EXTRACT(MONTH FROM CAST(week_date AS date))')
                  .select(
                    'products.l2',
                    'EXTRACT(YEAR FROM CAST(week_date AS date)) AS year',
                    'EXTRACT(MONTH FROM CAST(week_date AS date)) AS month',
                    'COUNT(*) AS product_count',
                    'COUNT(DISTINCT products.sku) AS unique_sku_count'
                  )

    data = {}
    counts.each do |record|
      l2 = record.l2
      year = record.year.to_i
      month = record.month.to_i

      next if year.zero? || month.zero?

      quarter_for_record = case month
                           when 1..3 then :q1
                           when 4..6 then :q2
                           when 7..10 then :q3
                           when 11..12 then :q4
                           else next
                           end

      data[l2] ||= { years: {} }
      data[l2][:years][year] ||= {}
      data[l2][:years][year][quarter_for_record] = record.product_count
      data[l2][:years][year]["#{quarter_for_record}_sku"] = record.unique_sku_count
    end

    # Filter out categories with product count < 100 in the specified quarter
    if quarter.present?
      data.select! do |l2, data_hash|
        valid = true
        data_hash[:years].each do |year, quarters|
          count = quarters[quarter] || 0
          if count > 0 && count < 100
            valid = false
            break
          end
        end
        valid
      end
    end

    all_quarters = [:q1, :q2, :q3, :q4]
    first_non_zero_quarter = nil
    min_year = data.values.map { |h| h[:years].keys.min }.min
    if min_year
      all_quarters.each do |q|
        has_data = data.any? do |l2, data_hash|
          data_hash[:years][min_year] && (data_hash[:years][min_year][q] || 0) > 0
        end
        if has_data
          first_non_zero_quarter = q
          break
        end
      end
    end

    data.each do |l2, data_hash|
      years = data_hash[:years].keys.sort
      years.each do |year|
        data_hash[:years][year] ||= {}
        all_quarters.each { |q| data_hash[:years][year][q] ||= 0 }
        all_quarters.each { |q| data_hash[:years][year]["#{q}_sku"] ||= 0 }
      end
    end

    results = data.map do |l2, years_data|
      quarters_output = {}
      all_percents = []

      years_data[:years].sort.each do |year, quarters|
        quarters_output[year.to_s] = {}
        prev = nil

        quarters_to_process = if year == min_year && first_non_zero_quarter
                                index = all_quarters.index(first_non_zero_quarter)
                                all_quarters[index..-1]
                              else
                                all_quarters
                              end

        quarters_to_process.each do |q|
          curr = quarters[q]
          percent = if prev.nil? || prev == 0
                      0.0
                    else
                      (((curr - prev) * 100.0) / prev).round(2)
                    end

          all_percents << percent if !prev.nil? && prev > 0 && percent > 0

          quarters_output[year.to_s][q.to_s] = {
            value: curr,
            percent: percent,
            previous_count: prev || 0
          }

          prev = curr
        end
      end

      average_growth = if all_percents.any?
                         max_growth = all_percents.max
                         other_percents = all_percents - [max_growth]
                         other_avg = other_percents.any? ? other_percents.sum / other_percents.size : 0.0
                         (0.5 * max_growth + 0.5 * other_avg).round(2)
                       else
                         0.0
                       end

      {
        category_name: l2,
        quarters: quarters_output,
        average_growth: average_growth
      }
    end

    results = results.reject { |entry| entry[:average_growth] == 0.0 }

    results.sort_by { |entry| -entry[:average_growth] }.first(limit)
  end
end
