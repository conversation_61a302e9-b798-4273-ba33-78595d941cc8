class Item < ApplicationRecord
  include PgSearch::Model
  include ItemElasticsearchable
  include ItemSearchable
  include Filterable

  # attr_accessor :chain
  # belongs_to :restaurant
  belongs_to :menu
  belongs_to :recent_menu, class_name: 'Menu', optional: true
  has_one :restaurant, through: :menu
  # belongs_to :restaurant # TODO Q: why many items.restaurant_id != menus.restaurant_id?
  has_one :chain, through: :restaurant
  has_one :city, through: :restaurant
  has_one :county, through: :city
  has_one :state, through: :city
  has_one :item_meta_datum
  has_many :regions, through: :city
  # belongs_to :brand
  belongs_to :mturk_menu_result

  belongs_to :normalized_item
  belongs_to :generalized_item
  belongs_to :menu_item
  has_one :item_category, through: :normalized_item
  has_one :item_subcategory, through: :normalized_item

  # has_many :categories, through: :item_category, class_name: 'Category' # TODO
  has_many :categories, through: :restaurant # TODO Q: why not through item_category?
  alias yelp_categories categories

  # delegate :item_subcategory, :item_category, :to => :normalized_item, :allow_nil => true
  # belongs_to :item_subcategory

  has_many :item_ingredients
  has_many :ingredients, -> { distinct }, :through => :item_ingredients

  has_many :item_brands, :dependent => :destroy
  has_many :brands, :through => :item_brands

  has_many :item_flavors, dependent: :destroy
  has_many :flavors, through: :item_flavors

  has_many :report_category_items, :class_name => 'Report::CategoryItem', dependent: :destroy
  has_many :report_categories, class_name: 'Report::Category', :through => :report_category_items

  has_many :report_items, dependent: :destroy
  has_many :reports, through: :report_items, inverse_of: :lead_items

  has_many :item_dashboards, dependent: :destroy # TODO: remove after switch to quarter
  has_many :menu_item_dashboards, through: :item_dashboards # TODO: remove after switch to quarter

  has_many :item_quarters, dependent: :delete_all
  has_many :quarters, :through => :item_quarters

  has_many :open_ai_logs

  has_many :item_yelp_images
  has_many :yelp_images, :through => :item_yelp_images


  has_neighbors :embedding

  belongs_to :standard_menu_item, counter_cache: true

  has_many :item_gpt_ingredients

  #todo: rename to :gpt_ingredients
  has_many :new_gpt_ingredients, :through => :item_gpt_ingredients, source: :gpt_ingredient

  belongs_to :beverage

  # validates :name, presence: true, length: { minimum: 2 }
  # validates_uniqueness_of :name, scope: :menu_id

  validates :price, comparison: { less_than_or_equal_to: 20000 }, allow_blank: true

  delegate :recipe, to: :standard_menu_item, :allow_nil => true

  has_many :item_blacklist_ingredients
  has_many :blacklist_ingredients, :through => :item_blacklist_ingredients

  has_many :item_products
  has_many :products, :through => :item_products

  has_many :item_projects
  has_many :projects, :through => :item_projects

  has_many :lead_scoring_report_items

  has_many :item_lead_items
  has_many :lead_items, :through => :item_lead_items

  has_many :item_food_trends
  has_many :food_trends, :through => :item_food_trends

  has_one :chicken_survey

  has_many :item_adjectives
  has_many :adjectives, :through => :item_adjectives

  normalizes :wine_name, with: ->(wine_name){ wine_name&.strip&.downcase }
  normalizes :beverage_brand, with: ->(beverage_brand){ beverage_brand&.strip&.downcase }
  normalizes :wine_category, with: ->(wine_category){ wine_category&.strip&.downcase }
  normalizes :spirit_category, with: ->(spirit_category){ spirit_category&.strip&.downcase }
  normalizes :beverage_region, with: ->(beverage_region){ beverage_region&.strip&.downcase }


  validates :chain_menu_id, presence: true, :if => :is_chain_menu_item?

  def is_chain_menu_item?
    self.type.to_s == "ChainMenuItem"
  end

  # after_update do
  #   Pinecone::ItemService.new(self).update if pinecone_at && (saved_change_to_name? || saved_change_to_description? || saved_change_to_normalized_item_id?)
  # end
  #
  # after_destroy do
  #   Pinecone::ItemService.new(self).delete if pinecone_at
  # end

  before_save do
    self.name = self.name&.strip
    self.restaurant_id ||= menu&.restaurant_id
  end

  pg_search_scope :pg_search, lambda { |_query, params={}|
    tsearch = {}
    params = params.to_h.with_indifferent_access

    query = _query.gsub(/\s+/, '<=>') if params[:exact_match] == '1'
    tsearch[:dictionary] = "english"  if params[:include_substrings] == '1'
    tsearch[:any_word] = true         if params[:some_words_present] == '1'

    search_params = {
      against: %i[name description],
      query: query || _query
    }
    search_params[:using] = { tsearch: tsearch } if tsearch.present?
    search_params
  }

  pg_search_scope :with_all_ingredients, against: [:name, :description]

  # Item.pg_search('pizza').count
  # Item.where("(to_tsvector('simple', coalesce(items.name::text, '')) || to_tsvector('simple', coalesce(items.description::text, ''))) @@ to_tsquery('simple', ''' ' || 'pizza' || ' ''')").count

  ## ##############################################################
  ## Scopes
  scope :filter_by_city,          ->(city)          { joins(:city).where(cities: { id: city }) }
  scope :filter_by_cities,        ->(cities)        { filter_by_city(cities) }
  scope :filter_by_item_category, ->(item_category) { left_joins(:item_category).where(item_categories: { id: item_category }) }
  scope :filter_by_yelp_category, ->(yelp_category) { left_joins(:categories).where(categories: yelp_category) }
  scope :filter_by_yelp_category_ids, ->(yelp_category) { filter_by_yelp_category(yelp_category) }
  scope :filter_by_frequency,     ->(frequency)     { filter_by_restaurant_count(frequency) }
  scope :filter_by_chain,         ->(chain)         { joins(:chain).where(chains: chain) }

  scope :searchable,              ->                { where(searchable: true) }
  scope :innovation, -> { where(innovation_score: 0.7..) }
  scope :categories, -> (categories=nil) { categories.present? ? where(category: categories) : all }
  scope :with_mention, -> (mention) { where('description ilike ?',"%#{mention}%") }
  scope :with_normalized_item, -> { where.not(:normalized_item => nil) }
  scope :with_restaurant_name, lambda {
    joins(:restaurant).where.not(restaurants: { business_name: [nil, ""] })
  }
  scope :approved, -> { joins(:menu).where(:menus => { :state => "approved" }).with_restaurant_name }
  scope :unapproved, lambda {
    left_outer_joins(:restaurant)
      .where.not(:menus => { :state => "approved" }) # not menu approved
      .or(where(restaurants: { business_name: [nil, ""] })) # or no restaurant name
      .or(where(restaurants: { id: nil })) # or removed restaurant
  }

  scope :with_null_gpt_ingredients, -> { where("EXISTS (SELECT 1 FROM jsonb_array_elements(gpt_ingredients) AS elem WHERE elem->>'name' IS NULL)") }

  scope :with_brands, lambda { |brand_id=nil|
    query_str = <<-SQL
      EXISTS (SELECT 1 FROM item_brands
        INNER JOIN brands ON item_brands.brand_id = brands.id
        WHERE item_brands.item_id = items.id
          AND brands.state = 'approved' __and_brand_id__
      )
    SQL
    query_str.gsub!('__and_brand_id__', brand_id.present? ? "AND item_brands.brand_id = #{brand_id}" : "")
    where(query_str)
  }

  scope :with_ingredients, lambda { |ingredient_id=nil|
    query_str = "EXISTS (SELECT 1 FROM item_ingredients WHERE item_ingredients.item_id = items.id __and_ingredient_id__)"
    query_str.gsub!('__and_ingredient_id__', ingredient_id.present? ? "AND item_ingredients.ingredient_id = #{ingredient_id}" : "")
    where(query_str)
  }

  scope :with_images, lambda {
    # joins(:yelp_images).where(yelp_images: { state: 'approved' }) # return items with duplicates if multiple images
    where("EXISTS (SELECT 1 FROM yelp_images WHERE yelp_images.item_id = items.id AND yelp_images.state = 'approved')")
  }

  scope :by_yelp_category, lambda { |yelp_category_id|
    joins(:categories).where(categories: yelp_category_id)
  }

  scope :by_chains, lambda { |chains|
    # joins(:restaurant).where(restaurant: {chain_id: chain_id})
    joins(:chain).where(chains: chains)
  }

  scope :by_report_category, lambda { |report_category|
    # joins(:restaurant).where(restaurant: {chain_id: chain_id})
    joins(:report_categories).where(report_categories: report_category)
  }

  scope :decorate_with_chain_id, lambda { |column_name = 'chain_id'|
    left_joins(:chain).select("items.*, CASE WHEN chains.id IS NULL THEN NULL ELSE chains.id END AS #{column_name}")
  }

  scope :for_period, lambda { |time_period = nil|
    joins(:menu).where(menus: { year: time_period.year, quarter: time_period.quarter }) if time_period.present?
  }
  scope :for_last_quarter, lambda {
    last_quarter = TimePeriod.approved.quarters.order('end_at desc').first
    for_period(last_quarter)
  }

  scope :entrees, -> { where(:detect_category => "entree") }
  scope :appetizers, -> { where(:detect_category => "appetizer") }
  scope :sushi, -> { where(:detect_category => "sushi") }

  scope :fish, -> { where(:is_fish => true) }
  scope :chicken, -> { where(:is_chicken => true) }

  scope :with_search_term, ->(term){ where("lower(items.name) ~* ? OR lower(items.description) ~* ?", term.to_s.downcase.strip, term.to_s.downcase.strip) }
  scope :ahi, -> { with_search_term("ahi") }
  scope :tuna, -> { with_search_term("tuna") }
  scope :foie_gras, -> { with_search_term("foie gras") }
  scope :salmon, -> { with_search_term("salmon") }
  scope :scallop, -> { with_search_term("scallop") }

  # with chicken survey
  scope :is_chicken_strip, ->{ joins(:chicken_survey).where(:chicken_surveys => { :is_chicken => true, :is_boneless => true }).where.not(:price => [0, nil]) }

  ## ##############################################################
  # all.select("ingredients.*, (SELECT count(i.id) FROM items i WHERE i.id IN (SELECT ii.item_id FROM item_ingredients ii WHERE ii.ingredient_id = ingredients.id) #{item_ids_sql ? "AND i.id IN (#{item_ids_sql})" : ''}) as items_count")

  def self.city(cities = nil)
    if cities.blank?
      return all
    elsif cities.is_a?(String)
      joins(:restaurant => [:city]).where("lower(cities.name) = ?", cities.downcase)
    else
      joins(:restaurant => [:city]).where(cities: cities)
    end
  end

  def self.with_duplicates(item_id)
    item = Item.approved.includes(:restaurant).find(item_id)
    Item.joins(:restaurant)
        .where(name: item.name)
        .where(restaurants: { business_name: item.restaurant.business_name })
  end

  def self.to_csv
    items = all
    items = items.includes(menu: [restaurant: [:city]])
    items = items.includes(%i[item_category city restaurant])

    headers = ['Restaurant Name','Street Address','City','State', 'Zip','Category', 'Menu Item Name', 'Description','Price','Locations']
    csv_data = CSV.generate(headers: true) do |csv|
      csv << headers
      items.each do |item|
        next if item.menu.nil?
        next if item.menu.restaurant.nil?

        csv << item.csv_row_data
      end
    end
    return csv_data
  end

  def self.top_item_categories
    # Item.pg_search('latte').top_item_categories2.limit(5).
    # select_str = 'normalized_items.item_category_id, count(items.id) as items_count'
    # subquery = joins(:normalized_item).group('normalized_items.item_category_id').select(select_str).reorder(nil).distinct # fix for pg_search scope
    # ItemCategory.approved
    #             .joins("JOIN ( #{subquery.to_sql} ) AS i_subquery ON item_categories.id = i_subquery.item_category_id")
    #             .select("item_categories.*, i_subquery.items_count")
    #             .order('items_count desc')

    item_ids_sql = all.select(:id).to_sql
    ItemCategory.approved.with_items_count2(item_ids_sql).order('items_count desc')
  end

  def self.unique_restaurants_count
    joins(:restaurant)
      .reorder(nil)
      .group("COALESCE(''||restaurants.chain_id, restaurants.business_name)")
      .count
      .size # Returns the number of unique groups
  end

  def self.percent_of_unique_restaurants
    unique_restaurants_count / Restaurant.with_approved_menus.unique_restaurants_total.to_f
  end # Item.searchable.pg_search('pizza').for_last_quarter.percent_of_unique_restaurants

  def csv_row_data
    [
      restaurant.business_name,
      restaurant.address,
      city&.name,
      restaurant.state_name,
      restaurant.zip_code,
      (item_category&.name || category).to_s.downcase,
      name.to_s.downcase,
      description,
      price ? price.to_f.round(2) : "",
      restaurant_chain_count
    ]
  end

  def auto_create_brand_ingredients
    # Ai::CreateBrandsAndIngredientsWorker.perform_async(id)
  end

  def auto_create_flavors(async: true)
    Ai::ItemAutoCreateFlavorsWorker.perform_async(id)   if async
    ItemFlavorTagsAutoCreateService.new(item: self).perform unless async
  end

  def auto_set_innovation_score
    Ai::SetItemInnovationWorker.perform_async(id) if name.present?
  end

  def auto_set_fusion_score
    Ai::SetItemFusionWorker.perform_async(id) if name.present?
  end

  def auto_categorize(async: true, force: false)
    return if ItemCategory.valids.where(id: item_category).present? && !force

    Ai::ItemAutoCategorizeWorker.perform_async(id)  if async
    SetItemCategoryService.new(item: self).perform      unless async
  end

  def frequency
    case chain.restaurants_count.to_i
    when 100.. then '100+ locations'
    when 10..100 then '10-100 locations'
    else '1-10 locations'
    end
  end

  def set_embedding
    begin
      o = OpenAi::Embedding.new
      text = summary
      emb = o.text2embedding(text)
      self.embedding = emb[:embedding]
      self.save
    rescue StandardError => error
      pp error.message
    end
  end

  def set_gpt_ingredients(client_name: "open_ai")
    Ai::ItemGptIngredientsWorker.new.perform(id, false, client_name)
  end

  def predict_products(retailer_id: nil, client_name:nil)
    new_gpt_ingredients.each do |ingredient|
      Ai::ItemPredictWorker.new.perform(id, ingredient.id, retailer_id, client_name)
    end
  end

  def self.convert_to_embeddings(items=Item.none)
    items.each do |item|
      begin
        o = OpenAi::Embedding.new
        text = item.summary
        emb = o.text2embedding(text)
        item.embedding = emb[:embedding]
        item.save
      rescue StandardError => error
        pp error.message
      end
    end
  end

  def summary
    text = <<-TEXT
NAME: #{name}
DESCRIPTION: #{description}
    TEXT
    text.to_s.downcase.strip.first(5000)
  end

  def display_gpt_ingredients
    item_gpt_ingredients.map do |igi|
      name = igi.gpt_ingredient&.name
      next if name.blank?
      {
        name: name,
        confidence_score: igi.confidence_score
      }
    end.compact.uniq
  end

  # finds the closest items by embeddings
  def related_items_by_embed(limit: 5)
    nearest_neighbors(:embedding, distance: "cosine").first(limit)
  end

  def convert_to_new_gpt_ingredients
    return if gpt_ingredients.nil?
    gpt_ingredients.each do |ing|
      g = GptIngredient.find_or_create_by(:name => ing["name"])
      ig = ItemGptIngredient.find_or_create_by(:gpt_ingredient_id => g.id, :item_id => self.id)
      ig.confidence_score = ing["confidence_score"]
      ig.save
    end
  end

  def self.search_by_emb(text="text")
    o = OpenAi::Embedding.new
    emb = o.text2embedding(text.to_s.downcase.strip)
    text_embedding = emb[:embedding]
    Item.nearest_neighbors(:embedding, text_embedding, distance: "cosine").first(20)
  end

  def beverage_year=(val)
    if val.to_s.length < 4
      val = nil
    end
    super(val)
  end

  def found_lead_items
    lead_items.pluck(:name).join(", ")
  end

  def generalized_menu_item
    standard_menu_item&.name.to_s.downcase.strip
  end

  def menu_link
    "/restaurant_menu/#{menu&.id}?category=#{item_category&.id || 0}#item_#{id}"
  end

  def self.es_optimize_index
    Item.__elasticsearch__.client.indices.put_settings index: Item.index_name, body: {
      index: {
        refresh_interval: -1, # Disable automatic refreshes to improve performance during bulk import
        number_of_replicas: 0 # Reduce the number of replicas to speed up the indexing process
      }
    }
  end

  def extract_ingredients_async
    Items::IngredientExtractorWorker.perform_async(id)
  end

  def image_url=(value)
    if value.to_s == "[nil]"
      value = nil
    end
    super(value)
  end



end
