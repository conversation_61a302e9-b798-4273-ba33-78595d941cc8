class GmiCategory < ApplicationRecord
  include Quarterable

  has_many :gmi_subcategories
  has_many :menu_items
  has_many :quarters, as: :quarterable

  validates :name, presence: true, uniqueness: true

  normalizes :name, with: ->(name){ name&.strip&.downcase }

  def calc_stats
    quarters.each do |q|
      menu_quarters = Quarter.joins(:time_period).where(:time_periods => {:quarter => q.quarter, :year => q.year}).where(quarterable_id: menu_items.pluck(:id), quarterable_type: "MenuItem")
      q.pen_rate = menu_quarters.average(:pen_rate)
      q.delta_last_period = menu_quarters.average(:delta_last_period)
      q.save
    end
  end

end
