class MenuItem < ApplicationRecord
  include Uuidable
  include Quarterable

  belongs_to :gmi_category
  belongs_to :gmi_subcategory

  has_one :menu_item_dashboard

  has_many :quarters, as: :quarterable

  normalizes :name, with: ->(name){ name&.strip&.downcase }
  normalizes :category, with: ->(category){ category&.strip&.downcase }
  normalizes :subcategory, with: ->(subcategory){ subcategory&.strip&.downcase }

end
