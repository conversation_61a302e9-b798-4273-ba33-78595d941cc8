class MenuItem < ApplicationRecord
  include Uuidable
  include Quarterable

  belongs_to :gmi_category
  belongs_to :gmi_subcategory

  has_one :menu_item_dashboard

  has_many :quarters, as: :quarterable
  has_many :items

  normalizes :name, with: ->(name){ name&.strip&.downcase }
  normalizes :category, with: ->(category){ category&.strip&.downcase }
  normalizes :subcategory, with: ->(subcategory){ subcategory&.strip&.downcase }

  def calc_stats
    quarters.each do |q|
      q.calc_menu_items
    end
    q4 = quarters.last

    self.pen_rate = q4.pen_rate
    self.foodservice_growth = q4.delta_last_period

    # todo: Remove this an replace with accurate prediction
    self.foodservice_prediction = (q4.delta_last_period.to_f * 1.05).to_f.clamp(0,100) * [1, -1].sample
    save
  end

  def pen_rate_over_time
    last_quarters = quarters.joins(:time_period).where(:time_period => TimePeriod.last_year)
    last_quarters.map do |q|
      {
        quarter: q.quarter,
        year: q.year,
        pen_rate: q.pen_rate.to_f.round(2),
        foodservice_growth: q.delta_last_period.to_f.round(2)
      }
    end
  end

end
