class Customer < ApplicationRecord
  ACCESS_FEATURE_OPTIONS = %w[search ingredients brands saved_reports reports dashboards contents grocery semantic_search lead_scoring_reports top_chains dole_reports].sort.freeze

  has_many :users
  has_many :access_locations
  has_many :menu_item_dashboards
  has_many :menu_category_boards

  has_many :customer_reports
  has_many :reports, through: :customer_reports

  has_many :projects

  has_many :auth_secrets

  has_many :lead_scoring_reports

  has_and_belongs_to_many :countries, :join_table => :countries_customers

  has_one :lead_scoring_item_list

  validates :name, presence: true, uniqueness: true
  validates :company_profile, length: { maximum: 50000 }

  accepts_nested_attributes_for :access_locations,
                                :reject_if => proc { |attributes| attributes['locationable_type'].blank? || attributes['locationable_id'].to_i.zero? },
                                :allow_destroy => true

  before_save do
    self.access_features = (access_features || []) & ACCESS_FEATURE_OPTIONS
  end

  def self.hf
    find_by(name: 'Honolulu Fish')
  end


  def access_regions
    regions = Region.all
    regions = regions.where(country: countries) if countries.present?
    regions.by_access_locations(access_locations)
  end

  def access_region?(region_id)
    if region_id == 'us'
      access_locations.blank? && countries.where(abbrev: 'us').exists?
    else
      access_regions.where(id: region_id).exists?
    end
  end

  def access_cities
    cities = City.all
    cities = cities.where(country: countries) if countries.present?
    cities.by_access_locations(access_locations)
  end

  def access_states
    states = State.all
    states = states.where(cities: access_cities)
    states
  end

  def accessible_reports
    Report.left_outer_joins(:customer_reports).where('customer_reports.customer_id = ? OR customer_reports.customer_id IS NULL', id)
  end

  def locations_attrs
    access_locations.map(&:locationable).compact.map(&:as_string)
  end

  def set_auth_secret
    return if auth_secrets.count > 0
    AuthSecret.create(:customer_id => id)
  end

  def add_default_access_features
    default_features = ["search", "ingredients"]
    default_features.each do |feature|
      access_features.push(feature)
    end
    save
  end

  attr_accessible :company_profile if defined?(attr_accessible)
end
