class Ingredient < ApplicationRecord
  include PgSearch::Model
  include Filterable
  include Uuidable
  include IngredientAnalytics
  include CustomerInsights

  has_many :item_ingredients, dependent: :delete_all
  has_many :items, through: :item_ingredients

  has_many :ingredient_dashboards, dependent: :destroy
  # has_many :menu_item_dashboards, through: :ingredient_dashboards

  has_many :ingredient_menu_categories
  has_many :menu_categories, through: :ingredient_menu_categories
  has_many :google_trends, dependent: :destroy

  belongs_to :menu_item_dashboard

  has_one :retail_insights_dashboard
  has_many :quarters, through: :retail_insights_dashboard
  has_many :products, through: :quarters

  has_many :favorites
  has_many :favorited_by, through: :favorites, source: :user

  has_many :consumer_sentiment_likes, dependent: :destroy
  has_many :consumer_sentiment_familiarities, dependent: :destroy
  has_many :ingredient_age_breakdowns, dependent: :destroy
  has_many :ingredient_ethnicity_breakdowns, dependent: :destroy

  validates :name, uniqueness: true

  has_neighbors :embedding

  normalizes :name, with: ->(name) { name&.strip&.downcase }
  normalizes :lifecycle, with: ->(lifecycle) { lifecycle&.strip&.downcase }
  normalizes :mac, with: ->(mac) { mac&.strip&.downcase }
  normalizes :taste, with: ->(taste) { taste&.strip&.downcase }
  normalizes :aroma, with: ->(aroma) { aroma&.strip&.downcase }
  normalizes :texture, with: ->(texture) { texture&.strip&.downcase }
  normalizes :food_type, with: ->(food_type) { food_type&.strip&.downcase }
  normalizes :food_type_category, with: ->(food_type_category) { food_type_category&.strip&.downcase }
  normalizes :snack_need_state, with: ->(snack_need_state) { snack_need_state&.strip&.downcase }
  # normalizes :snack_need_state_reason, with: ->(snack_need_state_reason){ snack_need_state_reason&.strip&.downcase }
  normalizes :beverage_need_state, with: ->(beverage_need_state){ beverage_need_state&.strip&.downcase }
  # normalizes :beverage_need_state_reason, with: ->(beverage_need_state_reason){ beverage_need_state_reason&.strip&.downcase }
  normalizes :frozen_smoothies_need_state, with: ->(frozen_smoothies_need_state){ frozen_smoothies_need_state&.strip&.downcase }
  # normalizes :frozen_smoothies_need_state_reason, with: ->(frozen_smoothies_need_state_reason){ frozen_smoothies_need_state_reason&.strip&.downcase }
  normalizes :frozen_novelties_need_state, with: ->(frozen_novelties_need_state){ frozen_novelties_need_state&.strip&.downcase }
  # normalizes :frozen_novelties_need_state_reason, with: ->(frozen_novelties_need_state_reason){ frozen_novelties_need_state_reason&.strip&.downcase }


  PDI_CATEGORIES = ["beans", "dairy", "fat", "fats/oil", "fortifier", "fortifiers", "fruit", "fruits", "grains", "herbs", "herbs/spices", "meat", "not applicable", "nut/seed/grain", "nuts/seeds/grains", "other", "seafood", "spice", "stabilizers/emulsifiers", "sweetener", "sweeteners", "vegetable", "vegetables", "vegetables/fruit"]
  TASTE_OPTIONS = ["acidic", "aesthetic", "alcoholic", "aromatic", "austere", "authentic", "bergamot", "beyond taste", "bitter", "botanical", "briny", "bubbly", "buttery", "citrusy", "clean", "compressed", "creamy", "crisp", "crispy", "drive thry only", "dry", "earthy", "fad", "floral", "fresh", "fruity", "functional", "grassy", "herbal", "item as flavor", "last", "lemony", "malty", "mellow", "mild", "mildly bitter", "minty", "n/a", "neutral", "no artificial", "no sugar added", "no taste", "none", "not applicable", "nutrient-dense", "nutty", "old", "peer", "peppery", "personalized", "piney", "pungent", "refreshing", "rich", "robust", "salty", "savory", "sharp", "simple", "slightly smoky", "slightly sour", "smoky", "sober", "sour", "spicy", "starchy", "strong", "sweet", "tangy", "tart", "toasted", "umami", "unknown", "uplifting", "yeasty", "zesty"]
  AROMA_OPTIONS = ["aromatic", "balsamic", "beany", "beefy", "bergamot", "botanical", "bready", "briny", "bubbly", "buttery", "caramelized", "carbonated", "chocolate", "chocolatey", "chocolaty", "cinnamony", "citrusy", "coffee-like", "complex", "creamy", "crisp", "crystalline", "delicate", "earthy", "fermented", "fishy", "flash", "floral", "fluffy", "fragrant", "fresh", "fruity", "functional", "garlicky", "ginger", "glistening", "graham cracker", "grassy", "green", "herbaceous", "herbal", "licorice", "light", "malty", "marshmallow", "marshmallowy", "meaty", "mellow", "mild", "minty", "musky", "n/a", "neutral", "no aroma", "no artificial", "none", "not applicable", "nutty", "oaky", "oceanic", "peppery", "porky", "pungent", "refreshing", "rich", "roasted", "robust", "savory", "sensery driven", "slightly fruity", "slightly sweet", "smoky", "sour", "spicy", "subtle", "sugary", "sweet", "tangy", "tart", "toasted", "tropical", "umami", "unknown", "vegetal", "woodsy", "woody", "zesty"]
  TEXTURE_OPTIONS = ["aromatic", "brittle", "bubbly", "cakey", "carbonated", "charred", "chewy", "coarse", "cotton-like", "creamy", "crisp", "crispy", "crumbly", "crunchy", "crusty", "crystalline", "delicate", "dense", "dried", "effervescent", "fibrous", "fine", "firm", "fizzy", "flaky", "fluffy", "fudgy", "functional", "glazed", "glistening", "glossy", "gooey", "grainy", "granular", "gritty", "gummy", "icy", "juicy", "light", "luscious", "meaty", "mellow", "melting", "moist", "n/a", "no artificial", "none", "not applicable", "oily", "plump", "powdery", "refreshing", "rich", "saucy", "sharp", "silky", "sizzling", "slightly crunchy", "slimy", "slurpy", "smooth", "soft", "spongy", "starchy", "sticky", "succulent", "syrupy", "tangy", "tart", "tender", "textured", "thick", "thin", "trend", "umami", "unknown", "velvety", "vibrant", "viscous", "watery", "zesty"]
  APPEARANCE_OPTIONS = ["aromatic", "brittle", "bubbly", "cakey", "carbonated", "charred", "chewy", "coarse", "cotton-like", "creamy", "crisp", "crispy", "crumbly", "crunchy", "crusty", "crystalline", "delicate", "dense", "dried", "effervescent", "fibrous", "fine", "firm", "fizzy", "flaky", "fluffy", "fudgy", "functional", "glazed", "glistening", "glossy", "gooey", "grainy", "granular", "gritty", "gummy", "icy", "juicy", "light", "luscious", "meaty", "mellow", "melting", "moist", "n/a", "no artificial", "none", "not applicable", "oily", "plump", "powdery", "refreshing", "rich", "saucy", "sharp", "silky", "sizzling", "slightly crunchy", "slimy", "slurpy", "smooth", "soft", "spongy", "starchy", "sticky", "succulent", "syrupy", "tangy", "tart", "tender", "textured", "thick", "thin", "trend", "umami", "unknown", "velvety", "vibrant", "viscous", "watery", "zesty"]

  include AASM
  aasm :column => 'state' do
    state :scanned, initial: true
    state :itemized
    state :approved
    state :failed

    event :approve do
      transitions from: :itemized, to: :approved
    end

    event :reject do
      transitions from: :itemized, to: :failed
    end

  end

  # scope :with_items_count0, lambda { |item_ids = nil|
  #   all.select("ingredients.*, (SELECT count(i.id) FROM items i WHERE i.id IN (SELECT ii.item_id FROM item_ingredients ii WHERE ii.ingredient_id = ingredients.id) #{item_ids ? "AND i.id IN (#{item_ids.join(',')})" : ''}) as items_count")
  # }

  scope :with_items_count, lambda { |item_ids = nil|
    # brands = brands.joins(:items => :menu).where(:menus => { :state => "approved" })
    ingredients = all.joins(:item_ingredients) # left_outer_joins
    ingredients = ingredients.where(item_ingredients: { item_id: item_ids }) if item_ids.present?
    ingredients = ingredients.select('ingredients.*, count(item_ingredients.id) as items_count')
    ingredients = ingredients.group('ingredients.id')
    ingredients = ingredients.order(items_count: :desc)
  }

  scope :search_by_name, lambda { |name = ''|
    search_str = Utility.to_ts_search_str(name)
    select("ingredients.*")
      .select("ts_rank(to_tsvector(name), to_tsquery('#{search_str}')) AS rank")
      .where("to_tsvector(name) @@ to_tsquery('#{search_str}')")
      .order("rank desc")
  }

  scope :is_pairing_trend, -> { where(is_pairing_trend: true) }

  scope :by_menu_adoption, ->(menu_adoption) do
    select('
      ingredients.guid,
      ingredients.name,
      ingredients.menu_adoption,
      ingredients.pen_rate as penetration,
      ingredients.delta_last_period as change,
      ingredients.created_at,
      carousel_images.url as image_url
    ')
    .with_image
    .where(menu_adoption: menu_adoption)
    .order(pen_rate: :asc)
    .limit(10)
  end

  scope :by_innovation, ->() do
    select('
      ingredients.guid,
      ingredients.name,
      ingredients.pen_rate as penetration,
      ingredients.delta_last_period as change,
      ingredients.pen_rate_1y,
      ingredients.created_at,
      carousel_images.url as image_url
    ')
    .with_image
    .joins(:menu_item_dashboard)
    .where('ingredients.pen_rate > 1')
    .where('ingredients.pen_rate_1y IS NOT NULL')
    .order('menu_item_dashboards.last_trend_value DESC')
    .limit(5)
  end

  scope :with_image, -> {
  joins(<<-SQL)
    LEFT JOIN LATERAL (
      SELECT url
      FROM carousel_images
      WHERE carousel_images.menu_item_dashboard_id = ingredients.menu_item_dashboard_id
      ORDER BY carousel_images.created_at ASC
      LIMIT 1
    ) carousel_images ON true
  SQL
  }

  scope :filter_by_favorite, lambda { |user_id, favorite_value|
    case favorite_value
    when "true"
      joins(:favorites).where(favorites: { user_id: user_id })
    when "false"
      where.not(id: Favorite.select(:ingredient_id).where(user_id: user_id))
    else
      all
    end
  }

  scope :filter_by_category, lambda { |menu_category_name_list|
    joins(:menu_categories).where(menu_categories: { name: menu_category_name_list })
  }

  scope :filter_by_menu_adoption, lambda { |menu_adoption_list|
    where(menu_adoption: menu_adoption_list)
  }

  scope :filter_by_aroma, lambda { |aroma_list|
    where(aroma: aroma_list)
  }

  scope :filter_by_texture, lambda { |texture_list|
    where(texture: texture_list)
  }

  scope :filter_by_taste, lambda { |taste_list|
    where(taste: taste_list)
  }

  scope :filter_by_appearance, lambda { |appearance_list|
    where(appearance: appearance_list)
  }

  delegate :growth_percent_display, to: :menu_item_dashboard, allow_nil: true
  delegate :assign_items, to: :menu_item_dashboard, allow_nil: true
  delegate :carousel_images, to: :menu_item_dashboard, allow_nil: true
  delegate :retails_carousel_images, to: :menu_item_dashboard, allow_nil: true

  def chart_data(starting = 2.months.ago)
    results = items.joins(:item_category).includes(normalized_item: [:item_category])#.where(created_at: starting..Time.current)
    total = results.count.to_f

    category_hash = {}
    results.each do |result|
      normalized_item = result.normalized_item
      category_name = normalized_item&.item_category&.name&.downcase.presence
      category_name = "uncategorized" if !category_name || category_name == "#value!"
      category_hash[category_name] ||= 0.0
      category_hash[category_name] += 1
    end

    pie_hash = {}
    category_hash.each do |k, v|
      pie_hash[k] ||= (v / total * 100).round(2)
    end
    Hash[pie_hash.sort_by{|k, v| v}.reverse] # order by value
  end

  def merge(duplicates)
    ActiveRecord::Base.transaction do
      duplicates.each do |duplicate|
        items << duplicate.items
      end
      self.name = duplicates.map(&:name).compact.last
      self.description = duplicates.map(&:description).map(&:presence).compact.last
      duplicates.each(&:destroy)
      save
    end
  end

  def self.uniq_from_attr(attr="texture")
    all.map{|i|i.send(attr.to_sym).split(",").map{|t|t.to_s.strip.downcase}}.flatten.uniq.sort
  end

  def init_menu_item_dashboard
    return if menu_item_dashboard_id.present?
    m = MenuItemDashboard.find_or_create_by(query: name)
    m.name = name if m.name.blank?
    m.state = "approved"
    m.enabled = true
    m.save
    m.set_dashboard_cache
    self.menu_item_dashboard_id = m.id
    save
  end

  def init_retail_insight_dashboard
    return if retail_insights_dashboard.present?
    r = RetailInsightsDashboard.find_or_create_by(query: name.to_s.downcase.strip, name: name.to_s.downcase.strip)
    r.ingredient_id = id
    r.save
    r.set_growth_percent
  end

  def update_growth_percent
    return if growth_percent_display.blank?
    self.growth_percent = growth_percent_display
    save
  end

  def fetch_and_store_trends(async: false)
    Ingredients::FetchAndStoreTrendsWorker.perform_async(id) and return if async
    trends_data = Serp::Api.new.google_trend(name)
    google_trends.create(yearly_trends: trends_data) if trends_data.present?
  rescue StandardError => e
    Rails.logger.error "Error fetching trends for #{name}: #{e.message}"
  end

  def google_trend
    menu_item_dashboard&.last_trend_value&.to_f
  end

  def set_metrics
    last_q = menu_item_dashboard&.quarters&.order('quarters.time_period_id')&.last
    return if last_q.nil?
    self.pen_rate = last_q.pen_rate
    self.menu_adoption = last_q.menu_adoption.to_s
    self.delta_last_period = last_q.delta_last_period
    self.change_1y = last_q.change_1y
    save
  end

  def init_pairing_trends
    Ingredient.is_pairing_trend.order(:name).find_each do |b|
      next if id == b.id
      IngredientPairingTrend.find_or_create_by(:ingredient_a_id => id, :ingredient_b_id => b.id)
    end
  end

  def ingredient_pairing_trends(limit = 10)
    trends = IngredientPairingTrend
               .where("ingredient_a_id = :id OR ingredient_b_id = :id", id: id.to_s)
               .order(pairing_percent: :desc)
               .limit(limit)

    other_ids = trends.map { |trend| trend.ingredient_a_id == id ? trend.ingredient_b_id : trend.ingredient_a_id }

    ingredients = Ingredient.where(id: other_ids).index_by(&:id)

    trends.map do |trend|
      other_id = trend.ingredient_a_id == id ? trend.ingredient_b_id : trend.ingredient_a_id
      ingredient = ingredients[other_id]
      next unless ingredient

      {
        guid: ingredient.guid,
        name: ingredient.name,
        pairing_percent: trend.pairing_percent,
        pairing_count: trend.pairing_count
      }
    end.compact
  end

  def self.most_liked_ingredients
    most_liked_videos = TikTokVideo.most_liked
    Ingredient.where(id: most_liked_videos.keys).map do |ingredient|
      {
        ingredient: ingredient,
        like_percentage: most_liked_videos[ingredient.id]
      }
    end
  end

  def pen_rate_over_time
    return [] if menu_item_dashboard.nil?
    return [] if menu_item_dashboard.quarters.empty?
    last_quarters = menu_item_dashboard.quarters.joins(:time_period).where(:time_period => TimePeriod.last_year)
    last_quarters.map do |q|
      {
        quarter: q.quarter,
        year: q.year,
        pen_rate: q.pen_rate.to_f.round(2),
        growth: q.delta_last_period.to_f.round(2),
        change_1y: q.change_1y
      }
    end
  end

  def top_categories_chart_data(limit: 7, level: 'l1', store_ids: nil)
    products = products_pg_search.where.not(sku: nil)
    products = products.where.not(level => [nil, '', ' '])
    products = products.where(store_id: store_ids) if store_ids.present?

    grouped_products = products.unscope(:order).group("products.#{level}")
                               .select(
                                 "products.#{level} AS category_name",
                                 "COUNT(DISTINCT products.sku) AS unique_skus",
                                 "COUNT(products.id) AS products_count"
                               )
                               .order(products_count: :desc)
                               .limit(limit)

    grouped_products.map do |category|
      {
        id: category.category_name,
        category: category.category_name,
        unique_skus: category.unique_skus,
        products_count: category.products_count
      }
    end
  end

  def retail_growth(unique_skus: true)
    current_year = Date.today.year
    years = [current_year - 1, current_year]
    products = products_pg_search.where.not(sku: nil)
    products = products.where(year: years)
    counts = products.unscope(:order).group(:year, :quarter)
                     .select(
                       'products.year',
                       'products.quarter',
                       unique_skus ? 'COUNT(DISTINCT products.sku) AS unique_sku_count' : 'COUNT(products.id) AS unique_sku_count'
                     )

    data = {}
    counts.each do |record|
      year = record.year.to_i
      quarter = record.quarter.to_i
      next if year.zero? || quarter.zero?
      quarter_name = "Q#{quarter}"
      data[year] ||= {}
      data[year][quarter_name] ||= 0
      data[year][quarter_name] += record.unique_sku_count
    end

    quarters_with_data = []
    data.each do |year, quarters|
      quarters.each do |quarter, count|
        next if count.zero?
        quarters_with_data << { name: "#{quarter} #{year}", count: count }
      end
    end
    quarters_with_data.sort_by! { |q| [q[:name].split.last.to_i, q[:name].split.first[1].to_i] }

    avg_growth = if quarters_with_data.length >= 2
                   first_count = quarters_with_data.first[:count].to_f
                   last_count = quarters_with_data.last[:count].to_f
                   ((last_count - first_count) / first_count * 100).round(2)
                 else
                   0
                 end

    update(growth_percent: avg_growth) if avg_growth != growth_percent

    {
      total_count: quarters_with_data.sum { |q| q[:count] },
      quarters: quarters_with_data,
      growth_percent: avg_growth,
      growth_prediction: growth_prediction
    }
  end

  def products_pg_search(params: {})
    Product.pg_search(name, params.merge(exact_match: '1'))
  end

  def product_images(count: 1, random: false, product_limit: 50)
    products = products_pg_search
                 .where.not(sku: nil)
                 .where.not(year: nil)
                 .where.not(quarter: nil)
                 .reorder('products.sku')
                 .select('DISTINCT ON (products.sku) products.*')

    return [] if products.empty?

    if random && count > 1
      unique_sku_product_ids = products.pluck(:id).sample(product_limit)
      random_products = Product.where(id: unique_sku_product_ids)

      images = Image
                 .joins(:product)
                 .where(products: { id: random_products.select(:id) })
                 .select('DISTINCT ON (images.product_id) images.url, products.name, products.listing_url, images.created_at')
                 .order('images.product_id, images.created_at DESC')
                 .limit(count)

      images.map do |image|
        {
          url: image.url,
          name: image.name,
          listing_url: image.listing_url
        }
      end
    else
      product = products.order('products.created_at DESC').first
      return [] unless product

      image = product.images.order(created_at: :desc).first
      return [] unless image

      [{
        url: image.url,
        name: product.name,
        listing_url: product.listing_url
      }]
    end
  end

end
