class MenuItemDashboard < ApplicationRecord
  include PgSearch::Model

  pg_search_scope :search_by_cuisine, against: [:cuisine_type]
  pg_search_scope :search_by_name, against: [:name, :query]


  include Filterable
  include DashboardNewsFetchable
  include Embeddable

  HOVER_ATTRIBUTES_OPTIONS = %w[average median].freeze

  belongs_to :customer, optional: true

  has_many :quarters, dependent: :destroy
  has_one :last_quarter, -> { order(id: :desc) }, class_name: 'Quarter'

  # has_many :total_items, through: :quarters, source: :items
  # has_many :items_ingredients, through: :total_items, source: :ingredients
  # has_many :item_quarters, through: :quarters

  # has_many :item_dashboards, dependent: :delete_all # TODO: remove after switch to quarter
  # has_many :items, through: :item_dashboards # TODO: remove after switch to quarter
  #
  # has_many :menus, through: :items
  # has_many :items_ingredients, through: :items, source: :ingredients
  # has_many :restaurants, through: :items, source: :restaurant
  # has_many :categories, through: :restaurants

  has_many :ingredient_dashboards, dependent: :delete_all
  has_many :ingredients, through: :ingredient_dashboards

  has_many :menu_item_dashboard_projects
  has_many :projects, :through => :menu_item_dashboard_projects

  has_many :menu_mentions_region_datas, dependent: :delete_all

  has_many :yelp_images, :through => :items
  has_many :google_trends, dependent: :destroy

  has_many :top_chains_data_rows
  has_many :menu_mention_data_rows

  has_many :dashboard_chains
  has_many :chains, through: :dashboard_chains

  has_many :dashboard_emerging_chains

  has_many :carousel_images

  has_many :geo_prices

  has_many :social_conversations

  belongs_to :standard_menu_item

  has_one :ingredient
  belongs_to :menu_item

  belongs_to :menu_category_board
  belongs_to :menu_subcategory_board

  validates :name, presence: true, uniqueness: { scope: [:customer_id, :ingredient_id] }
  validates :query, uniqueness: { scope: [:customer_id, :ingredient_id] }, allow_blank: true

  has_many :specific_ingredients, :class_name => "Ingredient"

  validates :guid, :uniqueness => true, :allow_nil => true

  has_neighbors :embedding



  normalizes :name, with: ->(name){ name&.strip&.downcase }
  normalizes :query, with: ->(query){ query&.strip&.downcase&.remove_special }
  normalizes :cuisine_type, with: ->(cuisine_type){ cuisine_type&.strip&.downcase }
  normalizes :meal_type, with: ->(meal_type){ meal_type&.strip&.downcase }

  MEAL_TYPES = ["alcoholic beverage", "appetizers", "beverages", "breakfast", "desserts", "dinner", "lunch", "side dishes"].freeze
  CUISINE_TYPES = ["american", "asian fusion", "chinese", "french", "greek", "indian", "italian", "japanese", "korean", "kosher", "latin american", "mediterranean", "mexican", "middle eastern", "pizza", "spanish", "sushi bar", "vegan"].freeze

  after_create do
    # TODO: Q: add status to show that it's in progress
    MenuItemDashboards::AssignItemsWorker.perform_async(id, 'create')
    set_guid
    # compute_segment_penetration_rates
  end



  scope :enabled, -> { where(enabled: true) }
  scope :demo, -> { where(demo: true) }

  scope :filter_by_top_cuisine, ->(top_cuisine) { where(top_cuisine: top_cuisine) }
  scope :filter_by_meal_type, ->(meal_type) { where(meal_type: meal_type) }
  scope :filter_by_cuisine_type, ->(cuisine_type) { where(cuisine_type: cuisine_type) }

  def self.decorated_with_menu_mentions(cities: nil, relation: nil)
    decorated_columns = %w[menu_mentions chain_menu_mentions independent_menu_mentions]

    subquery_sql = <<~SQL
      SELECT
        q.menu_item_dashboard_id,
        count(DISTINCT i.search_uniq_key) as menu_mentions,
        count(DISTINCT CASE WHEN r.chain_id IS NOT NULL THEN i.search_uniq_key ELSE NULL END) as chain_menu_mentions,
        count(DISTINCT CASE WHEN r.chain_id IS NULL THEN i.search_uniq_key ELSE NULL END) as independent_menu_mentions
      FROM quarters q
        JOIN item_quarters iq ON q.id = iq.quarter_id
        JOIN items i ON iq.item_id = i.id
        JOIN restaurants r ON i.restaurant_id = r.id
      WHERE iq.excluded != TRUE
        __filter_by_cities__
        __filter_by_relation__
      GROUP BY q.menu_item_dashboard_id
    SQL

    subquery_sql.gsub!('__filter_by_cities__', cities.present? ? "AND r.city_id IN (#{cities.ids.join(',')})" : "")
    case relation
    when 'chain_menu_mentions'
      subquery_sql.gsub!('__filter_by_relation__', "AND r.chain_id IS NOT NULL")
    when 'independent_menu_mentions'
      subquery_sql.gsub!('__filter_by_relation__', "AND r.chain_id IS NULL")
    else
      subquery_sql.gsub!('__filter_by_relation__', "")
    end

    entries = all.joins("LEFT OUTER JOIN ( #{subquery_sql} ) AS sub_q ON menu_item_dashboards.id = sub_q.menu_item_dashboard_id")
    str_decorated_columns = decorated_columns.map { |col| "coalesce(#{col}, 0) as #{col}" }.join(', ')
    entries.select("menu_item_dashboards.*, #{str_decorated_columns}")
  end

  # def self.decorated_with_menu_mentions_by_periods(cities: nil, relation: nil); end

  include AASM
  aasm column: 'state' do
    state :itemized, initial: true
    state :approved
    state :flagged
  end

  def last_year_quarters
    quarters.where(:time_period_id => TimePeriod.last_year)
  end

  def last_year_items
    item_ids = last_year_quarters.map{ |q| q.search_items.pluck(:id) }.flatten
    Item.where(:id => item_ids).approved
  end

  def last_year_menus
    # for_all_periods(TimePeriod.last_year)
    TimePeriod.last_year.menus.approved.where(:id => last_year_items.pluck(:menu_id))
  end

  def last_year_restaurants
    Restaurant.where(:id => last_year_items.joins(:restaurant).pluck('restaurants.id'))
  end

  def total_items
    result = quarters.map do |q|
      q.items
    end.flatten
    Item.where(:id => result)
  end

  def query
    super.presence || name
  end

  def items_to_show(excluded: false, cities: nil, total: false)
    if quarters.blank?
      quarters.find_or_create_by(time_period_id: TimePeriod.approved.last.id)
      MenuItemDashboards::AssignQuartersWorker.perform_async(id)
    end
    q_items = total ? total_items : quarters.last&.items
    # q_items = q_items.joins(:item_quarters).where(item_quarters: { excluded: excluded })
    cities.present? ? q_items.filter_by_city(cities) : q_items
  end

  def data_items
    if s3_file_name.present?
      read_from_s3
    else
      total_items
    end
  end

  def emerging_chains
    Chain.where(:id => dashboard_emerging_chains.pluck(:emerging_chain_id))
  end

  def auto_filter_items
    # iqs = quarters.last.item_quarters.where(filtered: false)
    # iqs.find_each(&:item_auto_check)
  end

  def top_categories(items = nil)
    select_str = 'categories.*, count(DISTINCT items.search_uniq_key) as shown_items_count'
    select_str += ', AVG(items.price) as average_price' if show_average_price?
    select_str += ', percentile_cont(0.5) WITHIN GROUP (ORDER BY items.price) as median_price' if show_median_price?
    subquery = (items || items_to_show).joins(:categories).group('categories.id').select(select_str)
    Category.select("categories.*").from("(#{subquery.to_sql}) as categories").order('shown_items_count desc')
  end

  def top_ingredients_chart_data
    sql = <<-SQL
      WITH menu_items AS (__items_sql__),
      mi_ingredients AS (
        SELECT ingredients.id as ingredient_id, ingredients.name as ingredient, __average_price__ __median_price__ COUNT(mi.*) as items_count
        FROM ingredients
          INNER JOIN ingredient_dashboards imi ON ingredients.id = imi.ingredient_id
          INNER JOIN item_dashboards im ON imi.menu_item_dashboard_id = im.menu_item_dashboard_id
          INNER JOIN item_ingredients ii ON ii.item_id = im.item_id AND ii.ingredient_id = imi.ingredient_id
          INNER JOIN menu_items mi ON mi.id = ii.item_id
        WHERE ingredients.state = 'approved' AND imi.excluded != true AND imi.menu_item_dashboard_id = __id__
        GROUP BY ingredients.id, ingredients.name, imi.top_ingredient
        ORDER BY imi.top_ingredient DESC)
      SELECT * FROM mi_ingredients ORDER BY items_count DESC
    SQL

    sql.gsub!('__id__', id.to_s)
    sql.gsub!('__items_sql__', items_to_show.to_sql)
    sql.gsub!('__average_price__', show_average_price? ? 'AVG(mi.price) as average_price,' : '')
    sql.gsub!('__median_price__',  show_median_price?  ? 'percentile_cont(0.5) WITHIN GROUP (ORDER BY mi.price) AS median_price,' : '')

    result = ActiveRecord::Base.connection.execute(sql).to_a
    result.map do |row|
      row['average_price'] = row['average_price'].to_f.round(2).nonzero?
      row['median_price']  = row['median_price'].to_f.round(2).nonzero?
      row
    end
  end

  def top_regions(items = nil)
    select_str = 'regions.*, count(DISTINCT items.search_uniq_key) as shown_items_count'
    select_str += ', AVG(items.price) as average_price' if show_average_price?
    select_str += ', percentile_cont(0.5) WITHIN GROUP (ORDER BY items.price) as median_price' if show_median_price?
    subquery = (items || items_to_show).joins(:regions).group('regions.id').select(select_str)
    Region.select("regions.*")
          .from("(#{subquery.to_sql}) as regions")
          .where(is_default: true)
          .order('shown_items_count desc')
  end

  def top_states(items = nil)
    select_str = 'states.id as state_id, count(DISTINCT items.search_uniq_key) as shown_items_count'
    select_str += ', AVG(items.price) as average_price' if show_average_price?
    select_str += ', percentile_cont(0.5) WITHIN GROUP (ORDER BY items.price) as median_price' if show_median_price?
    subquery = (items || items_to_show).joins(:state).group('states.id').select(select_str).to_sql
    # State.select("states.*").from("(#{subquery.to_sql}) as states").order('shown_items_count desc') # can be missing some states
    states = State.where.not(name: nil) # skip invalid states
    states.joins("LEFT JOIN (#{subquery}) items_agg ON states.id = items_agg.state_id").select("states.*, items_agg.*").order('shown_items_count desc nulls last')
  end

  def top_chains(items: nil)
    select_str = <<-SQL
      chains.*,
      count(DISTINCT items.search_uniq_key) as shown_items_count,
      COALESCE(chains.total_locations, count(restaurants.id)) as location_count
    SQL
    subquery = (items || items_to_show).joins(restaurant: :chain).group('chains.id').select(select_str)
    Chain.approved.select("chains.*")
         .from("(#{subquery.to_sql}) as chains")
         .order('location_count desc')
  end

  def top_chains_with_average_price(items: nil, chain_ids: [])
    select_str = <<-SQL
      chains.*,
      count(DISTINCT items.search_uniq_key) as shown_items_count,
      COALESCE(chains.total_locations, count(restaurants.id)) as location_count,
      AVG(items.price) as average_price
    SQL
    subquery = (items || items_to_show).where.not(:price => [nil, 0]).joins(restaurant: :chain).group('chains.id').select(select_str)
    if chain_ids.present?
      chains = Chain.approved.where(:id => chain_ids)
    else
      chains = Chain.approved
    end
    chains.select("chains.*")
         .from("(#{subquery.to_sql}) as chains")
         .order('location_count desc')
  end

  def set_top_chains_data_rows
    chains.each do |chain|
      TopChainsDataRow.find_or_create_by(
        :menu_item_dashboard_id => id,
        :chain_id => chain.id,
        :year => Menu.current_year,
        :quarter => Menu.current_quarter,
        :row_type => "top_chains"
      )
    end

    emerging_chains.each do |emerging_chain|
      TopChainsDataRow.find_or_create_by(
        :menu_item_dashboard_id => id,
        :chain_id => emerging_chain.id,
        :year => Menu.current_year,
        :quarter => Menu.current_quarter,
        :row_type => "emerging_chains"
      )
    end
  end

  def all_chains
    Chain.where(:id => chains.map(&:id) + emerging_chains.map(&:id))
  end

  def menu_mention_data
    time_periods = TimePeriod.approved.quarters.order('end_at desc').limit(4)
    menus = Menu.approved.for_all_periods(time_periods)

    items_with_menu = total_items.where(:menu_id => menus).joins(:menu).select("items.*, menus.year, menus.quarter, FIRST_VALUE(items.id) OVER (PARTITION BY menus.year, menus.quarter, items.search_uniq_key ORDER BY items.id DESC) as last_id")

    select_str = 'time_periods.id, time_periods.name as time_period_name'
    select_str += ', time_periods.year as time_period_year'
    select_str += ', time_periods.quarter as time_period_quarter'
    select_str += ', count(items.id) as items_count'
    select_str += ', AVG(items.price) as average_price' if show_average_price?
    select_str += ', percentile_cont(0.5) WITHIN GROUP (ORDER BY items.price) as median_price' if show_median_price?

    time_periods_data = time_periods.joins("LEFT JOIN ( #{items_with_menu.to_sql} ) AS items ON items.year = time_periods.year AND items.quarter = time_periods.quarter AND items.id = items.last_id").select(select_str).group('time_periods.id')

    @menu_mentions_chart_data = time_periods_data.as_json.sort_by {|h| h['time_period_name']}
  end

  def set_menu_mention_data_rows
    rows = MenuItemDashboards::GraphDataService.new(menu_item_dashboard: self).menu_mentions_chart_data
    rows.each do |tp|
      name = tp['time_period_name']
      year, quarter = name.split(" ")
      year = year.to_i
      quarter = quarter.scan(/\d+/).first.to_i

      m = MenuMentionDataRow.find_or_create_by(
        :menu_item_dashboard_id => id,
        :month => Time.now.month,
        :day => Time.now.day,
        :year => Time.now.year,
        :quarter => Menu.current_quarter,
        :time_period_name => name,
        :time_period_quarter => quarter,
        :time_period_year => year,
      )
      m.items_count = tp['items_count']
      m.save
    end
  end

  def show_average_price?
    hover_attributes.include?('average')
  end

  def show_median_price?
    hover_attributes.include?('median')
  end

  def show_items_price?
    show_average_price? || show_median_price?
  end

  # private

  def assign_items(force: false)
    MenuItemDashboards::AssignQuartersWorker.new.perform(id, force)
    self.is_new_complete = true
    save

    compute_segment_penetration_rates
    # MenuItemDashboards::SetCacheWorker.perform_async(id, {'force' => true})
  end

  def recent_year
    quarters.where(:time_period_id => TimePeriod.last_year).joins(:time_period).order(Arel.sql('time_periods.year, time_periods.quarter'))
  end

  def init_quarters
    TimePeriod.last_year.each do |time_period|
      quarters.find_or_create_by(time_period_id: time_period.id)
    end
  end

  def assign_quarters(force: false)
    recent_year.each do |q|
      # next if q.is_complete unless force
      q.set_restaurants_with_items_count
    end
  end

  def quarter_csv_data
    CSV.generate do |csv|
      csv << ["id", "name", "quarter_id", 'year', 'quarter','time_period_id', 'items_count', 'menus_count', 'restaurant_count', 'restaurants_with_items_count', 'pen_rate']
      recent_year.each do |q|
        csv << q.quarterly_row
      end
    end
  end

  def print_quarter_info
    AwsS3Manager.csv_data_to_file(quarter_csv_data, "quarter_data_#{SecureRandom.hex(10)}.csv")
  end

  def self.print_quarter_info
    csv_data = CSV.generate do |csv|
      csv << ["id", "name", "quarter_id", 'year', 'quarter','time_period_id', 'items_count', 'menus_count', 'restaurant_count', 'restaurants_with_items_count', 'pen_rate']
      where(:is_new_complete => true).each do |m|
        query = m.recent_year
        next if query.length != 4

        query.each do |q|
          csv << q.quarterly_row
        end
      end
    end
    AwsS3Manager.csv_data_to_file(csv_data, "quarter_data_#{SecureRandom.hex(10)}.csv")
  end

  def assign_data
    read_from_s3.find_each do |item|
      pp item.id
      ItemDashboard.find_or_create_by(:item_id => item.id, :menu_item_dashboard_id => id)
    end
  end

  def potential_items
    Item.where.not(:id => total_items).pg_search(query).where(:menu_id => TimePeriod.last_year.menus) # TODO: use elastic search
  end

  def auto_set_top_cuisine
    update(top_cuisine: MenuItemDashboards::GraphDataService.new(menu_item_dashboard: self).top_cuisines_chart_data.first&.[]('cuisine'))
  end

  def assign_ingredients
    # todo: Refactor This
    # exist_ingredient_ids = ingredient_dashboards.pluck(:ingredient_id)
    # new_ingredient_ids = items_ingredients.approved.ids.uniq - exist_ingredient_ids
    #
    # ingredient_ids_params = new_ingredient_ids.map { |ingredient_id| { ingredient_id: ingredient_id } }
    # ingredient_dashboards.insert_all(ingredient_ids_params) if ingredient_ids_params.present?
  end

  def auto_set_top_ingredients
    top_ingredient_ids = top_ingredients_chart_data.first(5).map{ |d| d[:ingredient_id] }
    ingredient_dashboards.where(ingredient_id: top_ingredient_ids).update_all(top_ingredient: true)
  end

  def dashboard_change_csv
    graph_service = MenuItemDashboards::GraphDataService.new(menu_item_dashboard: self)
    # self.hover_attributes = HOVER_ATTRIBUTES_OPTIONS # add average and median price if not saved
    # menu_mentions_data = graph_service.menu_mentions_chart_data
    menu_mentions_data = graph_service.saved_data&.[](:menu_mentions_chart_data) || graph_service.menu_mentions_chart_data

    headers = ['', *menu_mentions_data.map{ |d| d['time_period_name'] }]
    data = [
      ['Menu Mentions', *menu_mentions_data.map{ |d| d['items_count'] }],
      ['Average Price', *menu_mentions_data.map{ |d| d['average_price'] }],
      ['Median Price',  *menu_mentions_data.map{ |d| d['median_price'] }],

      ['Change', graph_service.change],
      ['Menu Dashboard Name', name],
      ['Menu Dashboard Id', id],
    ]
    CsvExportService.new(name: "#{name}-menu_mentions-sheet", headers: headers, data: data).call # TODO Q: to xlsx?
  end

  def set_dashboard_cache
    MenuItemDashboards::AssignItemsWorker.perform_async(id, 'set_dashboard_cache')
  end

  def read_from_s3
    return if s3_file_name.blank?
    file_name = s3_file_name
    aws_manager = AwsS3Manager.new
    obj = aws_manager.get_file(file_name)
    data = URI.open(aws_manager.get_presigned_url(obj)).read
    Marshal.load(data)
  end

  def dump_to_s3
    file_name = generate_s3_file_name
    tempfile = Tempfile.new(['foo','bin'], binmode: true)
    File.open(tempfile.path, "wb") do |file|
      file.write(Marshal.dump(Item.where(:id => total_items.joins(:item_dashboards, :menu, :restaurant, :restaurant => [:city]))))
    end

    aws_manager = AwsS3Manager.new
    obj = aws_manager.store_file(file_name, tempfile.read)
    self.s3_file_name = file_name
    save
    aws_manager.get_presigned_url obj
  end

  def generate_s3_file_name
    "mids/#{Time.now.year}/#{Time.now.month}/#{Time.now.day}/#{id}_#{name.to_s.downcase}_#{SecureRandom.hex(10)}.bin"
  end

  def us_mention_data
    menu_mentions_region_datas.where(:region_id => nil, :location_key => "us").last
  end

  def growth_percent
    return if us_mention_data.nil?
    us_mention_data.change
    # @growth_percent ||= rand(200) - 100
  end

  def growth_percent_display
    return if growth_percent.nil?
    us_mention_data.change.to_f.round(2)
  end

  def fetch_and_store_trends(async: false)
    MenuItemDashboards::FetchAndStoreTrendsWorker.perform_async(id) and return if async
    trends_data = Serp::Api.new.google_trend(query.presence || name)
    if trends_data.present?
      google_trend = google_trends.create(yearly_trends: trends_data)
      update(last_trend_value: google_trend.last_trend_value)
    end
  rescue StandardError => e
    Rails.logger.error "Error fetching trends for #{query.presence || name}: #{e.message}"
  end

  def set_guid
    return if guid.present?
    self.guid = SecureRandom.uuid
    save
  end

  def set_dashboard_public
    self.public = true
    self.public_token = SecureRandom.hex(10)
    save
  end

  def remove_public
    self.public = false
    self.public_token = nil
    save
  end

  def set_chart_row_data
    set_top_chains_data_rows
    set_menu_mention_data_rows
  end

  # make sure to import dashboard items to Elastic Search first; call optimize index after
  def es_search_items(chain_id: nil, page:nil, per_page: 30 )
    options = {
      :filters => {:menu_item_dashboard_ids => [id],
                   :chain_id => chain_id},
      :order_by => nil,
      :paginate_params => {:page => page, :per_page => per_page },
      :user_search_options => { "some_words_present" => "0"},
      :distinct => true
    }
    Item.search(query, options).records.to_a
  end

  def import_to_es
    Search::Elasticsearch::ImportItemsWorker.perform_async(nil, nil, total_items.pluck(:id)) # TODO: Q: total_items => items_to_show?
  end

  def self.update_google_trend
    GoogleTrend.pluck(:menu_item_dashboard_id).uniq.each do |dashboard_id|
      menu_item_dashboard = MenuItemDashboard.find(dashboard_id)
      last_trends = menu_item_dashboard.google_trends.order(:created_at).last
      menu_item_dashboard.update_column(:last_trend_value, last_trends&.last_trend_value)
    end
  end

  def update_top_chains_data_rows
    top_chains_data_rows.each(&:async_calc_stats)
  end

  def set_menu_mention_count
    self.menu_mentions_count = menu_mention_data_rows.order('year, quarter').where.not(:items_count => nil).sum(:items_count)
    save
  end

  def run_gpt_ingredients
    all_chains.each do |c|
      cm = c.chain_menus.order('year, quarter').last
      if cm
        cm.chain_menu_items.each do |item|
          Ai::ItemGptIngredientsWorker.perform_async item.id
        end
      end
    end
  end

  # calculate the top ingredients from dashboard chains
  def top_ingredients(chains, k: 5)
    items = ChainMenuItem.where(:chain_id => chains).pg_search(query, {:some_words_present => '1'})
    total_items = items.count
    ingredients = GptIngredient.joins(:items).where(:items => {:id => items})
    top_k = ingredients.select('gpt_ingredients.*, COUNT(DISTINCT items.id) AS gpt_count').group('gpt_ingredients.id').order('gpt_count DESC').limit(k)
    top_k.map do |ingredient|
      {
        :name => ingredient.name,
        :count => ingredient.items.where(:id => items).count,
        :total_items => total_items,
        :percent => (ingredient.items.where(:id => items).count / total_items.to_f * 100).to_f.round(2)
      }
    end
  end

  def run_adjectives
    all_chains.each do |c|
      cm = c.chain_menus.order('year, quarter').last
      if cm
        cm.chain_menu_items.each do |item|
          Items::ItemAdjectiveWorker.perform_async item.id
        end
      end
    end
  end

  def top_adjectives(chains, k: 5)
    items = ChainMenuItem.where(:chain_id => chains).pg_search(query, {:some_words_present => '1'})
    total_items = items.count
    adjectives = Adjective.joins(:items).where(:items => {:id => items})
    top_k = adjectives.select('adjectives.*, COUNT(DISTINCT items.id) AS adjective_count').group('adjectives.id').order('adjective_count DESC').limit(k)
    top_k.map do |adjective|
      {
        :name => adjective.name,
        :count => adjective.items.where(:id => items).count,
        :total_items => total_items,
        :percent => (adjective.items.where(:id => items).count / total_items.to_f * 100).to_f.round(2)
      }
    end
  end

  def news(chains)
    NewsArticle.search_any_word([query, chains.map(&:name)].flatten)
  end

  def latest_news(chains, limit: 20)
    found_articles = news(chains)
    NewsArticle.where(:id => found_articles.pluck(:id)).order('created_at DESC').limit(limit)
  end

  def update_metrics
    q = quarters.order('time_period_id').last
    return if q.nil?

    self.pen_rate = q.pen_rate
    self.menu_adoption = q.menu_adoption
    self.delta_last_period = q.delta_last_period
    save
  end

  def create_or_update_infegy_data
    infegy = Infegy.new('ds_gj4u3F40SLa', [query])

    start_date = (Time.now - 1.year).beginning_of_month.to_date
    end_date = (Time.now - 1.month).end_of_month.to_date

    monthly_social_conversations_over_time = infegy.monthly_social_conversations_over_time(start_date, end_date)

    # Check for error in response
    if monthly_social_conversations_over_time[:error]
      Rails.logger.error ">> #{self.class.name} ID: #{id} Infegy API Error: #{monthly_social_conversations_over_time[:error]} - Status: #{monthly_social_conversations_over_time[:status]} - Body: #{monthly_social_conversations_over_time[:body]}"
      return
    end

    monthly_social_conversations_over_time.each do |date, count|
      month = date.to_date.month
      year = date.to_date.year

      social_conversations.find_or_create_by(
        month: month,
        year: year
      ).update(num_conversations: count)
    end

    ai_summary = infegy.ai_summarization(start_date, end_date)
    topics = infegy.top_topics(start_date, end_date)
    sentiment = infegy.sentiment_analysis(start_date, end_date)

    counts = social_conversations.order('year DESC, month DESC').first(12).map(&:num_conversations)

    yearly_growth = counts.length > 1 && counts[-2] > 0 ? ((counts[-1] - counts[-2]).to_f / counts[-2] * 100).to_i : 0

    update(
      ai_summary:,
      topics:,
      positive_percentage: sentiment[:positive_percentage],
      negative_percentage: sentiment[:negative_percentage],
      neutral_percentage: sentiment[:neutral_percentage],
      yearly_growth:
    )
  end

  def current_pen_rate(region)
    quarters.last&.pen_rate(region)
  end

  def self.run_infegy(force: false)
    MenuItemDashboard.all.find_each do |m|
      MenuItemDashboards::RunInfegyWorker.perform_async(m.id, force)
    end
  end

  def self.run_segment_pen_rate
    MenuItemDashboard.all.find_each do |m|
      MenuItemDashboards::RunSegmentPenRateWorker.perform_async(m.id)
    end
  end

  def retails_carousel_images
    limited_time_offers = LimitedTimeOffer.pg_search(query)
    LimitedTimeOffer.from(limited_time_offers, :limited_time_offers).order(Arel.sql('created_at DESC')).limit(6)
  end

  # private

  def compute_segment_penetration_rates
    self.qsr_penetration = last_quarter&.qsr_penetration
    self.fast_casual_penetration = last_quarter&.fast_casual_penetration
    self.mid_scale_penetration = last_quarter&.mid_scale_penetration
    self.fine_dining_penetration = last_quarter&.fine_dining_penetration
    save
  end

end
