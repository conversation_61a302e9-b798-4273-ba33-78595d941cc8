class IngredientBlueprint < Blueprinter::Base
  view :retails_carousel_images do
    identifier :id

    field :carousel_images do |ingredient|
      images = ingredient.product_images(count: 7, random: true)
      images.map do |image|
        {
          img_url: image[:url],
          name: image[:name].titleize,
          offer_url: image[:listing_url]
        }
      end
    end
  end

  view :most_common_product_category do
    identifier :id
    field :chart_data do |ingredient|
      store_ids = params[:store_ids] if defined?(params)
      data = ingredient.top_categories_chart_data(level: 'l1', store_ids: store_ids)
      data.map do |cd|
        next if cd['unique_skus']&.zero? || cd[:unique_skus]&.zero?
        {
          id: cd[:id],
          category: cd[:category].titleize,
          item_count: cd[:unique_skus]
        }
      end.compact
    end
  end

  view :most_common_product_category_es do
    identifier :id
    field :chart_data do |ingredient|
      store_ids = params[:store_ids] if defined?(params)
      result = Product.top_categories_chart_data_es(name: ingredient.name, level: 'l1', store_ids: store_ids)
      result.map do |bucket|
        {
          id: bucket['key'],
          category: bucket['key'].titleize,
          item_count: bucket['unique_skus']['value']
        }
      end.compact
    end
  end

  view :retail_growth do
    field :retail_growth
  end

  view :retail_growth_es do
    field :retail_growth do |ingredient|
      Product.retail_growth_es(name: ingredient.name, growth_prediction: ingredient.growth_prediction)
    end
  end

  view :products_table do
    identifier :id
    field :products_table do |ingredient, options|
      es_result = options[:locals][:es_result]

      product_ids = es_result.map { |hit| hit['_id'] }

      products = Product
                   .where(id: product_ids)
                   .where.not(sku: nil)
                   .includes(:images, :retailer)
                   .select('products.id, products.retailer_id, products.name AS product_name, products.l1, products.price, products.created_at, retailers.name AS retailer_name')
                   .joins(:retailer)

      {
        data: products.map do |product|
          {
            id: product.id,
            name: product.retailer.name.titleize,
            category: product.l1.titleize,
            product: {
              name: product.product_name.titleize,
              image_urls: product.images.map(&:url).compact
            },
            price: product.price,
            created_at: product.created_at.to_date
          }
        end,
        recordsTotal: es_result.response['hits']['total']['value'],
        recordsFiltered: es_result.response['hits']['total']['value']
      }
    end
  end

  view :top_retails_by_banner_name do
    identifier :id
    field :top_retails_by_banner_name do |ingredient|
      result = Product.top_retailers_by_banner_name_es(name: ingredient.name)
      result.map do |bucket|
        {
          name: bucket["key"].titleize,
          count: bucket["unique_skus"]["value"],
          price: bucket["avg_price"]["value"]&.round(2) || 0.0
        }
      end
    end
  end

  view :top_manufacturers do
    identifier :id
    field :top_brands do |ingredient|
      result = Product.top_manufacturers_es(name: ingredient.name)


      result.map do |bucket|
        {
          name: bucket['key'].titleize,
          count: bucket['unique_sku_count']['value'] || bucket['doc_count'],
          price: bucket.dig('avg_price', 'value')&.round(2) || 0.0
        }
      end
    end
  end

  view :highest_growing_ingredient_by_category do
    exclude :id
    field :category_name do |category|
      category[:category_name].titleize
    end
    field :quarters do |category|
      quarters_hash = {}
      category[:quarters].each do |quarter_key, data|
        quarters_hash[quarter_key.to_s] = {
          value: data[:value],
          percent: data[:percent].zero? ? 0 : "#{data[:percent]}%"
        }
      end

      quarter_keys = quarters_hash.keys.sort_by { |q| q.match(/(\d+)_q(\d+)/).try(:captures).map(&:to_i) || [0, 0] }
      quarter_keys.index_with { |q_key| quarters_hash[q_key] }
    end
  end
end