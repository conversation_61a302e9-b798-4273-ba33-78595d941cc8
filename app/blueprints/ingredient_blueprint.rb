class IngredientBlueprint < Blueprinter::Base
  view :retails_carousel_images do
    identifier :id

    field :carousel_images do |ingredient|
      images = ingredient.product_images(count: 7, random: true)
      images.map do |image|
        {
          img_url: image[:url],
          name: image[:name],
          offer_url: image[:listing_url]
        }
      end
    end
  end

  view :most_common_product_category do
    identifier :id
    field :chart_data do |ingredient|
      store_ids = params[:store_ids] if defined?(params)
      data = ingredient.top_categories_chart_data(level: 'l1', store_ids: store_ids)
      data.map do |cd|
        next if cd['unique_skus']&.zero? || cd[:unique_skus]&.zero?

        {
          id: cd[:id],
          category: cd[:category],
          item_count: cd[:unique_skus]
        }
      end.compact
    end
  end

  view :retail_growth do
    field :retail_growth
  end

  view :products_table do
    identifier :id
    field :products_table do |ingredient, options|
      es_result = options[:locals][:es_result]

      product_ids = es_result.map { |hit| hit['_id'] }

      products = Product
                   .where(id: product_ids)
                   .where.not(sku: nil)
                   .includes(:images, :retailer)
                   .select('products.id, products.retailer_id, products.name AS product_name, products.l1, products.price, products.created_at, retailers.name AS retailer_name')
                   .joins(:retailer)

      {
        data: products.map do |product|
          {
            id: product.id,
            name: product.retailer.name,
            category: product.l1,
            product: {
              name: product.product_name,
              image_urls: product.images.map(&:url).compact
            },
            price: product.price,
            created_at: product.created_at.to_date
          }
        end,
        recordsTotal: es_result.response['hits']['total']['value'],
        recordsFiltered: es_result.response['hits']['total']['value']
      }
    end
  end
end