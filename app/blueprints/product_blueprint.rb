class ProductBlueprint < Blueprinter::Base
  view :product_view do
    identifier :id

    fields :name, :price, :upc, :listing_url, :created_at

    field :image_urls do |product|
      product.images.map(&:url)
    end

    field :retailer do |product|
      product.retailer.name
    end

    field :created_at do |product|
      product.created_at.to_date
    end
  end

  view :fastest_growing do
    exclude :id
    identifier :category_name
    field :category_name do |category|
      category[:category_name].titleize
    end
    field :quarters do |category|
      quarters_hash = {}
      category[:quarters].each do |year, quarters|
        quarters.each do |quarter, data|
          quarter_key = "#{year}_#{quarter}".to_sym
          quarters_hash[quarter_key] = {
            value: data[:value],
            percent: data[:percent] != 0 ? "#{data[:percent]}%" : 0
          }
        end
      end

      quarter_keys = quarters_hash.keys.sort
      quarter_keys.index_with do |q_key|
        quarters_hash[q_key]
      end
    end
  end

  view :white_space_opportunities do
    exclude :id
    fields :saturation, :saturation_percent, :unique_brands
    field :category_name do |category|
      category[:category_name].titleize
    end
  end
end
