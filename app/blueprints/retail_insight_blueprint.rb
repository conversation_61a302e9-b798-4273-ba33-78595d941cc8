class RetailInsightBlueprint < Blueprinter::Base
  identifier :id

  view :images do
    field :images do |dashboard|
      dashboard.products.includes(:images).limit(10).flat_map(&:images).map do |image|
        ImageBlueprint.render_as_hash(image)
      end
    end
  end

  view :top_brands do
    field :top_brands do |object|
      object.set_top_brands.map { |brand| brand.transform_keys(&:to_s) }
    end
  end

  view :top_retailers do
    field :top_retailers_data, &:top_retailers
  end

  view :top_categories do
    field :top_categories_data, &:top_categories
  end

  view :growth_categories do
    field :growth_categories_data, &:top_categories
  end

  view :retail_growth do
    field :quarterly_growth, &:growth_percent
    field :projected_growth do |dashboard|
      dashboard.growth_percent * 1.1
    end
  end

  view :top_skus do
    association :top_skus, blueprint: ProductBlueprint, view: :product_view
  end

  view :top_retails_by_banner_name do
    field :top_retails_by_banner_name, &:top_retailers_by_banner_name
  end
end
