require 'parallel' unless Rails.env.production? || Rails.env.staging?

class UpdateProductYearQuarterWorker
  include Sidekiq::Worker

  # Limit retries for Sidekiq
  sidekiq_options retry: 3
  sidekiq_options queue: :product_category_batch

  # Batch size for updates
  BATCH_SIZE = 100_000

  def perform(mode: 'server')
    mode == 'local' && !Rails.env.production? ? perform_local : perform_server
  end

  private

  # Server mode: Batch update using Sidekiq
  def perform_server
    process_batches do |batch|
      updates = build_updates(batch)
      perform_batch_update(updates) unless updates.empty?
    end
  end

  # Local mode: Parallel processing with ID range output
  def perform_local
    process_batches do |batch|
      updates = build_updates(batch)
      next if updates.empty?

      # Get ID range for the batch
      id_range = "#{updates.first[:id]}-#{updates.last[:id]}"
      puts "Processing IDs: #{id_range}"

      # Process batch update in parallel
      Parallel.each([updates].each_slice(1), in_processes: Parallel.processor_count) do |sub_batch|
        sub_batch.each { |u| perform_batch_update(u) }
      end
    end
  end

  # Helper method to process product batches
  def process_batches
    processed = 0

    Product.unscoped.where.not(week_date: nil)
           .where(year: nil)
           .where(quarter: nil)
           .in_batches(of: BATCH_SIZE) do |batch|
      yield batch
      processed += batch.size
      Rails.logger.info("Processed #{processed} products in batch")
    end

    Rails.logger.info("Finished processing products")
  end

  # Helper method to build updates from the product batch
  def build_updates(batch)
    batch.select(:id, :week_date).map do |product|
      parse_product_week_date(product)
    end.compact
  end

  # Parse week_date and return the update hash if valid
  def parse_product_week_date(product)
    return unless product.week_date.match?(/\A\d{4}-\d{2}-\d{2}\z/)

    begin
      date = Date.parse(product.week_date)
      quarter = determine_quarter(date.month)

      return unless quarter

      { id: product.id, year: date.year, quarter: quarter }
    rescue StandardError => e
      Rails.logger.error("Error processing Product ID #{product.id}: #{e.message}")
      nil
    end
  end

  # Helper method to determine quarter based on month
  def determine_quarter(month)
    case month
    when 1..3 then 1
    when 4..6 then 2
    when 7..10 then 3
    when 11..12 then 4
    else nil
    end
  end

  # Perform batch update using a single SQL query
  def perform_batch_update(updates)
    return if updates.empty?

    values = updates.map { |u| "(#{u[:id]}, #{u[:year]}, #{u[:quarter]})" }.join(', ')

    Product.connection.execute <<-SQL
      UPDATE products
      SET year = updates.year,
          quarter = updates.quarter
      FROM (VALUES #{values}) AS updates(id, year, quarter)
      WHERE products.id = updates.id
    SQL
  end
end
