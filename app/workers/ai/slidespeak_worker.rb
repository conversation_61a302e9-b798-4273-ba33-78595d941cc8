#!/usr/bin/env ruby

require 'net/http'
require 'json'
require 'uri'
require 'sidekiq'
require 'action_cable'

class Ai::SlidespeakWorker
  include Sidekiq::Worker
  sidekiq_options queue: :default

  API_KEY = '26408b85-44ef-4844-bf1c-1eb0de483627'
  API_URL = 'https://api.slidespeak.co/api/v1/presentation/generate'
  
  def perform(tool_name, chat_id, prompt, query)
    @tool_name = tool_name
    @chat_id = chat_id
    @prompt = prompt
    @query = query
    @chat = Chat.find(chat_id)

    # Broadcast streaming start
    broadcast_streaming_start

    begin
      # Format the content for the API
      content = format_trends_content(@prompt)
      
      # Create the presentation based on tool type
      task_id = case @tool_name
                when 'social_trends_by_query'
                  create_social_trends_presentation(content)
                else
                  create_default_presentation(content)
                end
      
      # Poll for status and get the URL
      report_url = poll_for_status(task_id)
      
      # Store the result in Rails cache
      store_result(report_url)
      
      # Broadcast the URL update
      broadcast_streaming_update(report_url)
      
      # Broadcast streaming complete
      broadcast_streaming_complete
      
      report_url
    rescue => e
      # Handle errors
      broadcast_streaming_complete
      raise e
    end
  end

  private

  def broadcast_streaming_start
    channel = "chat_channel_#{@chat_id}"
    ActionCable.server.broadcast(
      channel,
      { type: "streaming_start", message: "Generating presentation..." }
    )
  end

  def broadcast_streaming_update(url)
    channel = "chat_channel_#{@chat_id}"

    # Create initial AI message with HTML link
    @ai_message = ChatMessage.create(
      message: "<a href='#{url}' target='_blank'>Presentation Download Link</a>",
      chat_id: @chat.id,
      message_type: 'ai'
    )

    ActionCable.server.broadcast(
      channel,
      { 
        type: "streaming_update", 
        message: {
          id: @ai_message.id,
          message: "<a href='#{url}' target='_blank'>Presentation Download Link</a>",
          message_type: 'ai'
        }
      }
    )
  end

  def broadcast_streaming_complete
    channel = "chat_channel_#{@chat_id}"
    ActionCable.server.broadcast(
      channel,
      { 
        type: "streaming_complete", 
        messages: @chat.chat_messages.last(5).as_json 
      }
    )
  end

  def custom_instructions
    <<~TEXT
      Create a powerpoint presentation deck for the following content. Include relevant images and make the presentation visually appealing.

      THE TITLE OF THE PRESENTATION SHOULD BE: "#{@query} - Consumer Insights Report"
      Make sure to include all table data and menu examples.
      PROVIDE STATISTICS AND NUMBERS FOR EACH SLIDE WHEN AVAILABLE.

      REMOVE SWOT ANALYSIS.

      THIS IS THE FORMAT of the deck:
      SLIDE 1 should be: Consumer Insights Report Overview
      - **Total posts**: 8,301
      - **Sentiment**: 93.2% positive (7,740 posts), 6.8% negative (561 posts)
      - **Monthly conversation trend** (Jan-Apr 2025): 9.2% growth rate
        - Instagram: 8.9% growth (92.3% of all conversations)
        - TikTok: 12.2% growth (7.7% of all conversations)

      SLIDE 2 should be Top Descriptors:
      **Top Descriptors**:

      SLIDE 3 should be Flavor Innovation:

      **Emerging Trends**:

      SLIDE 4 should be Target Demographics:

      SLIDE 5 should be Emerging Insights:

      SLIDE 6 should be Trending Social Media Posts:
      - PRINT OUT THE EXACT EXAMPLES OF ONE POST EXAMPLE
      - MAKE THE QUOTE CONTAIN THE TEXT OF THE POST
      - PRINT OUT THE LINK TO THE POST
      - Take only the positive instagram post and make that this slide

      SLIDE 7 should be Marketing Opportunities:

      FOLLOW THIS TEMPLATE OF SLIDE 1-7
      REVIEW THIS AND MAKE SURE TO INCLUDE THE SLIDES WITH THE SAME AMOUNT OF DETAILS. Dont add anyhting that is not provided:
      REVIEW THAT EACH SLIDE IS ADDED.
    TEXT
  end

  def format_trends_content(prompt)
    case @tool_name
    when 'social_trends_by_query'
      <<~TEXT
#{prompt}
      TEXT
    else
      prompt
    end
  end

  def create_social_trends_presentation(content)
    payload = {
      plain_text: content,
      length: 7,
      template: "default",
      language: "ORIGINAL",
      fetch_images: true,
      tone: "default",
      verbosity: "standard",
      custom_user_instructions: custom_instructions,
      include_table_of_contents: "false"
    }

    make_api_request(payload)
  end

  def create_default_presentation(content)
    payload = {
      plain_text: content,
      length: 6,
      template: "default",
      language: "ORIGINAL",
      fetch_images: true,
      tone: "default",
      verbosity: "standard",
      custom_user_instructions: "Create a professional presentation"
    }

    make_api_request(payload)
  end

  def make_api_request(payload)
    uri = URI(API_URL)
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true

    request = Net::HTTP::Post.new(uri.request_uri)
    request['Content-Type'] = 'application/json'
    request['X-API-Key'] = API_KEY
    request.body = payload.to_json

    response = http.request(request)

    if response.is_a?(Net::HTTPSuccess)
      result = JSON.parse(response.body.force_encoding('UTF-8'))
      result['task_id']
    else
      raise "Failed to create presentation: #{response.code} - #{response.message}"
    end
  end

  def poll_for_status(task_id)
    status_url = "https://api.slidespeak.co/api/v1/task_status/#{task_id}"
    uri = URI(status_url)
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true

    loop do
      p "polling #{task_id}"
      status_request = Net::HTTP::Get.new(uri.request_uri)
      status_request['X-API-Key'] = API_KEY
      
      status_response = http.request(status_request)
      status_result = JSON.parse(status_response.body.force_encoding('UTF-8'))
      
      case status_result['task_status']
      when 'SUCCESS'
        return status_result['task_result']['url']
      when 'FAILED'
        raise "Task failed: #{status_result['error']}"
      end
      
      sleep(5) # Wait 5 seconds before polling again
    end
  end

  def store_result(report_url)
    key = "#{@tool_name}_report_#{@chat_id}"
    Rails.cache.write(key, report_url, expires_in: 90.days)
  end
end