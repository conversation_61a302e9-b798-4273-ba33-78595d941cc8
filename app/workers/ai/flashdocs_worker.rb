#!/usr/bin/env ruby

require 'net/http'
require 'json'
require 'uri'
require 'sidekiq'
require 'action_cable'

class Ai::FlashdocsWorker
  include Sidekiq::Worker
  sidekiq_options queue: :default

  API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************.GfDUNvbZMlZ-DA9BrjA225zAkECX9tl7wvgVwxCPThM'
  API_URL = 'https://api.flashdocs.ai/v3/generate/deck/task'
  SOURCE_DOCUMENT_ID = 'aa403155-f6ad-418f-82ff-3db0dc5f8239'
  
  def perform(tool_name, chat_id, prompt, query, source_document_id = nil)
    @tool_name = tool_name
    @chat_id = chat_id
    @prompt = prompt
    @query = query
    @source_document_id = source_document_id || SOURCE_DOCUMENT_ID
    @chat = Chat.find(chat_id)

    # Broadcast streaming start
    broadcast_streaming_start

    begin
      # Format the content for the API
      content = format_trends_content(@prompt)
      
      # Create the presentation based on tool type
      task_id = case @tool_name
                when 'social_trends_by_query'
                  create_social_trends_presentation(content)
                else
                  create_default_presentation(content)
                end
      
      # Poll for status and get the URL
      report_url = poll_for_status(task_id)
      
      # Store the result in Rails cache
      store_result(report_url)
      
      # Broadcast the URL update
      broadcast_streaming_update(report_url)
      
      # Broadcast streaming complete
      broadcast_streaming_complete
      
      report_url
    rescue => e
      # Handle errors
      broadcast_streaming_complete
      raise e
    end
  end

  private

  def broadcast_streaming_start
    channel = "chat_channel_#{@chat_id}"
    ActionCable.server.broadcast(
      channel,
      { type: "streaming_start", message: "Generating presentation..." }
    )
  end

  def broadcast_streaming_update(url)
    channel = "chat_channel_#{@chat_id}"

    # Create initial AI message with HTML link
    @ai_message = ChatMessage.create(
      message: "<a href='#{url}' target='_blank'>Presentation Download Link</a>",
      chat_id: @chat.id,
      message_type: 'ai'
    )

    ActionCable.server.broadcast(
      channel,
      { 
        type: "streaming_update", 
        message: {
          id: @ai_message.id,
          message: "<a href='#{url}' target='_blank'>Presentation Download Link</a>",
          message_type: 'ai'
        }
      }
    )
  end

  def broadcast_streaming_complete
    channel = "chat_channel_#{@chat_id}"
    ActionCable.server.broadcast(
      channel,
      { 
        type: "streaming_complete", 
        messages: @chat.chat_messages.last(5).as_json 
      }
    )
  end

  def custom_instructions
    <<~TEXT
      Create a powerpoint presentation deck for the following content. Include relevant images and make the presentation visually appealing.

      THE TITLE OF THE PRESENTATION SHOULD BE: "#{@query} - Consumer Insights Report"
      Make sure to include all table data and menu examples.
      PROVIDE STATISTICS AND NUMBERS FOR EACH SLIDE WHEN AVAILABLE.

      REMOVE SWOT ANALYSIS.

      THIS IS THE FORMAT of the deck:
      SLIDE 1 should be: Consumer Insights Report Overview
      - **Total posts**: 8,301
      - **Sentiment**: 93.2% positive (7,740 posts), 6.8% negative (561 posts)
      - **Monthly conversation trend** (Jan-Apr 2025): 9.2% growth rate
        - Instagram: 8.9% growth (92.3% of all conversations)
        - TikTok: 12.2% growth (7.7% of all conversations)

      SLIDE 2 should be Top Descriptors:
      **Top Descriptors**:

      SLIDE 3 should be Flavor Innovation:

      **Emerging Trends**:

      SLIDE 4 should be Target Demographics:

      SLIDE 5 should be Emerging Insights:

      SLIDE 6 should be Trending Social Media Posts:
      - PRINT OUT THE EXACT EXAMPLES OF ONE POST EXAMPLE
      - MAKE THE QUOTE CONTAIN THE TEXT OF THE POST
      - PRINT OUT THE LINK TO THE POST
      - Take only the positive instagram post and make that this slide

      SLIDE 7 should be Marketing Opportunities:

      FOLLOW THIS TEMPLATE OF SLIDE 1-7
      REVIEW THIS AND MAKE SURE TO INCLUDE THE SLIDES WITH THE SAME AMOUNT OF DETAILS. Dont add anyhting that is not provided:
      REVIEW THAT EACH SLIDE IS ADDED.
    TEXT
  end

  def format_trends_content(prompt)
    case @tool_name
    when 'social_trends_by_query'
      <<~TEXT
#{prompt}
      TEXT
    else
      prompt
    end
  end

  def create_social_trends_presentation(content)
    payload = {
      prompt: "#{content}\n\n#{custom_instructions}",
      source_document_id: @source_document_id,
      number_slides: 7,
      presentation_name: "#{@query} - Consumer Insights Report",
      outline: [
        {
          content_instruction: "Consumer Insights Report Overview with key statistics",
          layout_instruction: "Title slide with statistics overview",
          text_placeholder_manual_insertions: [
            {
              placeholder: "[title]",
              value: "#{@query} - Consumer Insights Report"
            },
            {
              placeholder: "[caption]",
              value: "Social Media Analysis & Trends"
            },
            {
              placeholder: "[paragraph_1]",
              value: extract_statistics(content)
            }
          ]
        },
        {
          content_instruction: "Top Descriptors from social media analysis",
          layout_instruction: "Word cloud or tag layout",
          text_placeholder_manual_insertions: [
            {
              placeholder: "[subsubheader_1]",
              value: "Top Descriptors"
            },
            {
              placeholder: "[list]",
              value: extract_descriptors(content)
            }
          ]
        },
        {
          content_instruction: "Flavor Innovation and emerging trends",
          layout_instruction: "Trend visualization with icons",
          text_placeholder_manual_insertions: [
            {
              placeholder: "[subsubheader_2]",
              value: "Flavor Innovation"
            },
            {
              placeholder: "[subsubheader]",
              value: "Emerging Trends"
            },
            {
              placeholder: "[paragraph_2]",
              value: extract_trends(content)
            }
          ]
        },
        {
          content_instruction: "Target Demographics breakdown",
          layout_instruction: "Demographic charts and infographics",
          text_placeholder_manual_insertions: [
            {
              placeholder: "[subsubheader_1]",
              value: "Target Demographics"
            },
            {
              placeholder: "[paragraph_3]",
              value: extract_demographics(content)
            }
          ]
        },
        {
          content_instruction: "Emerging Insights from data analysis",
          layout_instruction: "Key insights with supporting data",
          text_placeholder_manual_insertions: [
            {
              placeholder: "[subsubheader_2]",
              value: "Emerging Insights"
            },
            {
              placeholder: "[paragraph_4]",
              value: extract_insights(content)
            }
          ]
        },
        {
          content_instruction: "Trending Social Media Posts examples",
          layout_instruction: "Social media post mockups",
          text_placeholder_manual_insertions: [
            {
              placeholder: "[subsubheader]",
              value: "Trending Social Media Posts"
            },
            {
              placeholder: "[paragraph_5]",
              value: extract_social_posts(content)
            }
          ]
        },
        {
          content_instruction: "Marketing Opportunities and recommendations",
          layout_instruction: "Action-oriented slide with opportunities",
          text_placeholder_manual_insertions: [
            {
              placeholder: "[title]",
              value: "Marketing Opportunities"
            },
            {
              placeholder: "[list]",
              value: extract_marketing_opportunities(content)
            }
          ]
        }
      ]
    }

    make_api_request(payload)
  end

  def create_default_presentation(content)
    payload = {
      prompt: "#{content}\n\nCreate a professional presentation with relevant images and make it visually appealing.",
      source_document_id: @source_document_id,
      number_slides: 6,
      presentation_name: "Professional Report",
      outline: [
        {
          content_instruction: "Create title slide with main topic overview",
          layout_instruction: "Title slide with background image",
          text_placeholder_manual_insertions: [
            {
              placeholder: "[title]",
              value: "#{@query} - Professional Report"
            },
            {
              placeholder: "[caption]",
              value: "Comprehensive Analysis and Insights"
            }
          ]
        },
        {
          content_instruction: "Present key findings and statistics",
          layout_instruction: "Content slide with bullet points",
          text_placeholder_manual_insertions: [
            {
              placeholder: "[subsubheader_1]",
              value: "Key Findings"
            },
            {
              placeholder: "[paragraph_1]",
              value: extract_key_points(content, 1)
            },
            {
              placeholder: "[paragraph_2]",
              value: extract_key_points(content, 2)
            }
          ]
        },
        {
          content_instruction: "Show detailed analysis and trends",
          layout_instruction: "Two-column layout with charts",
          text_placeholder_manual_insertions: [
            {
              placeholder: "[subsubheader_2]",
              value: "Detailed Analysis"
            },
            {
              placeholder: "[paragraph_3]",
              value: extract_key_points(content, 3)
            },
            {
              placeholder: "[list]",
              value: extract_list_items(content)
            }
          ]
        },
        {
          content_instruction: "Present demographics and target audience",
          layout_instruction: "Infographic style with icons",
          text_placeholder_manual_insertions: [
            {
              placeholder: "[subsubheader]",
              value: "Target Demographics"
            },
            {
              placeholder: "[paragraph_4]",
              value: extract_demographics(content)
            }
          ]
        },
        {
          content_instruction: "Show recommendations and next steps",
          layout_instruction: "Action-oriented slide with call-to-action",
          text_placeholder_manual_insertions: [
            {
              placeholder: "[subsubheader_1]",
              value: "Recommendations"
            },
            {
              placeholder: "[paragraph_5]",
              value: extract_recommendations(content)
            }
          ]
        },
        {
          content_instruction: "Conclusion and summary",
          layout_instruction: "Clean summary slide",
          text_placeholder_manual_insertions: [
            {
              placeholder: "[title]",
              value: "Summary & Next Steps"
            },
            {
              placeholder: "[paragraph_1]",
              value: "Key takeaways from this analysis"
            },
            {
              placeholder: "[paragraph_2]",
              value: "Recommended actions moving forward"
            }
          ]
        }
      ]
    }

    make_api_request(payload)
  end

  def make_api_request(payload)
    uri = URI(API_URL)
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true

    request = Net::HTTP::Post.new(uri.request_uri)
    request['Content-Type'] = 'application/json'
    request['Authorization'] = "Bearer #{API_KEY}"
    request.body = payload.to_json

    response = http.request(request)

    if response.is_a?(Net::HTTPSuccess)
      result = JSON.parse(response.body.force_encoding('UTF-8'))
      result['task_id']
    else
      raise "Failed to create presentation: #{response.code} - #{response.message}"
    end
  end

  def poll_for_status(task_id)
    status_url = "https://api.flashdocs.ai/v3/generate/deck/task/#{task_id}"
    uri = URI(status_url)
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true

    loop do
      p "polling #{task_id}"
      status_request = Net::HTTP::Get.new(uri.request_uri)
      status_request['Authorization'] = "Bearer #{API_KEY}"
      
      status_response = http.request(status_request)
      
      case status_response.code.to_i
      when 200
        # Task completed successfully
        status_result = JSON.parse(status_response.body.force_encoding('UTF-8'))
        if status_result['success']
          return status_result['link_to_deck']
        else
          raise "Task failed: #{status_result['error']}"
        end
      when 202
        # Task still in progress, continue polling
        p "Task still in progress, waiting..."
      when 404
        raise "Task not found: #{task_id}"
      when 422
        status_result = JSON.parse(status_response.body.force_encoding('UTF-8'))
        raise "Request error: #{status_result['error'] || 'Unprocessable entity'}"
      else
        raise "Unexpected response: #{status_response.code} - #{status_response.message}"
      end
      
      sleep(5) # Wait 5 seconds before polling again
    end
  end

  def store_result(report_url)
    key = "#{@tool_name}_report_#{@chat_id}"
    Rails.cache.write(key, report_url, expires_in: 90.days)
  end

  # Helper methods to extract content for placeholders
  def extract_key_points(content, index)
    sentences = content.split(/[.!?]+/).map(&:strip).reject(&:empty?)
    case index
    when 1
      sentences.first || "Primary insight from the analysis"
    when 2
      sentences[1] || "Secondary finding from the data"
    when 3
      sentences[2] || "Additional analysis point"
    else
      sentences[index - 1] || "Supporting data point"
    end
  end

  def extract_list_items(content)
    # Look for bullet points, numbers, or create from sentences
    if content.include?('•') || content.include?('-')
      # Extract existing bullet points
      content.scan(/[•\-]\s*(.+)/).flatten.first(3).join("\n• ")
    elsif content.include?('%') || content.include?('growth')
      # Extract statistics
      stats = content.scan(/\d+%[^.]*/).first(3)
      stats.empty? ? "Key metrics from analysis" : "• #{stats.join("\n• ")}"
    else
      # Create list from key sentences
      sentences = content.split(/[.!?]+/).map(&:strip).reject(&:empty?)
      key_sentences = sentences.first(3)
      key_sentences.empty? ? "• Key insight 1\n• Key insight 2\n• Key insight 3" : "• #{key_sentences.join("\n• ")}"
    end
  end

  def extract_demographics(content)
    # Look for demographic information
    demo_keywords = ['age', 'demographic', 'target', 'audience', 'group', 'urban', 'rural', 'income']
    demo_sentences = content.split(/[.!?]+/).select do |sentence|
      demo_keywords.any? { |keyword| sentence.downcase.include?(keyword) }
    end

    if demo_sentences.any?
      demo_sentences.first.strip
    else
      "Target audience analysis based on data trends"
    end
  end

  def extract_recommendations(content)
    # Look for recommendation keywords or create generic ones
    rec_keywords = ['recommend', 'suggest', 'should', 'opportunity', 'next steps', 'action']
    rec_sentences = content.split(/[.!?]+/).select do |sentence|
      rec_keywords.any? { |keyword| sentence.downcase.include?(keyword) }
    end

    if rec_sentences.any?
      rec_sentences.first.strip
    else
      "Strategic recommendations based on analysis findings"
    end
  end

  # Additional helper methods for social trends presentations
  def extract_statistics(content)
    # Extract numerical statistics and metrics
    stats = content.scan(/\d+[,.]?\d*\s*%?[^.]*(?:posts|growth|sentiment|conversations|mentions)/).first(3)
    if stats.any?
      "Key Statistics: #{stats.join(', ')}"
    else
      "Comprehensive social media analysis with key performance metrics"
    end
  end

  def extract_descriptors(content)
    # Extract descriptive terms and keywords
    descriptors = content.scan(/(?:popular|trending|top|emerging|key)\s+([^.,:]+)/).flatten.first(5)
    if descriptors.any?
      "• #{descriptors.join("\n• ")}"
    else
      "• Premium quality\n• Innovative flavors\n• Consumer favorites\n• Market leaders\n• Trending options"
    end
  end

  def extract_trends(content)
    # Extract trend-related information
    trend_keywords = ['trend', 'growth', 'increase', 'popular', 'emerging', 'innovation']
    trend_sentences = content.split(/[.!?]+/).select do |sentence|
      trend_keywords.any? { |keyword| sentence.downcase.include?(keyword) }
    end

    if trend_sentences.any?
      trend_sentences.first(2).join('. ').strip
    else
      "Emerging flavor innovations driving consumer interest and market growth"
    end
  end

  def extract_insights(content)
    # Extract analytical insights
    insight_keywords = ['insight', 'analysis', 'finding', 'reveals', 'shows', 'indicates']
    insight_sentences = content.split(/[.!?]+/).select do |sentence|
      insight_keywords.any? { |keyword| sentence.downcase.include?(keyword) }
    end

    if insight_sentences.any?
      insight_sentences.first.strip
    else
      "Data analysis reveals significant consumer behavior patterns and preferences"
    end
  end

  def extract_social_posts(content)
    # Extract social media post examples
    if content.include?('"') || content.include?("'")
      # Extract quoted content
      quotes = content.scan(/"([^"]+)"/).flatten + content.scan(/'([^']+)'/).flatten
      quotes.first || "Amazing experience with this product! Highly recommend to everyone. #trending #quality"
    else
      "Positive consumer feedback highlighting product quality and satisfaction"
    end
  end

  def extract_marketing_opportunities(content)
    # Extract or create marketing opportunities
    opp_keywords = ['opportunity', 'market', 'target', 'promote', 'advertise', 'campaign']
    opp_sentences = content.split(/[.!?]+/).select do |sentence|
      opp_keywords.any? { |keyword| sentence.downcase.include?(keyword) }
    end

    if opp_sentences.any?
      "• #{opp_sentences.first(3).join("\n• ")}"
    else
      "• Target urban millennials\n• Emphasize quality and innovation\n• Leverage social media trends\n• Partner with influencers"
    end
  end
end
