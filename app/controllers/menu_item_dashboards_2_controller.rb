class MenuItemDashboards2Controller < ApplicationController
  before_action :set_menu_item_dashboard
  layout 'dashboard_kit'

  def show
  end

  def menu_mentions
    render json: {
      menu_mentions: data[:menu_mentions_chart_data],
      change: data[:change]
    }
  end

  def top_cousines
    render json: data[:top_cuisines_chart_data]
  end

  def top_chains
    render json: data[:top_chains_chart_data]
  end

  def top_regions
    render json: data[:top_regions_chart_data]
  end

  def menu_mentions_table
    render json: []
  end

  def quarters
    render json: @menu_item_dashboard.quarters
  end

  private
    def set_menu_item_dashboard
      @menu_item_dashboard = MenuItemDashboard.find(params[:id] || params[:board_id])
    end

    def graph_service
      params_hash = params.permit!.to_h.select { |_key, value| value.present? }
      @graph_service ||= MenuItemDashboards::GraphDataService.new(params: params_hash, current_user: current_user, menu_item_dashboard: @menu_item_dashboard)
    end

    def data
      @data ||= graph_service.collect_data(lazy: true).with_indifferent_access
    end
end
