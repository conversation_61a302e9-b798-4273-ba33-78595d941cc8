class MenuApi::V1::MenuItems::ItemsController < MenuApi::V1::BaseController

  def index
    page = params[:page] || 1
    per_page = params[:per_page] || 20
    quarter = params[:quarter] || TimePeriod.last.quarter
    year = params[:year] || TimePeriod.last.year
    menu_item = MenuItem.find_by_guid params[:menu_item_id]

    response = Item.es_with_images(query: menu_item.name, quarter: quarter, year: year, page: page, per_page: per_page)

    render json: {
      page: page,
      per_page: per_page,
      total_records: response.total_count,
      data: response.records.to_a.map{|item|
        {
          id: item.id,
          name: item.name,
          description: item.description,
          price: item.price,
          image_url: item.image_url,
          restaurant_type: item.restaurant&.restaurant_type&.to_s,
          business_name: item.restaurant&.business_name&.to_s,
          cuisine: item.restaurant&.categories&.pluck(:name),
          city: item.restaurant&.city&.name&.to_s
        }
      }
    }
  end

end