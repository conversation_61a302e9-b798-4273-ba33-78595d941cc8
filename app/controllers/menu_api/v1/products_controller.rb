class MenuApi::V1::ProductsController < MenuApi::V1::BaseController
  def fastest_growing_ingredient_category
    render json: Rails.cache.fetch(cash_key[:fastest_growing_ingredient_category], expires_in: 12.hours) {
      ProductBlueprint.render(Product.top_l2_fastest_growing_categories, view: :fastest_growing)
    }
  end

  def white_space_opportunities
    render json: ProductBlueprint.render(Product.white_space_opportunities(sort_order: 'none'), view: :white_space_opportunities)
  end

  private

  def store_ids
    [1028, 590, 1544, 1102, 312, 2250, 159]
  end

  def cash_key
    {
      fastest_growing_ingredient_category: "products/fastest_growing_ingredient_category"
    }
  end
end

