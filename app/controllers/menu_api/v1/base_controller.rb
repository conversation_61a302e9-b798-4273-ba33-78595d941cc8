class MenuApi::V1::BaseController < ApplicationController
  ES_SEARCH = true

  before_action :restrict_access

  private

  def restrict_access
    authenticate_or_request_with_http_token do |token, options|
      decode_token(token)
    end
  end

  def decode_token(token)
    user_id = User.decode_jwt_token(token)
    @current_user = User.find_by_id user_id
  end

  def current_user
    @current_user
  end

end