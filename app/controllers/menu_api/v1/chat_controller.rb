class MenuApi::V1::ChatController < MenuApi::V1::BaseController

  def index
    render json: MenuApi::ChatsDatatable.new(view_context).response
  end

  def create
    @chat = Chat.create(:topic => params[:message], :user_id => current_user.id)
    @message = @chat.chat_messages.create(:message => params[:message], message_type: 'user')
    MenuDataBot::ProcessRequestWorker.perform_async(@chat.id, @message.id)

    render json: @chat.as_json
  end

  def show
    @chat = Chat.find_by_guid params[:id]
    @messages = @chat.chat_messages
    render json: {
      chat: @chat.as_json,
      messages: @messages.as_json
    }
  end

end