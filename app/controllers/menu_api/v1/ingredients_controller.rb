class MenuApi::V1::IngredientsController < MenuApi::V1::BaseController
  CACHE_TIME = 12.hours
  before_action :set_ingredient, except: %i[index fastest_growing_ingredients highest_growing_ingredients pairing_trends ingredients_innovation ingredients_by_stage]

  def index
    render json: MenuApi::AllIngredientsDatatable.new(view_context).response
  end

  def show
    render json: @ingredient.as_json(
      except: [:id],
      only: [
        :guid,
        :name,
        :description,
        :pen_rate,
        :appearance,
        :taste,
        :aroma,
        :texture,
        :menu_adoption,
        :delta_last_period,
        :change_1y,
        :growth_prediction
      ]
    ).merge(
      :google_trend => @ingredient.google_trend.to_f,
      :category => @ingredient.menu_categories.first&.name,
      :images => @ingredient.carousel_images.as_json(:only => [:url])
    )
  end

  def images
    render json: {
      images: @ingredient.carousel_images.map(&:url)
    }
  end

  def categories
    render json: {
      categories: @ingredient.menu_categories.map(&:name)
    }
  end

  def google_trends
    render json: {
      google_trends: @ingredient.google_trends.map { _1.yearly_trends }
    }
  end

  def ingredient_pairing_trends
    @ingredient = Ingredient.find_by_guid params[:ingredient_id]
    @pairing_trends = IngredientPairingTrend.where(:ingredient_a_id => @ingredient.id).order('pairing_percent DESC').limit(params[:limit] || 3)

    render json: {
      data:
        @pairing_trends.map{|p|
          {
            id: p.id,
            ingredient_a_name: p.ingredient_a&.name,
            ingredient_a_guid: p.ingredient_a&.guid,
            ingredient_b_name: p.ingredient_b&.name,
            ingredient_b_guid: p.ingredient_b&.guid,
            category: p.category,
            pairing_percent: p.pairing_percent,
            change: p.change
          }
        }
    }
  end

  def menu_types
    @ingredient = Ingredient.find_by_guid params[:ingredient_id]
    raise StandardError.new("Ingredient not found.") if @ingredient.nil?
    ingredient_name = @ingredient.name
    quarter = params[:quarter] || TimePeriod.last&.quarter
    year = params[:year] || TimePeriod.last&.year
    hash_key = Digest::MD5.hexdigest({ingredient_name: ingredient_name, quarter: quarter, year: year, title: 'es_category_distribution'}.to_s)
    response = Rails.cache.fetch(hash_key, expires_in: 1.hour) do
      Item.es_category_distribution query: @ingredient.name
    end
    render json: { data: response.as_json }
  rescue StandardError => e
    render json: { error: e.message }, status: :internal_server_error
  end

  def fastest_growing_ingredients
    render json: MenuApi::FastestGrowingIngredientsDatatable.new(view_context).response
  end

  def highest_growing_ingredients
    ingredients = Ingredient.highest_growing_ingredients.limit(5)

    render json: ingredients.as_json(except: [:id])
  end

  def pairing_trends
    render json: MenuApi::PairingTrendsDatatable.new(view_context).response
  end

  def ingredients_innovation
    if params[:preview].present?
      render json: Ingredient.by_innovation.limit(5).as_json(except: [:id])
    else
      render json: MenuApi::InnovationIngredientsDatatable.new(view_context).response
    end
    # render json: Ingredient.by_innovation.limit(5).as_json(except: [:id])
  end

  def ingredients_by_stage
    @ingredients = Ingredient.all

    if params[:filters].present?
      filters = params[:filters].to_unsafe_h
      @ingredients = @ingredients.filter_by(filters.slice(*%i[category menu_adoption aroma texture taste appearance]))
      if filters["favorite"].present?
        @ingredients = @ingredients.filter_by_favorite(current_user.id, filters["favorite"].first)
      end
    end

    render json:
    {
      emergence: @ingredients.by_menu_adoption('emergence').as_json(except: [:id]),
      growth: @ingredients.by_menu_adoption('growth').as_json(except: [:id]),
      mainstream: @ingredients.by_menu_adoption('mainstream').as_json(except: [:id]),
      mature: @ingredients.by_menu_adoption('mature').as_json(except: [:id])
    }
  end

  def pen_rate_over_time
    @ingredient = Ingredient.find_by_guid params[:ingredient_id]

    render json: @ingredient.pen_rate_over_time.as_json
  end

  def consummation_habbits
    sentiments = @ingredient.consumer_sentiment_likes
    if sentiments.blank?
      render json: {
        percentages: []
      }

      return
    end

    total = sentiments.count

    render json: {
      percentages: [
        (sentiments.where(familirity_score: 1).count.to_f / total * 100).round(2),
        (sentiments.where(familirity_score: 2).count.to_f / total * 100).round(2),
        (sentiments.where(familirity_score: 3).count.to_f / total * 100).round(2),
        (sentiments.where(familirity_score: 4).count.to_f / total * 100).round(2),
        (sentiments.where(familirity_score: 5).count.to_f / total * 100).round(2)
      ]
    }
  end

  def social_sentiments
    sentiments = @ingredient.consumer_sentiment_likes

    if sentiments.blank?
      render json: {
        percentages: []
      }

      return
    end

    total = sentiments.count

    render json: {
      percentages: [
        (sentiments.where(score: 1).count.to_f / total * 100).round(2),
        (sentiments.where(score: 2).count.to_f / total * 100).round(2),
        (sentiments.where(score: 3).count.to_f / total * 100).round(2),
        (sentiments.where(score: 4).count.to_f / total * 100).round(2),
        (sentiments.where(score: 5).count.to_f / total * 100).round(2),
      ]
    }
  end

  def top_retailers
    render json: Rails.cache.fetch(cash_key[:top_retailers], expires_in: CACHE_TIME) { ES_SEARCH ? IngredientBlueprint.render(@ingredient, view: :top_retails_by_banner_name) : RetailInsightBlueprint.render(@ingredient.retail_insights_dashboard, view: :top_retails_by_banner_name) }
  end

  def top_manufacturers
    render json: Rails.cache.fetch(cash_key[:top_manufacturers], expires_in: CACHE_TIME) { ES_SEARCH ? IngredientBlueprint.render(@ingredient, view: :top_manufacturers) : RetailInsightBlueprint.render(@ingredient.retail_insights_dashboard, view: :top_brands) }
  end

  def retails_images
    render json: Rails.cache.fetch(cash_key[:retails_images], expires_in: CACHE_TIME) { IngredientBlueprint.render(@ingredient, view: :retails_carousel_images) }
  end

  def most_common_product_category
    store_ids = params[:store_ids]&.split(',')&.map(&:to_i)
    render json: Rails.cache.fetch(cash_key[:most_common_product_category], expires_in: CACHE_TIME) { IngredientBlueprint.render(@ingredient, view: (ES_SEARCH.present? ? :most_common_product_category_es : :most_common_product_category), params: { store_ids: store_ids }) }
  end

  def highest_growing_ingredient_by_category
    render json: Rails.cache.fetch(cash_key[:highest_growing_ingredient_by_category], expires_in: CACHE_TIME) {
      if ES_SEARCH
        IngredientBlueprint.render(Product.top_l2_growth_by_ingredient_es(name: @ingredient.name), view: :highest_growing_ingredient_by_category)
      else
        ProductBlueprint.render(Product.top_l2_fastest_growing_categories(collection: @ingredient.products_pg_search, quarter: nil), view: :fastest_growing)
      end
    }
  end

  def retail_growth
    render json: Rails.cache.fetch(cash_key[:retail_growth], expires_in: CACHE_TIME) { IngredientBlueprint.render(@ingredient, view: (ES_SEARCH ? :retail_growth_es : :retail_growth)) }
  end

  def mentions_by_state
    cache_key = "mentions_by_state_#{@ingredient.name}"
    formatted_states = Rails.cache.fetch(cache_key, expires_in: 1.day) do
      Constants::US_STATESUS_STATES.map do |state|
        {
          id: state[:id],
          name: state[:name],
          abbreviation: state[:abbreviation],
          mentions: Infegy.new.mentions_by_state(@ingredient.name, state[:abbreviation].downcase)
        }
      end
    end

    render json: formatted_states
  end

  def consumer_experience
    consumer_sentiment_likes = @ingredient.consumer_sentiment_likes.where('score > 0')

    if consumer_sentiment_likes.blank?
      return render json: nil
    end

    render json: {
      consumer_who_likes: {
        count: consumer_sentiment_likes.who_likes.size,
        male: consumer_sentiment_likes.who_likes.for_male.size,
        female: consumer_sentiment_likes.who_likes.for_female.size
      },
      consumer_who_neutral: {
        count: consumer_sentiment_likes.who_neutral.size,
        male: consumer_sentiment_likes.who_neutral.for_male.size,
        female: consumer_sentiment_likes.who_neutral.for_female.size
      },
      consumer_who_dislikes: {
        count: consumer_sentiment_likes.who_dislikes.size,
        male: consumer_sentiment_likes.who_dislikes.for_male.size,
        female: consumer_sentiment_likes.who_dislikes.for_female.size
      },
      total: consumer_sentiment_likes.size
    }
  end

  def consumed_by_generation
    sentiments = @ingredient.consumer_sentiment_likes

    if sentiments.blank?
      render json: {
        age_percentages: {},
        ethnicity_percentages: {}
      }
      return
    end

    total = sentiments.size

    render json: {
      age_percentages: {
        'Gen Z': (sentiments.where('age >= 13 AND age <= 28').size.to_f / total * 100).round(2),
        'Millennial': (sentiments.where('age >= 29 AND age <= 44').size.to_f / total * 100).round(2),
        'Gen X': (sentiments.where('age >= 45 AND age <= 60').size.to_f / total * 100).round(2),
        'Baby Boomer': (sentiments.where('age >= 61').size.to_f / total * 100).round(2)
      },
      ethnicity_percentages: {
        'Asian': (sentiments.where('ethnicity = ?', 'Asian').size.to_f / total * 100).round(2),
        'Black': (sentiments.where('ethnicity = ?', 'Black').size.to_f / total * 100).round(2),
        'White': (sentiments.where('ethnicity = ?', 'White').size.to_f / total * 100).round(2),
        'Mixed': (sentiments.where('ethnicity = ?', 'Mixed').size.to_f / total * 100).round(2),
        'Other': (sentiments.where('ethnicity = ?', 'Other').size.to_f / total * 100).round(2)
      }
    }
  end

  def products_table
    search_params = products_table_params.merge(exact_match: '1', l_category_exist: 'l1', sku: 'not_nil')
    cache_key = "products_table/#{params[:ingredient_id]}/#{Digest::MD5.hexdigest(search_params.to_json)}"

    response = Rails.cache.fetch(cache_key, expires_in: 4.hours) do
      result = Product.es_search(@ingredient.name, search_params)
      IngredientBlueprint.render_as_json(@ingredient, view: :products_table, locals: { es_result: result })
    end

    render json: response
  end

  private

  def set_ingredient
    @ingredient = Ingredient.find_by_guid(params[:ingredient_id] || params[:id])
    render json: { error: 'Ingredient not found' }, status: :not_found unless @ingredient
  end

  def cash_key
    {
      top_retailers: "ingredient/#{@ingredient.id}/top_retailers",
      top_manufacturers: "ingredient/#{@ingredient.id}/top_manufacturers",
      retails_images: "ingredient/#{@ingredient.id}/retails_images",
      most_common_product_category: "ingredient/#{@ingredient.id}/most_common_product_category",
      highest_growing_ingredient_by_category: "ingredient/#{@ingredient.id}/highest_growing_ingredient_by_category",
      retail_growth: "ingredient/#{@ingredient.id}/retail_growth",
    }
  end

  def products_table_params
    params[:sort][:column] = 'l1' if params[:sort][:column] == 'category'

    {
      page: params[:page] || 1,
      per_page: params[:per_page] || 10,
      sort: params[:sort] ? { column: params[:sort][:column], direction: params[:sort][:direction] } : nil,
      search: params[:search]
    }
  end
end
