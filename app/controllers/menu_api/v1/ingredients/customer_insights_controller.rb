
class MenuApi::V1::Ingredients::CustomerInsightsController < ApplicationController
  def most_talked_about
    data, total_count = Ingredient.most_talked_about(params[:page], params[:per_page])
    render json: {
      data: ,
      total_count:
    }
  end

  def most_liked_ingredients
    max_like_score = ConsumerSentimentLike.maximum(:like_score_average)

    subquery = <<-SQL
      SELECT DISTINCT ON (ingredient_id) *
      FROM consumer_sentiment_likes
      WHERE like_score_average IS NOT NULL
      ORDER BY ingredient_id, like_score_average DESC
    SQL

    top_likes = ConsumerSentimentLike
      .from("(#{subquery}) AS consumer_sentiment_likes")
      .includes(:ingredient)
      .order('like_score_average DESC')
      .limit(5)

    data = top_likes.map do |like|
      {
        guid: like.ingredient.guid,
        name: like.ingredient.name,
        menu_adoption: like.ingredient.menu_adoption,
        penetration: like.ingredient.pen_rate,
        liked_by: like.like_score_average.to_f / max_like_score
      }
    end

    render json: data
  end

  def consumer_favorite_retailers
    render json: ConsumersFavoriteRetailer.order(count: :desc).limit(3).as_json
  end
end
