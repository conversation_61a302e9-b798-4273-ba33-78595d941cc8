
class MenuApi::V1::Ingredients::CustomerInsightsController < ApplicationController
  def most_talked_about
    data, total_count = Ingredient.most_talked_about(**permitted_params.to_h.symbolize_keys)
    render json: {
      data: ,
      total_count:
    }
  end

  def most_liked_ingredients
    likes_count = ConsumerSentimentLike.where(score: [4, 5]).count

    top_likes, total_count = Ingredient.most_liked(**permitted_params.to_h.symbolize_keys)

    data = top_likes.map do |ingredient|
      ingredient.as_json.merge(
        liked_by: ingredient.likes_count.to_f / likes_count * 100
      )
    end

    render json: {
      data:,
      total_count:
    }
  end

  def consumer_favorite_retailers
    render json: ConsumersFavoriteRetailer.order(count: :desc).limit(3).as_json
  end

  private

  def permitted_params
    params.permit(:page, :per_page, :sort, :search)
  end
end
