class MenuApi::V1::MenuItemsController < MenuApi::V1::BaseController

  def index
    render json: MenuApi::MenuItems::AllMenuItemsDatatable.new(view_context).response
  end


  def show
    @menu_item = MenuItem.find_by_guid params[:id]
    render json: @menu_item.as_json(
      except: [:id],
      only: [
        :guid,
        :name,
        :description,
        :appearance,
        :taste,
        :aroma,
        :texture,
        :menu_adoption,
        :pen_rate,
        :foodservice_growth,
        :foodservice_prediction,
        :google_trend
      ]
    ).merge(
      :category => "",
      :images => []
    )
  end

  def pen_rate_over_time
    @menu_item = MenuItem.find_by_guid params[:menu_item_id]

    render json: @menu_item.pen_rate_over_time.as_json
  end



  # todo:
  def highest_reviewed_dishes
    @menu_item = MenuItem.find_by_guid params[:menu_item_id]

    render json: []
  end

  # todo:
  def cuisine_distro
    @menu_item = MenuItem.find_by_guid params[:menu_item_id]

    render json: []
  end

  # todo:
  def meal_type_distro
    @menu_item = MenuItem.find_by_guid params[:menu_item_id]

    render json: []
  end

  def fastest_growing_dish_categories_chart
    data = GmiSubcategory.order("foodservice_growth DESC").limit(5).map do |g|
      {
        name: g.name,
        foodservice_growth: g.foodservice_growth,
        quarters: g.quarters.last_year.map{|q|
          {
            quarter: q.quarter,
            year: q.year,
            pen_rate: q.pen_rate
          }
        }
      }
    end
    render json: data
  end

  def fastest_growing_dish_categories_table
    render json: MenuApi::MenuItems::FastestGrowingDishCategories.new(view_context).response
  end

  def menu_items_innovation
    page = params[:page] || 1
    per_page = params[:per_page] || 20
    quarter = params[:quarter] || TimePeriod.last.quarter
    year = params[:year] || TimePeriod.last.year

    response = Item.es_innovation_score(quarter: quarter, year: year, page: page, per_page: per_page)

    render json: {
      page: page,
      per_page: per_page,
      total_records: response.total_count,
      data: response.records.to_a.map{|item|
        {
          name: item.name,
          description: item.description,
          innovation_score: item.innovation_score.to_f,
          image_url: item.image_url
        }
      }
    }
  end

  def popular_limited_time_offers
    render json: MenuApi::MenuItems::LimitedTimeOffers.new(view_context).response
  end

  def fastest_growing_menu_items
    render json: MenuApi::MenuItems::FastestGrowingMenuItems.new(view_context).response
  end

  def highest_penetration
    render json: MenuApi::MenuItems::HighestPenetration.new(view_context).response
  end

  def geographic_popularity
    render json: [
      { id: "01", value: 120 },
      { id: "02", value: 95 },
      { id: "04", value: 59 },
      { id: "05", value: 110 },
      { id: "06", value: 140 },
      { id: "08", value: 135 },
      { id: "09", value: 125 },
      { id: "10", value: 98 },
      { id: "12", value: 130 },
      { id: "13", value: 145 },
      { id: "15", value: 87 },
      { id: "16", value: 103 },
      { id: "17", value: 138 },
      { id: "18", value: 110 },
      { id: "19", value: 105 },
      { id: "20", value: 101 },
      { id: "21", value: 115 },
      { id: "22", value: 102 },
      { id: "23", value: 93 },
      { id: "24", value: 127 },
      { id: "25", value: 120 },
      { id: "26", value: 132 },
      { id: "27", value: 110 },
      { id: "28", value: 98 },
      { id: "29", value: 119 },
      { id: "30", value: 90 },
      { id: "31", value: 97 },
      { id: "32", value: 104 },
      { id: "33", value: 89 },
      { id: "34", value: 122 },
      { id: "35", value: 100 },
      { id: "36", value: 130 },
      { id: "37", value: 135 },
      { id: "38", value: 86 },
      { id: "39", value: 128 },
      { id: "40", value: 98 },
      { id: "41", value: 125 },
      { id: "42", value: 136 },
      { id: "44", value: 87 },
      { id: "45", value: 102 },
      { id: "46", value: 82 },
      { id: "47", value: 108 },
      { id: "48", value: 145 },
      { id: "49", value: 112 },
      { id: "50", value: 75 },
      { id: "51", value: 140 },
      { id: "53", value: 132 },
      { id: "54", value: 80 },
      { id: "55", value: 119 },
      { id: "56", value: 80 },
    ]
  end

  def popular_meal_types
    render json: [
      {
        name: 'Alcoholic beverages',
        pen_rate: 12.35,
        liked_by: 34.43
      },
      {
        name: 'Appetizers',
        pen_rate: 17.35,
        liked_by: 45.89
      },
      {
        name: 'Desserts',
        pen_rate: 27.35,
        liked_by: 102.34
      }
    ]
  end

  def most_popular_cuisines
    render json: [
      {
        name: 'French',
        share: 12.35,
        liked_by: 34.43
      },
      {
        name: 'Latin American',
        share: 17.35,
        liked_by: 45.89
      },
      {
        name: 'African',
        share: 27.35,
        liked_by: 102.34
      }
    ]
  end

  def most_talked_about_menu_items
    render json: [
      {
        name: 'Scallion pancakes',
        sentiments: {
          'strongly-dislike': 10,
          'somewhat-dislike': 25,
          neutral: 25,
          'somewhat-like': 25,
          'strongly-like': 15
        },
        pen_rate: 2.52
      },
      {
        name: 'BBQ pulled chicken',
        sentiments: {
          'somewhat-dislike': 10,
          neutral: 45,
          'somewhat-like': 20,
          'strongly-like': 25
        },
        pen_rate: 5.44
      },
      {
        name: 'Coconut macaroon',
        sentiments: {
          neutral: 60,
          'somewhat-like': 15,
          'strongly-like': 25
        },
        pen_rate: 5.44
      },
      {
        name: 'Chocolate brownie',
        sentiments: {
          neutral: 55,
          'somewhat-like': 30,
          'strongly-like': 15
        },
        pen_rate: 5.44
      },
      {
        name: 'Stuffed jalapeno poppers',
        sentiments: {
          'somewhat-dislike': 15,
          neutral: 40,
          'somewhat-like': 20,
          'strongly-like': 25
        },
        pen_rate: 67.44
      }
    ]
  end

  def most_liked_menu_items
    render json: [
      {
        name: 'Frapuccino',
        menu_mentions: 142,
        pen_rate: 5.44,
        liked_by: 85.44
      },
      {
        name: 'Lamb shank',
        menu_mentions: 105,
        pen_rate: 42.52,
        liked_by: 87.25
      },
      {
        name: 'Frittata',
        menu_mentions: 92,
        pen_rate: 5.44,
        liked_by: 78.45
      },
      {
        name: 'Cauliflower rice',
        menu_mentions: 92,
        pen_rate: 5.44,
        liked_by: 12.44
      },
      {
        name: 'Raspberry tart',
        menu_mentions: 101,
        pen_rate: 5.44,
        liked_by: 67.44
      }
    ]
  end
end
