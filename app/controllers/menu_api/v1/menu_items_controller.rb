class MenuApi::V1::MenuItemsController < MenuApi::V1::BaseController

  def index
    @menu_items = MenuItem.all
    render json: @menu_items.select('guid, name').as_json(except: [:id])
  end


  def show
    @menu_item = MenuItem.find_by_guid params[:id]
    render json: @menu_item.as_json(except: [:id], :only => [:guid, :name])
  end

  def fastest_growing_dish_categories_chart
    render json: []
  end

  def fastest_growing_dish_categories_table
    render json: []
  end

  def menu_items_innovation
    render json: []
  end

  def popular_limited_time_offers
    render json: []
  end

  def highest_penetration
    render json: []
  end






end