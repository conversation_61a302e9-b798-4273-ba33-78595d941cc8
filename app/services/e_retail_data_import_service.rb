class ERetailDataImportService
  REQUIRED_FIELDS = %i[
    chain host latitude longitude store_id store_number city address state
    title image_url cat_l0 image_url
  ]
  ADDITIONAL_FIELDS_KEYS = %i[ product_code_type channel_method banner_name cat_l2 cat_l3 listing_url best_price
    best_price_multiple best_price_units ]

  DELETE_FILE_AFTER_IMPORT = true
  BATCH_SIZE = 1000

  def initialize(csv_url, logger)
    @csv_url  = csv_url
    @logger   = logger

    @row_skipping          = []

    @retailers             = {}
    @stores                = {}
    @product_categories    = {}
    @product_subcategories = {}
  end

  def import
    @logger.info("------------------E-Retail data import Starting------------------")
    prnt '----Import Start----'

    file_handler = FileHandlerService.new(@csv_url)
    local_file_paths = Array(file_handler.fetch_file)

    return prnt 'Local Files is Empty' if local_file_paths.empty?

    total_elapsed_time = Benchmark.realtime do
      local_file_paths.each do |local_file_path|
        file_size = File.size(local_file_path)
        file_name = File.basename(local_file_path)

        existing_import = ImportedCsvFile.success.find_by(file_name: file_name, file_size: file_size)
        if existing_import
          prnt "File #{file_name} already imported successfully, skipping."
          @logger.info "File #{file_name} already imported successfully, skipping."
          next
        end

        imported_csv_file = ImportedCsvFile.find_or_create_by(
          file_name: file_name,
          file_size: file_size,
        )

        imported_csv_file.link_path = @csv_url

        begin
          file_elapsed_time = Benchmark.realtime do
            process_file(local_file_path, imported_csv_file)
          end

          imported_csv_file.update!(
            status: :success,
            imported_at: Time.current,
            import_duration: format_elapsed_time(file_elapsed_time),
            error_message: nil
          )
        rescue CSV::MalformedCSVError => e
          @logger.error("Failed to parse CSV: #{e.message}")
          imported_csv_file.mark_as_failed(e.message)
          raise
        rescue StandardError => e
          @logger.error("Import failed: #{e.message}")
          imported_csv_file.mark_as_failed(e.message)
          raise
        ensure
          File.delete(local_file_path) if DELETE_FILE_AFTER_IMPORT && File.exist?(local_file_path)
        end
      end
    end

    @logger.info("Total time taken: #{format_elapsed_time(total_elapsed_time)} seconds")
    @logger.info("-----------E-Retail data import completed successfully-----------")
    prnt '----Import Ended----'
  end

  private

  def process_file(file_path, imported_csv_file)
    prnt ">>>>> Starting import from file: #{file_path}"
    @logger.info(">>>>> Starting import from file: #{file_path}")

    skipped_rows = []
    missing_fields_rows = []
    total_rows = 0

    CSV.foreach(file_path, headers: true, header_converters: :symbol).each_slice(BATCH_SIZE).with_index do |rows, batch_index|
      total_rows += rows.size

      ActiveRecord::Base.transaction do
        rows.each_with_index do |row, index|
          row_number = batch_index * BATCH_SIZE + index + 2
          data = row.to_h.compact
          missing = REQUIRED_FIELDS.reject { |f| data[f].present? }

          if missing.any?
            log_missing_fields(row_number, missing)
            missing_fields_rows << { row_number => missing } # row_number and missing_fields
            next
          end

          if skip_row?(row)
            skipped_rows << row_number
            next
          end

          process_row(data, row_number)
        end
      end

      prnt ">> Processed #{rows.size * (batch_index + 1)} rows"
    end

    @logger.info ">>> Skipped rows amount: #{skipped_rows.size} | Rows numbers: #{skipped_rows}"
    @logger.info ">>> Rows with missing fields: #{missing_fields_rows}"

    imported_csv_file.update!(skipped_rows: , missing_fields_rows: , total_rows: total_rows )
  end

  def process_row(data, row_number)
    retailer = find_or_create_retailer(data)
    store = find_or_create_store(data, retailer)
    product_category = find_or_create_product_category(data, retailer, store)
    product_subcategory = find_or_create_product_subcategory(data, retailer, product_category, store)
    product = find_or_create_product(data, retailer, store, product_category, product_subcategory)

    process_pricing_data(product, data)
    add_product_to_catalog(product, retailer, data)
    attach_image_to_product(product, data)
  rescue StandardError => e
    @logger.error("Row #{row_number} Error: #{e.message}")
  end

  def find_or_create_retailer(data)
    key = "#{data[:chain]}-#{data[:host]}"
    @retailers[key] ||= Retailer.datasembly.find_or_create_by(
      name: data[:chain]&.strip,
      base_uri: data[:host]
    )
  end

  def find_or_create_store(data, retailer)
    key = "#{retailer.id}-#{data[:store_id]}-#{data[:latitude]}-#{data[:longitude]}"
    @stores[key] ||= Store.find_or_create_by(
      external_id: data[:store_id],
      latitude: data[:latitude].to_f,
      longitude: data[:longitude].to_f,
      retailer_id: retailer.id
    ) do |s|
      s.store_number = data[:store_number]
      s.city         = data[:city]
      s.address      = data[:address]
      s.state_name   = data[:state]&.strip
    end
  end

  def find_or_create_product_category(data, retailer, store)
    key = "#{retailer.id}-#{store.id}-#{data[:cat_l0]}"
    @product_categories[key] ||= ProductCategory.find_or_create_by(
      name: data[:cat_l0]&.strip,
      retailer_id: retailer.id,
      store_id: store.id
    )
  end

  def find_or_create_product_subcategory(data, retailer, product_category, store)
    return unless data[:cat_l1].present?

    key = "#{retailer.id}-#{store.id}-#{data[:cat_l1]}"
    @product_subcategories[key] ||= ProductSubcategory.find_or_create_by(
      name: data[:cat_l1]&.strip,
      retailer_id: retailer.id,
      is_tracking: false,
      store_id: store.id,
      product_category_id: product_category.id
    )
  end

  def find_or_create_product(data, retailer, store, product_category, product_subcategory)
    year = nil
    quarter = nil

    if data[:week]&.match?(/\A\d{4}-\d{2}-\d{2}\z/)
      begin
        current_date = Date.parse(data[:week])
        year = current_date.year
        quarter = determine_quarter(current_date.month)
      rescue Date::Error
        @logger.warn("Invalid date format for week_date: #{data[:week]}")
      end
    end

    Product.find_or_create_by(
      week_date: data[:week],
      upc: data[:product_code]&.strip,
      sku: data[:sku]&.strip,
      product_category_id: product_category.id,
      product_subcategory_id: product_subcategory&.id,
      retailer_id: retailer.id,
      store_id: store.id
    ) do |product|
      update_product_fields(product, data)
      product.year = year
      product.quarter = quarter
    end
  end

  def determine_quarter(month)
    case month
    when 1..3 then 1
    when 4..6 then 2
    when 7..10 then 3
    when 11..12 then 4
    else nil
    end
  end

  def update_product_fields(pr, data) # pr -> product
    pr.name              = data[:title]&.strip
    pr.brand             = data[:brand]&.strip
    pr.price             = data[:list_price]&.to_f
    pr.slug              = data[:title]&.parameterize
    pr.pack_size         = data[:list_price_multiple]
    pr.uom               = data[:list_price_units]&.strip
    pr.composition       = data[:product_ingredients]&.strip
    pr.additional_fields = get_additional_fields(data)
  end

  def get_additional_fields(data)
    additional_fields = {}
    ADDITIONAL_FIELDS_KEYS.each {|key| additional_fields[key] = data[key]&.strip if data[key].present? }
    additional_fields
  end

  def process_pricing_data(product, data)
    current_date = Date.parse(data[:week]) rescue nil
    return unless current_date

    PricingDataPoint.find_or_create_by(
      product_id: product.id,
      day: current_date.day,
      month: current_date.month,
      year: current_date.year
    ) do |pdp|
      pdp.price   = product.price
      pdp.quarter = current_date.quarter
    end
  end

  def add_product_to_catalog(product, retailer, data)
    current_date = Date.parse(data[:week]) rescue nil
    return unless current_date

    catalog = ProductCatalog.find_or_create_by(
      retailer_id: retailer.id,
      quarter: get_manual_quarter(current_date),
      year: current_date.year
    )
    catalog.products << product unless catalog.products.exists?(id: product.id)
  end

  def attach_image_to_product(product, data)
    Image.find_or_create_by(url: data[:image_url].strip, product_id: product.id)
  end

  def log_missing_fields(row_number, missing)
    message = "Row #{row_number} missing fields: #{missing.join(', ')}"
    @logger.warn(message)
    prnt message
  end

  def prnt(mes)
    puts "[FileHandlerService] #{mes}"
  end

  def valid_url?(string)
    uri = URI.parse(string)
    uri.is_a?(URI::HTTP) || uri.is_a?(URI::HTTPS)
  rescue URI::InvalidURIError
    false
  end

  def skip_row?(row)
    !valid_url?(row[:image_url]) || !valid_number?(row[:latitude])
  end

  def valid_number?(string)
    Float(string)
    true
  rescue ArgumentError
    false
  end

  def format_elapsed_time(seconds)
    hours = (seconds / 3600).to_i
    minutes = ((seconds % 3600) / 60).to_i
    remaining_seconds = (seconds % 60).to_i

    "#{hours}h #{minutes}m #{remaining_seconds}s"
  end

  def get_manual_quarter(current_date)
    rules = { 3 => ['October'] }

    month_name = current_date.strftime('%B')
    rules.each do |quarter, months|
      return months.include?(month_name) ? quarter : current_date.quarter
    end
  end
end
