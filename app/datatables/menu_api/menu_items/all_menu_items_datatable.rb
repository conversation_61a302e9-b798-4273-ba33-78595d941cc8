class MenuApi::MenuItems::AllMenuItemsDatatable < MenuApi::ReactDatatable

  def data
    query = <<-SQL_QUERY
menu_items.guid,
menu_items.name,
menu_items.menu_adoption,
menu_items.pen_rate,
menu_items.foodservice_growth
    SQL_QUERY

    @menu_items = MenuItem.select(query)

    if params[:search].present?
      search = params[:search].to_s.downcase
      @menu_items = @menu_items.where('
      lower(menu_items.name) ILIKE ?', "%#{search}%")
    end

    @menu_items
  end

  def response
    entries = data.order(sorting).page(page).per(per_page)

    {
      page: page,
      per_page: per_page,
      total_records: entries.total_count,
      data: entries.as_json(:except => [:id])
    }
  end

end