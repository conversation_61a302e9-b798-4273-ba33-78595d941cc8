class MenuApi::MenuItems::HighestPenetration < MenuApi::ReactDatatable
  def data
    MenuItem.all
  end

  def response
    entries = data.order(sorting).page(page).per(per_page)

    {
      page: page,
      per_page: per_page,
      total_records: entries.total_count,
      data: entries.map do |menu_item|
        {
          guid: menu_item.guid,
          name: menu_item.name,
          pen_rate: menu_item.pen_rate,
          foodservice_growth: menu_item.foodservice_growth,
          foodservice_prediction: menu_item.foodservice_prediction,
          quarters: menu_item.quarters.last(2).map{|q|
            {
              pen_rate: q.pen_rate,
              quarter: q.quarter,
              year: q.year
            }
          }
        }
      end
    }
  end
end