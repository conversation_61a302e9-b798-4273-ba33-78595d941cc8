class MenuApi::PairingTrendsDatatable < MenuApi::ReactDatatable


  def data
    @pairs = IngredientPairingTrend.ingredient_pairing_trends_table.order(sorting)

    if params[:search].present?
      search = params[:search].to_s.downcase
      @pairs = @pairs.where('
      lower(i1.name) ILIKE ? OR
 lower(i2.name) ILIKE ? OR
 lower(ingredient_pairing_trends.category) ILIKE ?', "%#{search}%", "%#{search}%", "%#{search}%")
    end

    if params[:filters].present?
      filters = params[:filters].to_unsafe_h
      @pairs = @pairs.filter_by(filters.slice(*%i[category]))
    end

    @pairs
  end

  def response
    entries = data.order(sorting).page(page).per(per_page)
    total_count = IngredientPairingTrend.from(data.select(1), :sub_query).count

    {
      page: page,
      per_page: per_page,
      total_records: total_count,
      data: entries.as_json(:except => [:id])
    }
  end

end