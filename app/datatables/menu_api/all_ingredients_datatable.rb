class MenuApi::AllIngredientsDatatable < MenuApi::ReactDatatable

  def data
    distinct_params = ["ingredients.name", sorting_column].compact
    distinct_query = "DISTINCT ON (#{distinct_params.join(",")})"
    query = <<-SQL_QUERY
#{distinct_query}
ingredients.guid,
ingredients.name,
FIRST_VALUE(menu_categories.name) OVER(partition by ingredients.name) as category,
ingredients.menu_adoption,
COALESCE(ingredients.pen_rate, 0) as pen_rate,
COALESCE(menu_item_dashboards.yearly_growth, 0) as social_mentions,
COALESCE(ingredients.delta_last_period, 0) as change,
change_1y,
COALESCE(retail_insights_dashboards.growth_percent, 0) as retail_change
    SQL_QUERY

    @ingredients = Ingredient.select(query).joins(:menu_categories).left_joins(:menu_item_dashboard).left_joins(:retail_insights_dashboard)

    if params[:search].present?
      search = params[:search].to_s.downcase
      @ingredients = @ingredients.where('
      lower(ingredients.name) ILIKE ? OR
 lower(menu_categories.name) ILIKE ? OR
 lower(ingredients.menu_adoption) ILIKE ?', "%#{search}%", "%#{search}%", "%#{search}%")
    end

    if params[:filters].present?
      filters = params[:filters].to_unsafe_h
      @ingredients = @ingredients.filter_by(filters.slice(*%i[category menu_adoption aroma texture taste appearance]))
      if filters["favorite"].present?
        @ingredients = @ingredients.filter_by_favorite(current_user.id, filters["favorite"].first)
      end
    end

    @ingredients
  end

  def response
    entries = data.order(sorting).page(page).per(per_page)

    {
      page: page,
      per_page: per_page,
      total_records: entries.total_count,
      data: entries.as_json(:except => [:id])
    }
  end

end