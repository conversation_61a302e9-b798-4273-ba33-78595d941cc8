# helper to use with https://tanstack.com/table/latest
class MenuApi::ReactDatatable
  delegate :params, to: :@view
  delegate :current_user, to: :@view

  def initialize(view, entries: [])
    @view = view
    @entries = entries
  end

  def response
    entries = @entries.order(sorting).page(page).per(per_page)

    {
      page: page,
      per_page: per_page,
      total_records: entries.total_count,
      data: entries.as_json(:except => [:id])
    }
  end

  private


  def pagination
    params['pagination']
  end

  def per_page
    return 10 if pagination.nil?
    pagination['pageSize']
  end

  def page
    return 1 if pagination.nil?
    pagination['pageIndex'].to_i + 1
  end

  def sorting_params
    params['sorting']
  end

  def sorting_column
    return if sorting_params.nil?
    sorting = sorting_params['0']
    sorting['id']
  end

  def sorting_direction
    return if sorting_params.nil?
    sorting = sorting_params['0']
    sorting['desc'] == 'true' ? 'DESC' : ''
  end

  def sorting
    "#{sorting_column} #{sorting_direction}"
  end





end