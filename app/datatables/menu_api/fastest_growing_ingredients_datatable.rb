class MenuApi::FastestGrowingIngredientsDatatable < MenuApi::ReactDatatable

  def data
    @ingredients = Ingredient.joins(:menu_categories)

    distinct_params = ["ingredients.name", sorting_column].compact
    distinct_query = "DISTINCT ON (#{distinct_params.join(",")})"

    cols = [
      'ingredients.guid',
      'ingredients.name',
      'menu_categories.name as category',
      'ingredients.menu_adoption',
      'COALESCE(ingredients.pen_rate, 0) as pen_rate',
      'COALESCE(ingredients.delta_last_period, 0) as change',
      'change_1y',
      'pen_rate_1y',
    ]

    case sorting_column
    when 'social_mentions'
      cols << 'COALESCE(menu_item_dashboards.yearly_growth, 0) as social_mentions'
      @ingredients = @ingredients.left_joins(:menu_item_dashboard)
    when 'retail_change'
      cols << 'COALESCE(retail_insights_dashboards.growth_percent, 0) as retail_change' if sorting_column == 'retail_change'
      @ingredients = @ingredients.left_joins(:retail_insights_dashboard)
    end

    @ingredients = @ingredients.select(Arel.sql("#{distinct_query} #{cols.join(', ')}"))


    if params[:search].present?
      search = params[:search].to_s.downcase
      @ingredients = @ingredients.where('
      lower(ingredients.name) ILIKE ? OR
 lower(menu_categories.name) ILIKE ? OR
 lower(ingredients.menu_adoption) ILIKE ?', "%#{search}%", "%#{search}%", "%#{search}%")
    end

    if params[:filters].present?
      filters = params[:filters].to_unsafe_h
      @ingredients = @ingredients.filter_by(filters.slice(*%i[category menu_adoption aroma texture taste appearance]))
      if filters["favorite"].present?
        @ingredients = @ingredients.filter_by_favorite(current_user.id, filters["favorite"].first)
      end
    end

    @ingredients
  end

  def response
    entries = data.order(sorting).page(page).per(per_page)

    {
      page: page,
      per_page: per_page,
      total_records: entries.total_count,
      data: entries.as_json(:except => [:id])
    }
  end

end