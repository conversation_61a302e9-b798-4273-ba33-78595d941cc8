#!/usr/bin/env ruby

# Script to assign items using PostgreSQL fallback instead of Elasticsearch
# Run in Rails console: load 'assign_items_postgres_fallback.rb'

puts "🚀 MenuItemDashboard assign_items Script (PostgreSQL Fallback)"
puts "=" * 60

# Configuration
INGREDIENT_NAME = 'apple'  # Change this to test other ingredients
FORCE_REASSIGN = false
DRY_RUN = false

puts "\n📋 Configuration:"
puts "- Ingredient: #{INGREDIENT_NAME}"
puts "- Force reassign: #{FORCE_REASSIGN}"
puts "- Dry run: #{DRY_RUN}"

# Patch methods to use PostgreSQL instead of Elasticsearch
def patch_elasticsearch_methods
  puts "\n🔧 Patching Elasticsearch methods to use PostgreSQL..."
  
  # Patch Quarter#es_search_items_count to use PostgreSQL
  Quarter.class_eval do
    def es_search_items_count_pg_fallback
      # Use PostgreSQL search instead of Elasticsearch
      items = Item.joins(:menu)
                  .where(menus: { quarter: quarter, year: year, state: 'approved' })
                  .where("LOWER(items.name) LIKE ? OR LOWER(items.description) LIKE ?", 
                         "%#{menu_item_dashboard.query.downcase}%", 
                         "%#{menu_item_dashboard.query.downcase}%")
      items.count
    end
    
    alias_method :original_es_search_items_count, :es_search_items_count
    alias_method :es_search_items_count, :es_search_items_count_pg_fallback
  end
  
  # Patch Restaurant.calc_penetration_rate_es to use PostgreSQL
  Restaurant.singleton_class.class_eval do
    def calc_penetration_rate_es_pg_fallback(query, quarter, year, city_ids: nil, restaurant_type: nil)
      # Use PostgreSQL calculation instead of Elasticsearch
      
      # Get all restaurants for the quarter/year
      total_restaurants = Restaurant.joins(:menus)
                                   .where(menus: { quarter: quarter, year: year, state: 'approved' })
                                   .distinct
      
      # Apply filters if provided
      if city_ids.present?
        total_restaurants = total_restaurants.where(city_id: city_ids)
      end
      
      if restaurant_type.present?
        total_restaurants = total_restaurants.where(restaurant_type: restaurant_type)
      end
      
      total_count = total_restaurants.count
      return 0.0 if total_count == 0
      
      # Get restaurants with the query item
      restaurants_with_item = Restaurant.joins(menus: :items)
                                       .where(menus: { quarter: quarter, year: year, state: 'approved' })
                                       .where("LOWER(items.name) LIKE ? OR LOWER(items.description) LIKE ?", 
                                              "%#{query.downcase}%", 
                                              "%#{query.downcase}%")
                                       .distinct
      
      # Apply same filters
      if city_ids.present?
        restaurants_with_item = restaurants_with_item.where(restaurants: { city_id: city_ids })
      end
      
      if restaurant_type.present?
        restaurants_with_item = restaurants_with_item.where(restaurants: { restaurant_type: restaurant_type })
      end
      
      with_item_count = restaurants_with_item.count
      
      # Calculate penetration rate as percentage
      penetration_rate = (with_item_count.to_f / total_count.to_f) * 100
      penetration_rate.round(2)
    end
    
    alias_method :original_calc_penetration_rate_es, :calc_penetration_rate_es
    alias_method :calc_penetration_rate_es, :calc_penetration_rate_es_pg_fallback
  end
  
  puts "✅ Elasticsearch methods patched to use PostgreSQL!"
end

# Restore original methods
def restore_elasticsearch_methods
  puts "\n🔄 Restoring original Elasticsearch methods..."
  
  Quarter.class_eval do
    if method_defined?(:original_es_search_items_count)
      alias_method :es_search_items_count, :original_es_search_items_count
      remove_method :original_es_search_items_count
      remove_method :es_search_items_count_pg_fallback
    end
  end
  
  Restaurant.singleton_class.class_eval do
    if method_defined?(:original_calc_penetration_rate_es)
      alias_method :calc_penetration_rate_es, :original_calc_penetration_rate_es
      remove_method :original_calc_penetration_rate_es
      remove_method :calc_penetration_rate_es_pg_fallback
    end
  end
  
  puts "✅ Original methods restored!"
end

# Helper method to check prerequisites
def check_prerequisites
  puts "\n🔍 Checking Prerequisites..."
  
  # Check TimePeriods
  time_periods = TimePeriod.last_year
  if time_periods.empty?
    puts "❌ No TimePeriods found for last year!"
    puts "💡 Run: rails data_fixes:init_time_periods"
    return false
  else
    puts "✅ TimePeriods available: #{time_periods.count} quarters"
    time_periods.each { |tp| puts "   - #{tp.name}" }
  end
  
  # Check Elasticsearch status
  puts "\n🔍 Checking Elasticsearch..."
  begin
    require 'net/http'
    response = Net::HTTP.get_response(URI('http://localhost:9200'))
    puts "✅ Elasticsearch is running (will use PostgreSQL fallback anyway)"
  rescue => e
    puts "⚠️  Elasticsearch not available: #{e.message}"
    puts "✅ Will use PostgreSQL fallback methods"
  end
  
  true
end

# Process ingredient with PostgreSQL fallback
def process_ingredient_with_fallback(ingredient_name, force: false, dry_run: false)
  puts "\n" + "=" * 40
  puts "🧪 Processing: #{ingredient_name.upcase}"
  puts "=" * 40
  
  # Step 1: Find ingredient
  ingredient = Ingredient.where(name: ingredient_name).first
  if ingredient.nil?
    puts "❌ Ingredient '#{ingredient_name}' not found"
    
    # Try to find similar ingredients
    similar = Ingredient.where("name ILIKE ?", "%#{ingredient_name}%").limit(5)
    if similar.any?
      puts "💡 Similar ingredients found:"
      similar.each { |ing| puts "   - #{ing.name}" }
    end
    return false
  end
  
  puts "✅ Found ingredient: #{ingredient.name} (ID: #{ingredient.id})"
  
  # Step 2: Get MenuItemDashboard
  dashboard = ingredient.menu_item_dashboard
  if dashboard.nil?
    puts "❌ No MenuItemDashboard found for #{ingredient_name}"
    puts "💡 You may need to create one first or run the import script"
    return false
  end
  
  puts "✅ MenuItemDashboard found: #{dashboard.name} (ID: #{dashboard.id})"
  puts "   - State: #{dashboard.state}"
  puts "   - Enabled: #{dashboard.enabled}"
  puts "   - Query: #{dashboard.query}"
  puts "   - Is New Complete: #{dashboard.is_new_complete}"
  
  # Step 3: Check if already processed
  if dashboard.is_new_complete && !force
    puts "⚠️  Dashboard already processed (is_new_complete: true)"
    puts "💡 Use force: true to reassign anyway"
    return true
  end
  
  # Step 4: Initialize quarters if needed
  quarters_count = dashboard.quarters.count
  puts "📅 Current quarters: #{quarters_count}"
  
  if quarters_count == 0
    puts "🔧 Initializing quarters..."
    if dry_run
      puts "🔍 DRY RUN: Would initialize quarters"
    else
      dashboard.init_quarters
      puts "✅ Quarters initialized: #{dashboard.quarters.count}"
    end
  end
  
  # Step 5: Run assign_items with PostgreSQL fallback
  puts "\n🎯 Running assign_items with PostgreSQL fallback..."
  if dry_run
    puts "🔍 DRY RUN: Would run dashboard.assign_items(force: #{force})"
  else
    begin
      # Apply PostgreSQL patches
      patch_elasticsearch_methods
      
      # Run assign_items
      dashboard.assign_items(force: force)
      
      puts "✅ assign_items completed successfully!"
      
      # Verify completion
      dashboard.reload
      puts "📊 Results:"
      puts "   - Is New Complete: #{dashboard.is_new_complete}"
      puts "   - Quarters: #{dashboard.quarters.count}"
      
      # Show quarter details
      dashboard.quarters.joins(:time_period).order('time_periods.year, time_periods.quarter').each do |quarter|
        tp = quarter.time_period
        puts "   - #{tp.name}:"
        puts "     * Items: #{quarter.items_count || 0}"
        puts "     * Penetration Rate: #{quarter.pen_rate&.round(2) || 'N/A'}%"
      end
      
    rescue => e
      puts "❌ Error during assign_items: #{e.message}"
      puts "Backtrace: #{e.backtrace.first(3).join("\n")}"
      return false
    ensure
      # Always restore original methods
      restore_elasticsearch_methods
    end
  end
  
  true
end

# Main execution
def main
  puts "\n🚀 Starting assign_items with PostgreSQL fallback..."
  
  # Check prerequisites
  return unless check_prerequisites
  
  begin
    success = process_ingredient_with_fallback(
      INGREDIENT_NAME, 
      force: FORCE_REASSIGN, 
      dry_run: DRY_RUN
    )
    
    puts "\n" + "=" * 50
    puts "📊 SUMMARY"
    puts "=" * 50
    
    if success
      puts "✅ Successfully processed: #{INGREDIENT_NAME}"
    else
      puts "❌ Failed to process: #{INGREDIENT_NAME}"
    end
    
    if DRY_RUN
      puts "\n🔍 This was a DRY RUN - no actual changes were made"
    else
      puts "\n🎉 Process completed!"
      puts "💡 Check your dashboard at /menu_item_dashboards"
    end
    
    puts "\n🔧 Next Steps:"
    puts "1. Check if data appears in the UI"
    puts "2. Consider starting Elasticsearch for full functionality"
    puts "3. Run cache updates if needed"
    
  rescue => e
    puts "\n❌ Unexpected error: #{e.message}"
    puts "Backtrace: #{e.backtrace.first(5).join("\n")}"
  ensure
    # Make sure we restore methods even if there's an error
    restore_elasticsearch_methods rescue nil
  end
end

# Execute the script
main
