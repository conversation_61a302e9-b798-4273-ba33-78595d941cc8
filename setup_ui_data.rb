#!/usr/bin/env ruby

# Complete UI Data Setup Script
# Run with: rails runner setup_ui_data.rb

puts "🚀 Setting up UI data for MenuData application..."

# 1. Initialize Time Periods (Critical for dashboards)
puts "\n📅 1. Initializing Time Periods..."
begin
  times_periods = [[2022, 4], [2023, 1], [2023, 2], [2023, 3], [2023, 4], [2024, 1], [2024, 2], [2024, 3], [2024, 4]]
  times_periods.each do |tp|
    year = tp[0]
    quarter = tp[1]
    tp_record = TimePeriod.find_or_create_by(year: year, quarter: quarter, is_approved: true)
    start_at, end_at = Utility.quarter_dates(year, quarter)
    
    tp_record.update(
      name: "#{year} Q#{quarter}",
      start_at: start_at,
      end_at: end_at,
      time_period_type: 'quarter'
    )
    puts "   ✅ Created #{year} Q#{quarter}"
  end
rescue => e
  puts "   ❌ Error creating time periods: #{e.message}"
end

# 2. Create Default Regions
puts "\n🗺️ 2. Creating Default Regions..."
begin
  default_regions = [
    { name: 'California', states: ['CA'] },
    { name: 'West', states: %w[WA OR NV ID MT WY UT CO AZ NM AK HI] },
    { name: 'Plains', states: %w[ND SD NE KS MN IA MO] },
    { name: 'South Central', states: %w[TX OK AR LA] },
    { name: 'Great Lakes', states: %w[WI IL IN MI OH KY] },
    { name: 'South East', states: %w[MS AL GA SC FL] },
    { name: 'Mid South', states: %w[TN NC WV VA DE MD] },
    { name: 'North East', states: %w[ME NH VT MA RI CT NY NJ PA] }
  ]

  default_regions.each do |region_data|
    region = Region.find_or_create_by(name: region_data[:name])
    region.update(is_default: true)
    puts "   ✅ Created #{region_data[:name]} region"
  end
rescue => e
  puts "   ❌ Error creating regions: #{e.message}"
end

# 3. Create Sample MenuItemDashboards
puts "\n📊 3. Creating Sample MenuItemDashboards..."
begin
  sample_dashboards = [
    { name: 'Coffee Trends', query: 'coffee', meal_type: 'breakfast', cuisine_type: 'american' },
    { name: 'Pizza Analysis', query: 'pizza', meal_type: 'dinner', cuisine_type: 'italian' },
    { name: 'Burger Insights', query: 'burger', meal_type: 'lunch', cuisine_type: 'american' },
    { name: 'Taco Trends', query: 'taco', meal_type: 'lunch', cuisine_type: 'mexican' },
    { name: 'Sushi Analysis', query: 'sushi', meal_type: 'dinner', cuisine_type: 'japanese' }
  ]

  sample_dashboards.each do |dashboard_data|
    dashboard = MenuItemDashboard.find_or_create_by(name: dashboard_data[:name]) do |d|
      d.query = dashboard_data[:query]
      d.meal_type = dashboard_data[:meal_type]
      d.cuisine_type = dashboard_data[:cuisine_type]
      d.enabled = true
      d.state = 'approved'
      d.demo = true
    end
    
    # Initialize quarters for the dashboard
    dashboard.init_quarters
    puts "   ✅ Created #{dashboard_data[:name]} dashboard"
  end
rescue => e
  puts "   ❌ Error creating MenuItemDashboards: #{e.message}"
end

# 4. Create Sample McDashboards
puts "\n🍗 4. Creating Sample McDashboards..."
begin
  sample_mc_dashboards = [
    { name: 'BBQ Sauce', query: 'bbq sauce' },
    { name: 'Hot Sauce', query: 'hot sauce' },
    { name: 'Ranch Dressing', query: 'ranch' },
    { name: 'Sriracha', query: 'sriracha' }
  ]

  sample_mc_dashboards.each do |dashboard_data|
    dashboard = McDashboard.find_or_create_by(name: dashboard_data[:name]) do |d|
      d.query = dashboard_data[:query]
      d.enabled = true
      d.state = 'approved'
      d.demo = true
      # Add sample topics data
      d.topics = {
        "spicy" => 150,
        "flavor" => 120,
        "sauce" => 100,
        "taste" => 80,
        "hot" => 60
      }
    end
    
    dashboard.init_quarters
    puts "   ✅ Created #{dashboard_data[:name]} MC dashboard"
  end
rescue => e
  puts "   ❌ Error creating McDashboards: #{e.message}"
end

# 5. Create Sample Ingredients and RetailInsightsDashboards
puts "\n🧪 5. Creating Sample Ingredients and Retail Dashboards..."
begin
  sample_ingredients = [
    { name: 'Mozzarella Cheese', guid: '660d232f-6e7e-4a42-8868-14f97c0f68c3' },
    { name: 'Organic Tomatoes', guid: SecureRandom.uuid },
    { name: 'Olive Oil', guid: SecureRandom.uuid },
    { name: 'Basil', guid: SecureRandom.uuid },
    { name: 'Parmesan Cheese', guid: SecureRandom.uuid }
  ]

  sample_ingredients.each do |ingredient_data|
    ingredient = Ingredient.find_or_create_by(name: ingredient_data[:name]) do |i|
      i.guid = ingredient_data[:guid]
    end
    
    # Create RetailInsightsDashboard for each ingredient
    retail_dashboard = RetailInsightsDashboard.find_or_create_by(ingredient: ingredient) do |rd|
      rd.name = ingredient.name
      rd.query = ingredient.name
    end
    
    retail_dashboard.init_quarters
    puts "   ✅ Created #{ingredient_data[:name]} ingredient and retail dashboard"
  end
rescue => e
  puts "   ❌ Error creating ingredients: #{e.message}"
end

# 6. Create Sample Chains
puts "\n🏪 6. Creating Sample Restaurant Chains..."
begin
  sample_chains = [
    { name: 'Starbucks', total_locations: 15000 },
    { name: 'McDonald\'s', total_locations: 13500 },
    { name: 'Subway', total_locations: 21000 },
    { name: 'Pizza Hut', total_locations: 7000 },
    { name: 'KFC', total_locations: 5000 }
  ]

  sample_chains.each do |chain_data|
    chain = Chain.find_or_create_by(name: chain_data[:name]) do |c|
      c.total_locations = chain_data[:total_locations]
      c.state_status = 'approved'
    end
    puts "   ✅ Created #{chain_data[:name]} chain"
  end
rescue => e
  puts "   ❌ Error creating chains: #{e.message}"
end

# 7. Create Sample Categories
puts "\n🍽️ 7. Creating Sample Categories..."
begin
  sample_categories = [
    'American', 'Italian', 'Mexican', 'Chinese', 'Japanese', 
    'Indian', 'Thai', 'French', 'Mediterranean', 'Korean'
  ]

  sample_categories.each do |category_name|
    category = Category.find_or_create_by(name: category_name)
    puts "   ✅ Created #{category_name} category"
  end
rescue => e
  puts "   ❌ Error creating categories: #{e.message}"
end

# 8. Run Cache Updates
puts "\n🔄 8. Updating Dashboard Caches..."
begin
  MenuItemDashboard.where(demo: true).limit(3).each do |dashboard|
    MenuItemDashboards::SetCacheWorker.perform_async(dashboard.id, { 'force' => true })
    puts "   ✅ Queued cache update for #{dashboard.name}"
  end
rescue => e
  puts "   ❌ Error updating caches: #{e.message}"
end

puts "\n✅ UI Data Setup Complete!"
puts "\n📋 Summary:"
puts "   ✅ Time periods initialized"
puts "   ✅ Default regions created"
puts "   ✅ Sample MenuItemDashboards created"
puts "   ✅ Sample McDashboards created"
puts "   ✅ Sample ingredients and retail dashboards created"
puts "   ✅ Sample restaurant chains created"
puts "   ✅ Sample categories created"
puts "   ✅ Cache updates queued"

puts "\n🚀 You should now see data in the UI!"
puts "   - Visit /menu_item_dashboards to see dashboard data"
puts "   - Visit /retail_insights to see retail data"
puts "   - Visit /mc_dashboards to see MC dashboard data"

puts "\n⚠️  Note: Some data may take a few minutes to populate as background jobs process."
