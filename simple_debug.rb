#!/usr/bin/env ruby

# Simple debug script to check chart data
# Run in Rails console: load 'simple_debug.rb'

puts "🔍 Simple Chart Data Debug"
puts "=" * 40

# Change this to one of your processed ingredients
INGREDIENT_NAME = 'spaghett'

puts "\n1️⃣ Finding ingredient..."
ingredient = Ingredient.where(name: INGREDIENT_NAME).first

if ingredient.nil?
  puts "❌ Ingredient not found. Available ingredients:"
  Ingredient.joins(:menu_item_dashboard).limit(5).pluck(:name).each { |name| puts "   - #{name}" }
  exit
end

puts "✅ Found: #{ingredient.name}"

puts "\n2️⃣ Checking dashboard..."
dashboard = ingredient.menu_item_dashboard
puts "✅ Dashboard: #{dashboard.name} (ID: #{dashboard.id})"
puts "   Complete: #{dashboard.is_new_complete}"

puts "\n3️⃣ Checking quarters..."
quarters_count = dashboard.quarters.count
puts "   Quarters: #{quarters_count}"

if quarters_count == 0
  puts "❌ NO QUARTERS! This is why charts are empty."
  puts "💡 Need to run assign_items properly"
  exit
end

dashboard.quarters.joins(:time_period).order('time_periods.year, time_periods.quarter').each do |quarter|
  tp = quarter.time_period
  items_count = quarter.items.count
  puts "   - #{tp.name}: #{items_count} items"
end

puts "\n4️⃣ Checking items_to_show..."
items_to_show_count = dashboard.items_to_show.count
puts "   items_to_show: #{items_to_show_count}"

if items_to_show_count == 0
  puts "❌ NO ITEMS_TO_SHOW! This is the problem."
  
  # Check if last quarter has items
  last_quarter = dashboard.quarters.last
  if last_quarter
    puts "   Last quarter items: #{last_quarter.items.count}"
    puts "   Last quarter time period: #{last_quarter.time_period.name}"
  end
  exit
end

puts "\n5️⃣ Checking restaurant-chain links..."
items_with_restaurants = dashboard.items_to_show.joins(:restaurant).count
puts "   Items with restaurants: #{items_with_restaurants}"

items_with_chains = dashboard.items_to_show.joins(restaurant: :chain).count
puts "   Items with chains: #{items_with_chains}"

if items_with_chains == 0
  puts "❌ NO ITEMS WITH CHAINS! This is why top_chains is empty."
  
  # Check if restaurants have chain_id
  restaurant_ids = dashboard.items_to_show.joins(:restaurant).pluck('restaurants.id').uniq
  restaurants_with_chains = Restaurant.where(id: restaurant_ids).where.not(chain_id: nil).count
  puts "   Restaurants with chain_id: #{restaurants_with_chains}"
  
  if restaurants_with_chains == 0
    puts "❌ Restaurants don't have chain_id set!"
  end
  exit
end

puts "\n6️⃣ Testing top_chains..."
top_chains_count = dashboard.top_chains.limit(5).count
puts "   top_chains count: #{top_chains_count}"

if top_chains_count > 0
  puts "✅ TOP CHAINS DATA FOUND:"
  dashboard.top_chains.limit(3).each do |chain|
    puts "   - #{chain.name}"
  end
else
  puts "❌ top_chains query returns 0 results"
end

puts "\n7️⃣ Testing API endpoint..."
begin
  graph_service = MenuItemDashboards::GraphDataService.new(menu_item_dashboard: dashboard)
  api_data = graph_service.top_chains_chart_data
  puts "   API data count: #{api_data.count}"
  
  if api_data.count > 0
    puts "✅ API RETURNS DATA:"
    api_data.first(3).each do |chain_data|
      puts "   - #{chain_data['name']}: #{chain_data['shown_items_count']} items"
    end
  else
    puts "❌ API returns empty array"
  end
rescue => e
  puts "❌ API error: #{e.message}"
end

puts "\n🎯 SUMMARY:"
puts "Dashboard ID: #{dashboard.id}"
puts "URL: /menu_item_dashboards_2/#{dashboard.id}"
puts "API: /menu_item_dashboards_2/#{dashboard.id}/top_chains"

puts "\n💡 If charts are still empty, the issue is likely:"
puts "1. Frontend not calling the API correctly"
puts "2. API returning data but frontend not rendering it"
puts "3. JavaScript errors in the browser console"
