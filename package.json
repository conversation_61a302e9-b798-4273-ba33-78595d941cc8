{"name": "menudata", "private": true, "engines": {"node": "16.20.2", "yarn": "1.22.19"}, "dependencies": {"@babel/preset-react": "^7.18.6", "@rails/actioncable": "^8.0.100", "@rails/webpacker": "5.4.3", "axios": "^1.7.7", "babel-plugin-transform-react-remove-prop-types": "^0.4.24", "javascript-time-ago": "^2.5.11", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^9.1.2", "react_ujs": "^2.6.2", "redux": "^5.0.1", "redux-devtools-extension": "^2.13.9", "redux-saga": "^1.3.0", "title-case": "^4.3.1", "webpack": "^4.46.0", "webpack-cli": "^3.3.12"}, "devDependencies": {"webpack-dev-server": "^3"}}