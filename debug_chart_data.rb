#!/usr/bin/env ruby

# Debug script to check why charts aren't populated
# Run in Rails console: load 'debug_chart_data.rb'

puts "🔍 Debugging Chart Data for MenuItemDashboards"
puts "=" * 60

# Configuration - change this to one of your processed ingredients
INGREDIENT_NAME = 'spaghett'  # Change this to match one of your ingredients

def debug_ingredient_data(ingredient_name)
  puts "\n🧪 Debugging: #{ingredient_name.upcase}"
  puts "=" * 40
  
  # Step 1: Find ingredient
  ingredient = Ingredient.where(name: ingredient_name).first
  if ingredient.nil?
    puts "❌ Ingredient '#{ingredient_name}' not found"
    
    # Show available ingredients
    puts "\n💡 Available ingredients with dashboards:"
    Ingredient.joins(:menu_item_dashboard).limit(10).each do |ing|
      puts "   - #{ing.name}"
    end
    return false
  end
  
  puts "✅ Found ingredient: #{ingredient.name} (ID: #{ingredient.id})"
  
  # Step 2: Check MenuItemDashboard
  dashboard = ingredient.menu_item_dashboard
  if dashboard.nil?
    puts "❌ No MenuItemDashboard found"
    return false
  end
  
  puts "✅ Dashboard: #{dashboard.name} (ID: #{dashboard.id})"
  puts "   - State: #{dashboard.state}"
  puts "   - Enabled: #{dashboard.enabled}"
  puts "   - Query: #{dashboard.query}"
  puts "   - Is New Complete: #{dashboard.is_new_complete}"
  
  # Step 3: Check Quarters
  puts "\n📅 Quarters Analysis:"
  quarters = dashboard.quarters
  puts "   - Total quarters: #{quarters.count}"
  
  if quarters.empty?
    puts "❌ No quarters found! This is why charts are empty."
    puts "💡 Run assign_items to create quarters"
    return false
  end
  
  quarters.joins(:time_period).order('time_periods.year, time_periods.quarter').each do |quarter|
    tp = quarter.time_period
    items_count = quarter.items.count rescue 0
    puts "   - #{tp.name}: #{items_count} items"
  end
  
  # Step 4: Check items_to_show (this is what charts use)
  puts "\n📊 Items Analysis:"
  begin
    items_to_show = dashboard.items_to_show
    puts "   - items_to_show count: #{items_to_show.count}"
    
    if items_to_show.count == 0
      puts "❌ No items_to_show! This is why charts are empty."
      
      # Debug why items_to_show is empty
      puts "\n🔍 Debugging items_to_show:"
      puts "   - Quarters blank? #{dashboard.quarters.blank?}"
      
      if dashboard.quarters.present?
        last_quarter = dashboard.quarters.last
        puts "   - Last quarter: #{last_quarter.time_period.name}"
        puts "   - Last quarter items: #{last_quarter.items.count}"
        
        # Check if items exist but aren't being found
        puts "\n🔍 Raw quarter items check:"
        dashboard.quarters.each do |quarter|
          tp = quarter.time_period
          raw_items = quarter.items
          puts "     - #{tp.name}: #{raw_items.count} items"
          
          if raw_items.count > 0
            # Show sample items
            sample_items = raw_items.limit(3)
            sample_items.each do |item|
              puts "       * #{item.name} (Restaurant: #{item.menu.restaurant.name rescue 'N/A'})"
            end
          end
        end
      end
    else
      puts "✅ items_to_show has data: #{items_to_show.count} items"
      
      # Show sample items
      puts "\n📋 Sample items:"
      items_to_show.limit(5).each do |item|
        restaurant_name = item.menu.restaurant.name rescue 'N/A'
        chain_name = item.menu.restaurant.chain.name rescue 'N/A'
        puts "   - #{item.name} (Restaurant: #{restaurant_name}, Chain: #{chain_name})"
      end
    end
  rescue => e
    puts "❌ Error checking items_to_show: #{e.message}"
  end
  
  # Step 5: Test Top Chains specifically
  puts "\n🏪 Top Chains Analysis:"
  begin
    # This is exactly what the controller calls
    top_chains = dashboard.top_chains.limit(5)
    puts "   - top_chains count: #{top_chains.count}"
    
    if top_chains.count == 0
      puts "❌ No top_chains data! Let's debug why..."
      
      # Debug the top_chains query step by step
      puts "\n🔍 Debugging top_chains query:"
      
      # Check if items have restaurant associations
      items_with_restaurants = dashboard.items_to_show.joins(:restaurant).count
      puts "   - Items with restaurants: #{items_with_restaurants}"
      
      # Check if restaurants have chain associations
      items_with_chains = dashboard.items_to_show.joins(restaurant: :chain).count
      puts "   - Items with chains: #{items_with_chains}"
      
      # Check if chains are approved
      approved_chains = Chain.approved.count
      puts "   - Total approved chains: #{approved_chains}"
      
      if items_with_chains == 0
        puts "❌ No items linked to chains! This is the problem."
        puts "💡 Check if restaurants have chain_id set"
        
        # Check restaurant chain associations
        restaurants_with_items = Restaurant.joins(:menus => :items)
                                          .where(items: { id: dashboard.items_to_show.pluck(:id) })
                                          .distinct
        
        puts "   - Restaurants with these items: #{restaurants_with_items.count}"
        restaurants_with_chains = restaurants_with_items.where.not(chain_id: nil).count
        puts "   - Restaurants with chain_id: #{restaurants_with_chains}"
        
        if restaurants_with_chains == 0
          puts "❌ Restaurants don't have chain_id set!"
          puts "💡 This is why top_chains is empty"
        end
      end
      
    else
      puts "✅ top_chains has data!"
      top_chains.each do |chain|
        location_count = chain.try(:location_count) || chain.total_locations || 0
        shown_items_count = chain.try(:shown_items_count) || 0
        puts "   - #{chain.name}: #{shown_items_count} items, #{location_count} locations"
      end
    end
  rescue => e
    puts "❌ Error checking top_chains: #{e.message}"
    puts "Backtrace: #{e.backtrace.first(3).join("\n")}"
  end
  
  # Step 6: Test API endpoint data
  puts "\n🌐 API Endpoint Test:"
  begin
    # Simulate what the controller does
    graph_service = MenuItemDashboards::GraphDataService.new(menu_item_dashboard: dashboard)
    top_chains_data = graph_service.top_chains_chart_data
    
    puts "   - API top_chains_chart_data count: #{top_chains_data.count}"
    
    if top_chains_data.count > 0
      puts "✅ API data available:"
      top_chains_data.each do |chain_data|
        puts "   - #{chain_data['name']}: #{chain_data['shown_items_count']} items, #{chain_data['location_count']} locations"
      end
    else
      puts "❌ API returns empty data"
    end
  rescue => e
    puts "❌ Error testing API: #{e.message}"
  end
  
  # Step 7: Check other chart data
  puts "\n📈 Other Charts Analysis:"
  
  # Top Cuisines
  begin
    graph_service = MenuItemDashboards::GraphDataService.new(menu_item_dashboard: dashboard)
    cuisines_data = graph_service.top_cuisines_chart_data
    puts "   - Top Cuisines count: #{cuisines_data.count}"
  rescue => e
    puts "   - Top Cuisines error: #{e.message}"
  end
  
  # Menu mentions over time
  begin
    graph_service = MenuItemDashboards::GraphDataService.new(menu_item_dashboard: dashboard)
    mentions_data = graph_service.menu_mentions_chart_data
    puts "   - Menu Mentions count: #{mentions_data.count}"
  rescue => e
    puts "   - Menu Mentions error: #{e.message}"
  end
  
  puts "\n🎯 Dashboard URL: /menu_item_dashboards_2/#{dashboard.id}"
  puts "🎯 Top Chains API: /menu_item_dashboards_2/#{dashboard.id}/top_chains"
  
  true
end

# Test with multiple ingredients if available
def debug_multiple_ingredients
  puts "\n🔍 Quick check of multiple ingredients:"
  
  ingredients = Ingredient.joins(:menu_item_dashboard)
                         .where(menu_item_dashboards: { is_new_complete: true })
                         .limit(5)
  
  if ingredients.empty?
    puts "❌ No completed dashboards found"
    return
  end
  
  ingredients.each do |ingredient|
    dashboard = ingredient.menu_item_dashboard
    items_count = dashboard.items_to_show.count rescue 0
    chains_count = dashboard.top_chains.limit(5).count rescue 0
    
    puts "   - #{ingredient.name}: #{items_count} items, #{chains_count} chains"
  end
end

# Main execution
def main
  # Debug specific ingredient
  success = debug_ingredient_data(INGREDIENT_NAME)
  
  # Quick check of other ingredients
  debug_multiple_ingredients
  
  puts "\n" + "=" * 60
  puts "🎯 DIAGNOSIS SUMMARY"
  puts "=" * 60
  
  if success
    puts "✅ Debugging completed for #{INGREDIENT_NAME}"
    puts "\n💡 Common issues and solutions:"
    puts "1. No quarters → Run assign_items"
    puts "2. No items in quarters → Check menu data and search query"
    puts "3. No restaurant-chain links → Check restaurant.chain_id"
    puts "4. No approved chains → Check chain.state = 'approved'"
    puts "5. Empty items_to_show → Check quarter.items associations"
  else
    puts "❌ Could not complete debugging"
    puts "💡 Try changing INGREDIENT_NAME to a valid ingredient"
  end
  
  puts "\n🔧 Next steps:"
  puts "1. Fix any issues identified above"
  puts "2. Re-run assign_items if needed"
  puts "3. Check the dashboard URL in browser"
  puts "4. Test API endpoints directly"
end

# Execute
main
