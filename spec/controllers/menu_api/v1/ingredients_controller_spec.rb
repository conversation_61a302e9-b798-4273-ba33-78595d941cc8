require 'rails_helper'

RSpec.describe MenuApi::V1::IngredientsController, type: :controller do
  fixtures :time_periods

  describe 'GET #index' do
    let(:user) { FactoryBot.create(:user) }
    let(:token) { user.create_new_jwt_token }

    before do
      request.headers['Authorization'] = "Bearer #{token}"
    end

    it '#index returns a successful response' do
      get :index
      expect(response).to be_successful
    end
  end

  describe 'GET #show' do
    let(:user) { FactoryBot.create(:user) }
    let(:token) { user.create_new_jwt_token }
    let(:ingredient) { FactoryBot.create(:ingredient) }

    before do
      request.headers['Authorization'] = "Bearer #{token}"
    end

    it 'returns a successful response' do
      get :show, params: { id: ingredient.guid }
      expect(response).to be_successful
    end
  end

  describe 'ingredient_pairing_trends' do
    let(:user) { FactoryBot.create(:user) }
    let(:token) { user.create_new_jwt_token }
    let(:ingredient) { FactoryBot.create(:ingredient) }

    before do
      request.headers['Authorization'] = "Bearer #{token}"
    end

    it 'returns a successful response' do
      get :ingredient_pairing_trends, params: { ingredient_id: ingredient.guid }
      expect(response).to be_successful
    end
  end

  # uses ES, might fail
  # use manual testing for now
  # describe 'menu_types' do
  #   let(:user) { FactoryBot.create(:user) }
  #   let(:token) { user.create_new_jwt_token }
  #   let(:ingredient) { FactoryBot.create(:ingredient) }
  #
  #   before do
  #     request.headers['Authorization'] = "Bearer #{token}"
  #   end
  #
  #   it 'returns a successful response' do
  #     get :menu_types, params: { ingredient_id: ingredient.guid }
  #     expect(response).to be_successful
  #   end
  # end

  describe 'pen_rate_over_time' do
    let(:user) { create(:user) }
    let(:token) { user.create_new_jwt_token }
    let(:ingredient) { create(:ingredient) }

    before do
      request.headers['Authorization'] = "Bearer #{token}"
    end

    it 'returns a successful response' do
      get :pen_rate_over_time, params: { ingredient_id: ingredient.guid }
      expect(response).to be_successful
    end
  end

  describe 'GET #fastest_growing_ingredients' do
    let(:user) { create(:user) }
    let(:token) { user.create_new_jwt_token }

    before do
      request.headers['Authorization'] = "Bearer #{token}"
      get :fastest_growing_ingredients
    end

    it 'returns a successful response' do
      expect(response).to be_successful
    end
  end

  describe 'GET #highest_growing_ingredients' do
    let(:user) { create(:user) }
    let(:token) { user.create_new_jwt_token }

    before do
      request.headers['Authorization'] = "Bearer #{token}"
      get :highest_growing_ingredients
    end

    it 'returns a successful response' do
      expect(response).to be_successful
    end
  end

  describe 'GET #pairing_trends' do
    let(:user) { create(:user) }
    let(:token) { user.create_new_jwt_token }

    before do
      request.headers['Authorization'] = "Bearer #{token}"
      get :pairing_trends
    end

    it 'returns a successful response' do
      expect(response).to be_successful
    end
  end

  describe 'GET #ingredients_innovation' do
    let(:user) { create(:user) }
    let(:token) { user.create_new_jwt_token }

    before do
      request.headers['Authorization'] = "Bearer #{token}"
      get :ingredients_innovation
    end

    it 'returns a successful response' do
      expect(response).to be_successful
    end
  end

  describe 'GET #ingredients_by_stage' do
    let(:user) { create(:user) }
    let(:token) { user.create_new_jwt_token }

    before do
      request.headers['Authorization'] = "Bearer #{token}"
      get :ingredients_by_stage
    end

    it 'returns a successful response' do
      expect(response).to be_successful
    end
  end

  describe 'GET #consummation_habbits' do
    let(:user) { create(:user) }
    let(:ingredient) { create(:ingredient) }
    let(:token) { user.create_new_jwt_token }

    before do
      request.headers['Authorization'] = "Bearer #{token}"
      get :consummation_habbits, params: { ingredient_id: ingredient.guid }
    end

    it 'returns a successful response' do
      expect(response).to be_successful
      expect(JSON.parse(response.body)).to have_key('percentages')
    end
  end

  describe 'GET #social_sentiments' do
    let(:user) { create(:user) }
    let(:ingredient) { create(:ingredient) }
    let(:token) { user.create_new_jwt_token }

    before do
      request.headers['Authorization'] = "Bearer #{token}"
      get :social_sentiments, params: { ingredient_id: ingredient.guid }
    end

    it 'returns a successful response' do
      expect(response).to be_successful
      expect(JSON.parse(response.body)).to have_key('percentages')
    end
  end
end
