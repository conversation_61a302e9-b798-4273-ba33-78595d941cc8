require 'rails_helper'

RSpec.describe MenuApi::V1::Ingredients::CustomerInsightsController, type: :controller do
  describe 'GET #most_talked_about' do
    let(:user) { create(:user) }
    let(:token) { user.create_new_jwt_token }

    before do
      request.headers['Authorization'] = "Bearer #{token}"
      get :most_talked_about
    end

    it 'returns a successful response' do
      expect(response).to be_successful
    end
  end

  describe 'GET #most_liked_ingredients' do
    let(:user) { create(:user) }
    let(:token) { user.create_new_jwt_token }

    before do
      request.headers['Authorization'] = "Bearer #{token}"
      get :most_liked_ingredients
    end

    it 'returns a successful response' do
      expect(response).to be_successful
    end
  end

  describe 'GET #consumer_favorite_retailers' do
    let(:user) { create(:user) }
    let(:token) { user.create_new_jwt_token }

    before do
      request.headers['Authorization'] = "Bearer #{token}"
      get :consumer_favorite_retailers
    end

    it 'returns a successful response' do
      expect(response).to be_successful
    end
  end
end
