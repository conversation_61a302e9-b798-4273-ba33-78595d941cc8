# Add this to your routes.rb:
# post '/test/flashdocs', to: 'test_flashdocs#create'

class TestFlashdocsController < ApplicationController
  # Skip CSRF for testing purposes
  skip_before_action :verify_authenticity_token, only: [:create]

  def create
    # Extract parameters
    tool_name = params[:tool_name] || 'social_trends_by_query'
    chat_id = params[:chat_id] || 1
    prompt = params[:prompt] || default_test_prompt
    query = params[:query] || 'Test Query'

    begin
      # Queue the worker
      job_id = Ai::FlashdocsWorker.perform_async(tool_name, chat_id, prompt, query)
      
      render json: {
        success: true,
        message: "FlashDocs worker queued successfully",
        job_id: job_id,
        parameters: {
          tool_name: tool_name,
          chat_id: chat_id,
          query: query,
          prompt_length: prompt.length
        }
      }
    rescue => e
      render json: {
        success: false,
        error: e.message,
        backtrace: e.backtrace.first(5)
      }, status: 500
    end
  end

  private

  def default_test_prompt
    <<~TEXT
      Coffee consumption trends analysis:
      
      Total social media posts analyzed: 12,450
      Sentiment: 87% positive, 13% negative
      
      Top trends:
      1. Cold brew coffee - 35% growth
      2. Oat milk alternatives - 28% growth  
      3. Single-origin beans - 22% growth
      4. Sustainable practices - 19% growth
      
      Demographics:
      - Primary age group: 25-34 (42%)
      - Urban vs suburban: 68% urban
      - Income level: Middle to high income
      
      Popular social media posts:
      "Just tried the new cold brew at @coffeeshop - absolutely amazing! The smooth taste and perfect caffeine kick. #coldbrew #coffee"
      
      Marketing opportunities:
      - Target urban millennials
      - Emphasize sustainability
      - Promote cold brew varieties
      - Partner with oat milk brands
    TEXT
  end
end
