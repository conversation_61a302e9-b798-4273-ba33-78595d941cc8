#!/usr/bin/env ruby

# Test script for FlashDocs Worker
# Run this in Rails console: rails console
# Then: load 'test_flashdocs_worker.rb'

puts "Testing FlashDocs Worker..."

# Create a test chat (you'll need to adjust this based on your Chat model)
# chat = Chat.create!(name: "Test Chat") # Adjust based on your Chat model structure

# For testing purposes, let's assume you have a chat with ID 1
chat_id = 1

# Test data
tool_name = "social_trends_by_query"
prompt = "Coffee consumption has increased by 15% in urban areas. Popular trends include cold brew (35% growth), oat milk lattes (28% growth), and specialty single-origin beans (22% growth). Social media mentions show positive sentiment around sustainable coffee practices."
query = "Coffee Trends Analysis"

puts "Test Parameters:"
puts "- Tool Name: #{tool_name}"
puts "- Chat ID: #{chat_id}"
puts "- Query: #{query}"
puts "- Prompt length: #{prompt.length} characters"

# Option 1: Run synchronously (for testing)
puts "\n=== Testing Synchronous Execution ==="
begin
  worker = Ai::FlashdocsWorker.new
  result = worker.perform(tool_name, chat_id, prompt, query)
  puts "✅ Worker completed successfully!"
  puts "Result URL: #{result}"
rescue => e
  puts "❌ Worker failed with error: #{e.message}"
  puts "Backtrace: #{e.backtrace.first(5).join("\n")}"
end

# Option 2: Queue the job (for production-like testing)
puts "\n=== Testing Async Execution ==="
begin
  job = Ai::FlashdocsWorker.perform_async(tool_name, chat_id, prompt, query)
  puts "✅ Job queued successfully!"
  puts "Job ID: #{job}"
  puts "Check Sidekiq web interface or logs for progress"
rescue => e
  puts "❌ Failed to queue job: #{e.message}"
end

puts "\n=== Test Complete ==="
