#!/usr/bin/env ruby

# Script to run assign_items for 10 ingredients without Elasticsearch
# Run in Rails console: load 'assign_items_batch_10.rb'

puts "🚀 Batch assign_items for 10 Ingredients (No Elasticsearch Required)"
puts "=" * 70

# Configuration
BATCH_SIZE = 10
FORCE_REASSIGN = false
DRY_RUN = false  # Set to true to see what would happen without running

puts "\n📋 Configuration:"
puts "- Batch size: #{BATCH_SIZE} ingredients"
puts "- Force reassign: #{FORCE_REASSIGN}"
puts "- Dry run: #{DRY_RUN}"

# Helper method to check prerequisites
def check_prerequisites
  puts "\n🔍 Checking Prerequisites..."
  
  # Check TimePeriods
  time_periods = TimePeriod.last_year
  if time_periods.empty?
    puts "❌ No TimePeriods found for last year!"
    puts "💡 Run: rails data_fixes:init_time_periods"
    return false
  else
    puts "✅ TimePeriods available: #{time_periods.count} quarters"
    time_periods.each { |tp| puts "   - #{tp.name}" }
  end
  
  # Check ingredients with dashboards
  ingredients_with_dashboards = Ingredient.joins(:menu_item_dashboard).count
  puts "✅ Ingredients with dashboards: #{ingredients_with_dashboards}"
  
  # Check Elasticsearch status (optional)
  puts "\n🔍 Checking Elasticsearch..."
  begin
    require 'net/http'
    response = Net::HTTP.get_response(URI('http://localhost:9200'))
    puts "✅ Elasticsearch is running (will use PostgreSQL for basic data)"
  rescue => e
    puts "⚠️  Elasticsearch not available: #{e.message}"
    puts "✅ Will use PostgreSQL fallback - basic charts will work"
  end
  
  true
end

# Find ingredients that need processing
def find_ingredients_to_process(limit = 10, force = false)
  puts "\n🔍 Finding ingredients to process..."
  
  if force
    # Get any ingredients with dashboards
    ingredients = Ingredient.joins(:menu_item_dashboard)
                           .includes(:menu_item_dashboard)
                           .limit(limit)
    puts "🔄 Force mode: Processing #{ingredients.count} ingredients regardless of completion status"
  else
    # Get ingredients with incomplete dashboards
    ingredients = Ingredient.joins(:menu_item_dashboard)
                           .includes(:menu_item_dashboard)
                           .where(menu_item_dashboards: { is_new_complete: [false, nil] })
                           .limit(limit)
    
    if ingredients.count < limit
      puts "⚠️  Only #{ingredients.count} incomplete dashboards found"
      puts "💡 Looking for any ingredients with dashboards..."
      
      # Fallback to any ingredients with dashboards
      additional_needed = limit - ingredients.count
      additional_ingredients = Ingredient.joins(:menu_item_dashboard)
                                        .includes(:menu_item_dashboard)
                                        .where.not(id: ingredients.pluck(:id))
                                        .limit(additional_needed)
      
      ingredients = ingredients + additional_ingredients
    end
  end
  
  puts "✅ Found #{ingredients.count} ingredients to process:"
  ingredients.each_with_index do |ingredient, index|
    dashboard = ingredient.menu_item_dashboard
    status = dashboard.is_new_complete ? "✅ Complete" : "⏳ Incomplete"
    puts "   #{index + 1}. #{ingredient.name} (ID: #{ingredient.id}) - #{status}"
  end
  
  ingredients
end

# Process a single ingredient
def process_ingredient(ingredient, index, total, force: false, dry_run: false)
  puts "\n" + "=" * 50
  puts "🧪 Processing #{index}/#{total}: #{ingredient.name.upcase}"
  puts "=" * 50
  
  dashboard = ingredient.menu_item_dashboard
  
  puts "📊 Dashboard Info:"
  puts "   - ID: #{dashboard.id}"
  puts "   - Name: #{dashboard.name}"
  puts "   - Query: #{dashboard.query}"
  puts "   - State: #{dashboard.state}"
  puts "   - Enabled: #{dashboard.enabled}"
  puts "   - Is New Complete: #{dashboard.is_new_complete}"
  puts "   - Quarters: #{dashboard.quarters.count}"
  
  # Check if already processed
  if dashboard.is_new_complete && !force
    puts "⚠️  Dashboard already processed (is_new_complete: true)"
    puts "💡 Skipping (use force: true to reassign anyway)"
    return { success: true, skipped: true, reason: "already_complete" }
  end
  
  # Check current state
  quarters_count = dashboard.quarters.count
  if quarters_count == 0
    puts "🔧 No quarters found, will initialize during assign_items"
  else
    puts "📅 Current quarters: #{quarters_count}"
    dashboard.quarters.joins(:time_period).order('time_periods.year, time_periods.quarter').each do |quarter|
      tp = quarter.time_period
      items_count = quarter.items.count rescue 0
      puts "   - #{tp.name}: #{items_count} items"
    end
  end
  
  # Run assign_items
  puts "\n🎯 Running assign_items..."
  if dry_run
    puts "🔍 DRY RUN: Would run dashboard.assign_items(force: #{force})"
    return { success: true, skipped: true, reason: "dry_run" }
  else
    begin
      start_time = Time.now
      
      # This is the main method - it will use PostgreSQL fallbacks
      dashboard.assign_items(force: force)
      
      end_time = Time.now
      duration = (end_time - start_time).round(2)
      
      puts "✅ assign_items completed in #{duration} seconds!"
      
      # Verify results
      dashboard.reload
      puts "📊 Results:"
      puts "   - Is New Complete: #{dashboard.is_new_complete}"
      puts "   - Quarters: #{dashboard.quarters.count}"
      
      # Show quarter details
      dashboard.quarters.joins(:time_period).order('time_periods.year, time_periods.quarter').each do |quarter|
        tp = quarter.time_period
        items_count = quarter.items.count rescue 0
        puts "   - #{tp.name}: #{items_count} items"
      end
      
      # Test top chains data
      begin
        top_chains = dashboard.top_chains.limit(3)
        puts "🏪 Top Chains Preview:"
        top_chains.each do |chain|
          puts "   - #{chain.name}: #{chain.try(:shown_items_count) || 'N/A'} items, #{chain.location_count} locations"
        end
      rescue => e
        puts "⚠️  Could not fetch top chains: #{e.message}"
      end
      
      return { success: true, skipped: false, duration: duration }
      
    rescue => e
      puts "❌ Error during assign_items: #{e.message}"
      puts "Backtrace: #{e.backtrace.first(3).join("\n")}"
      return { success: false, error: e.message }
    end
  end
end

# Main execution
def main
  puts "\n🚀 Starting batch assign_items process..."
  
  # Check prerequisites
  return unless check_prerequisites
  
  # Find ingredients to process
  ingredients = find_ingredients_to_process(BATCH_SIZE, FORCE_REASSIGN)
  
  if ingredients.empty?
    puts "\n❌ No ingredients found to process!"
    puts "💡 Make sure you have ingredients with menu_item_dashboards"
    return
  end
  
  # Confirm before proceeding
  unless DRY_RUN
    puts "\n🤔 Ready to process #{ingredients.count} ingredients."
    print "Continue? (y/N): "
    response = gets.chomp.downcase
    
    unless ['y', 'yes'].include?(response)
      puts "❌ Cancelled by user"
      return
    end
  end
  
  # Process ingredients
  results = []
  start_time = Time.now
  
  ingredients.each_with_index do |ingredient, index|
    result = process_ingredient(
      ingredient, 
      index + 1, 
      ingredients.count,
      force: FORCE_REASSIGN, 
      dry_run: DRY_RUN
    )
    
    result[:ingredient_name] = ingredient.name
    result[:ingredient_id] = ingredient.id
    results << result
    
    # Small delay between ingredients to avoid overwhelming the system
    sleep(2) unless DRY_RUN
  end
  
  end_time = Time.now
  total_duration = (end_time - start_time).round(2)
  
  # Summary
  puts "\n" + "=" * 70
  puts "📊 BATCH PROCESSING SUMMARY"
  puts "=" * 70
  
  successful = results.count { |r| r[:success] && !r[:skipped] }
  skipped = results.count { |r| r[:skipped] }
  failed = results.count { |r| !r[:success] }
  
  puts "✅ Successfully processed: #{successful}"
  puts "⏭️  Skipped: #{skipped}"
  puts "❌ Failed: #{failed}"
  puts "⏱️  Total time: #{total_duration} seconds"
  
  if failed > 0
    puts "\n❌ Failed ingredients:"
    results.select { |r| !r[:success] }.each do |result|
      puts "   - #{result[:ingredient_name]}: #{result[:error]}"
    end
  end
  
  if skipped > 0
    puts "\n⏭️  Skipped ingredients:"
    results.select { |r| r[:skipped] }.each do |result|
      puts "   - #{result[:ingredient_name]}: #{result[:reason]}"
    end
  end
  
  if successful > 0
    puts "\n✅ Successfully processed ingredients:"
    results.select { |r| r[:success] && !r[:skipped] }.each do |result|
      puts "   - #{result[:ingredient_name]} (#{result[:duration]}s)"
    end
    
    puts "\n🎉 Great! You can now check these dashboards:"
    results.select { |r| r[:success] && !r[:skipped] }.each do |result|
      ingredient = Ingredient.find(result[:ingredient_id])
      dashboard_id = ingredient.menu_item_dashboard.id
      puts "   - #{result[:ingredient_name]}: /menu_item_dashboards_2/#{dashboard_id}"
    end
  end
  
  if DRY_RUN
    puts "\n🔍 This was a DRY RUN - no actual changes were made"
    puts "💡 Set DRY_RUN = false to execute for real"
  else
    puts "\n🎯 Next Steps:"
    puts "1. Check the dashboard URLs above"
    puts "2. Verify Top Chains, Top Cuisines, and Top Regions charts work"
    puts "3. If successful, run for more ingredients"
    puts "4. Consider setting up Elasticsearch for penetration rate data"
  end
end

# Execute the script
main
