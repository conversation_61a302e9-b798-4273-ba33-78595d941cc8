#!/usr/bin/env ruby

# Simple script to assign items for Apple ingredient (following your example)
# Run in Rails console: load 'assign_items_apple_example.rb'

puts "🍎 Apple MenuItemDashboard assign_items Example"
puts "=" * 50

# Step 1: Find the apple ingredient
puts "\n1️⃣ Finding apple ingredient..."
ingredient = Ingredient.where(name: 'apple').first

if ingredient.nil?
  puts "❌ Apple ingredient not found!"
  puts "💡 Available ingredients with 'apple' in name:"
  apple_ingredients = Ingredient.where("name ILIKE ?", "%apple%")
  if apple_ingredients.any?
    apple_ingredients.each { |ing| puts "   - #{ing.name} (ID: #{ing.id})" }
    puts "\n💡 Try using one of these names instead"
  else
    puts "   - No ingredients found with 'apple' in name"
  end
  exit
end

puts "✅ Found ingredient: #{ingredient.name} (ID: #{ingredient.id})"

# Step 2: Get the MenuItemDashboard
puts "\n2️⃣ Getting MenuItemDashboard..."
apple_dashboard = ingredient.menu_item_dashboard

if apple_dashboard.nil?
  puts "❌ No MenuItemDashboard found for apple!"
  puts "💡 This ingredient might not have a dashboard yet"
  puts "💡 You may need to create one first"
  exit
end

puts "✅ Found MenuItemDashboard: #{apple_dashboard.name} (ID: #{apple_dashboard.id})"
puts "   - State: #{apple_dashboard.state}"
puts "   - Enabled: #{apple_dashboard.enabled}"
puts "   - Query: #{apple_dashboard.query}"
puts "   - Is New Complete: #{apple_dashboard.is_new_complete}"

# Step 3: Check prerequisites
puts "\n3️⃣ Checking prerequisites..."

# Check TimePeriods
time_periods = TimePeriod.last_year
if time_periods.empty?
  puts "❌ No TimePeriods found for last year!"
  puts "💡 Run: rails data_fixes:init_time_periods"
  exit
end
puts "✅ TimePeriods available: #{time_periods.count} quarters"

# Check quarters
quarters_count = apple_dashboard.quarters.count
puts "📅 Current quarters: #{quarters_count}"

if quarters_count == 0
  puts "🔧 Initializing quarters..."
  apple_dashboard.init_quarters
  puts "✅ Quarters initialized: #{apple_dashboard.quarters.count}"
end

# Step 4: Show current state
puts "\n4️⃣ Current dashboard state:"
puts "   - Total items: #{apple_dashboard.total_items.count}"
puts "   - Quarters: #{apple_dashboard.quarters.count}"
apple_dashboard.quarters.each do |quarter|
  tp = quarter.time_period
  puts "     * #{tp.name}: #{quarter.items.count} items"
end

# Step 5: Ask for confirmation
puts "\n5️⃣ Ready to run assign_items..."
puts "⚠️  This will:"
puts "   - Process menu items for apple"
puts "   - Assign items to quarters"
puts "   - Calculate penetration rates"
puts "   - Set dashboard as complete"

print "\n🤔 Do you want to proceed? (y/N): "
response = gets.chomp.downcase

unless ['y', 'yes'].include?(response)
  puts "❌ Cancelled by user"
  exit
end

# Step 6: Run assign_items
puts "\n6️⃣ Running assign_items..."
puts "⏳ This may take a few minutes..."

begin
  start_time = Time.now
  
  # This is the main method that does all the work
  apple_dashboard.assign_items
  
  end_time = Time.now
  duration = (end_time - start_time).round(2)
  
  puts "✅ assign_items completed in #{duration} seconds!"
  
rescue => e
  puts "❌ Error during assign_items: #{e.message}"
  puts "Backtrace:"
  puts e.backtrace.first(5).join("\n")
  exit
end

# Step 7: Show results
puts "\n7️⃣ Results:"
apple_dashboard.reload

puts "📊 Dashboard Status:"
puts "   - Is New Complete: #{apple_dashboard.is_new_complete}"
puts "   - State: #{apple_dashboard.state}"
puts "   - Total Items: #{apple_dashboard.total_items.count}"

puts "\n📅 Quarters Data:"
apple_dashboard.quarters.joins(:time_period).order('time_periods.year, time_periods.quarter').each do |quarter|
  tp = quarter.time_period
  puts "   - #{tp.name}:"
  puts "     * Items: #{quarter.items.count}"
  puts "     * Restaurants: #{quarter.restaurants_with_items_count || 0}"
  puts "     * Penetration Rate: #{quarter.pen_rate&.round(2) || 'N/A'}%"
end

# Step 8: Optional cache update
puts "\n8️⃣ Cache Update (Optional):"
puts "💡 To update dashboard cache, run:"
puts "   MenuItemDashboards::SetCacheWorker.perform_async(#{apple_dashboard.id}, {'force' => true})"

print "\n🤔 Do you want to update cache now? (y/N): "
cache_response = gets.chomp.downcase

if ['y', 'yes'].include?(cache_response)
  puts "🔄 Updating cache..."
  MenuItemDashboards::SetCacheWorker.perform_async(apple_dashboard.id, {'force' => true})
  puts "✅ Cache update job queued!"
  puts "💡 Check Sidekiq for job progress"
end

puts "\n🎉 Apple dashboard setup complete!"
puts "🔗 View at: /menu_item_dashboards/#{apple_dashboard.id}"
puts "\n📋 Summary:"
puts "   - Dashboard ID: #{apple_dashboard.id}"
puts "   - Total Items: #{apple_dashboard.total_items.count}"
puts "   - Quarters: #{apple_dashboard.quarters.count}"
puts "   - Status: #{apple_dashboard.is_new_complete ? 'Complete' : 'Incomplete'}"
