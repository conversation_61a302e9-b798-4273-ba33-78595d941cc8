#!/usr/bin/env ruby

# Script to assign items to MenuItemDashboards for ingredients
# Run in Rails console: load 'assign_items_script.rb'

puts "🚀 MenuItemDashboard assign_items Script"
puts "=" * 50

# Configuration
INGREDIENT_NAMES = ['apple', 'coffee', 'cheese', 'chicken', 'tomato']  # Add more as needed
FORCE_REASSIGN = false  # Set to true to force reassignment even if already complete
DRY_RUN = false  # Set to true to see what would happen without actually running

puts "\n📋 Configuration:"
puts "- Ingredients to process: #{INGREDIENT_NAMES.join(', ')}"
puts "- Force reassign: #{FORCE_REASSIGN}"
puts "- Dry run: #{DRY_RUN}"

# Helper method to check prerequisites
def check_prerequisites
  puts "\n🔍 Checking Prerequisites..."
  
  # Check TimePeriods
  time_periods = TimePeriod.last_year
  if time_periods.empty?
    puts "❌ No TimePeriods found for last year!"
    puts "💡 Run: rails data_fixes:init_time_periods"
    return false
  else
    puts "✅ TimePeriods available: #{time_periods.count} quarters"
    time_periods.each { |tp| puts "   - #{tp.name}" }
  end
  
  # Check if <PERSON>ki<PERSON> is running (optional but recommended)
  begin
    require 'sidekiq/api'
    queue_size = Sidekiq::Queue.new.size
    puts "✅ Sidekiq queue size: #{queue_size}"
  rescue => e
    puts "⚠️  Sidekiq status unknown: #{e.message}"
    puts "💡 Consider running: bundle exec sidekiq"
  end
  
  true
end

# Helper method to process a single ingredient
def process_ingredient(ingredient_name, force: false, dry_run: false)
  puts "\n" + "=" * 30
  puts "🧪 Processing: #{ingredient_name.upcase}"
  puts "=" * 30
  
  # Step 1: Find ingredient
  ingredient = Ingredient.where(name: ingredient_name).first
  if ingredient.nil?
    puts "❌ Ingredient '#{ingredient_name}' not found"
    return false
  end
  
  puts "✅ Found ingredient: #{ingredient.name} (ID: #{ingredient.id})"
  
  # Step 2: Get or create MenuItemDashboard
  dashboard = ingredient.menu_item_dashboard
  if dashboard.nil?
    puts "❌ No MenuItemDashboard found for #{ingredient_name}"
    puts "💡 Creating MenuItemDashboard..."
    
    if dry_run
      puts "🔍 DRY RUN: Would create MenuItemDashboard for #{ingredient_name}"
      return true
    end
    
    # Create dashboard if it doesn't exist
    ingredient.init_menu_item_dashboard
    dashboard = ingredient.reload.menu_item_dashboard
    
    if dashboard.nil?
      puts "❌ Failed to create MenuItemDashboard"
      return false
    end
  end
  
  puts "✅ MenuItemDashboard found: #{dashboard.name} (ID: #{dashboard.id})"
  puts "   - State: #{dashboard.state}"
  puts "   - Enabled: #{dashboard.enabled}"
  puts "   - Query: #{dashboard.query}"
  puts "   - Is New Complete: #{dashboard.is_new_complete}"
  
  # Step 3: Check if already processed
  if dashboard.is_new_complete && !force
    puts "⚠️  Dashboard already processed (is_new_complete: true)"
    puts "💡 Use force: true to reassign anyway"
    return true
  end
  
  # Step 4: Initialize quarters if needed
  quarters_count = dashboard.quarters.count
  puts "📅 Current quarters: #{quarters_count}"
  
  if quarters_count == 0
    puts "🔧 Initializing quarters..."
    if dry_run
      puts "🔍 DRY RUN: Would initialize quarters"
    else
      dashboard.init_quarters
      puts "✅ Quarters initialized: #{dashboard.quarters.count}"
    end
  end
  
  # Step 5: Check retail insights dashboard
  retail_dashboard = ingredient.retail_insights_dashboard
  if retail_dashboard.nil?
    puts "🏪 No RetailInsightsDashboard found, creating..."
    if dry_run
      puts "🔍 DRY RUN: Would create RetailInsightsDashboard"
    else
      ingredient.init_retail_insight_dashboard
      puts "✅ RetailInsightsDashboard created"
    end
  else
    puts "✅ RetailInsightsDashboard exists: #{retail_dashboard.name}"
  end
  
  # Step 6: Run assign_items
  puts "\n🎯 Running assign_items..."
  if dry_run
    puts "🔍 DRY RUN: Would run dashboard.assign_items(force: #{force})"
    puts "🔍 This would:"
    puts "   - Initialize quarters for last year"
    puts "   - Assign quarters with restaurant and item data"
    puts "   - Set is_new_complete to true"
    puts "   - Compute segment penetration rates"
  else
    begin
      dashboard.assign_items(force: force)
      puts "✅ assign_items completed successfully!"
      
      # Verify completion
      dashboard.reload
      puts "📊 Results:"
      puts "   - Is New Complete: #{dashboard.is_new_complete}"
      puts "   - Quarters: #{dashboard.quarters.count}"
      puts "   - Total Items: #{dashboard.total_items.count}"
      
    rescue => e
      puts "❌ Error during assign_items: #{e.message}"
      puts "Backtrace: #{e.backtrace.first(3).join("\n")}"
      return false
    end
  end
  
  # Step 7: Optional - Set cache (commented out as it's async)
  puts "\n💾 Cache Update:"
  puts "💡 Consider running cache update: MenuItemDashboards::SetCacheWorker.perform_async(#{dashboard.id}, {'force' => true})"
  
  true
end

# Main execution
def main
  puts "\n🚀 Starting assign_items process..."
  
  # Check prerequisites
  return unless check_prerequisites
  
  success_count = 0
  total_count = INGREDIENT_NAMES.length
  
  INGREDIENT_NAMES.each do |ingredient_name|
    success = process_ingredient(
      ingredient_name, 
      force: FORCE_REASSIGN, 
      dry_run: DRY_RUN
    )
    
    success_count += 1 if success
    
    # Small delay between ingredients to avoid overwhelming the system
    sleep(1) unless DRY_RUN
  end
  
  puts "\n" + "=" * 50
  puts "📊 SUMMARY"
  puts "=" * 50
  puts "✅ Successfully processed: #{success_count}/#{total_count} ingredients"
  puts "❌ Failed: #{total_count - success_count}"
  
  if DRY_RUN
    puts "\n🔍 This was a DRY RUN - no actual changes were made"
    puts "💡 Set DRY_RUN = false to execute for real"
  else
    puts "\n🎉 Process completed!"
    puts "💡 Check your dashboards at /menu_item_dashboards"
  end
  
  puts "\n🔧 Next Steps:"
  puts "1. Check Sidekiq for any background jobs"
  puts "2. Run cache updates if needed"
  puts "3. Verify data in the UI"
end

# Execute the script
main
