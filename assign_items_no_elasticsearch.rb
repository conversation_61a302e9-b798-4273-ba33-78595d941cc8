#!/usr/bin/env ruby

# Script to assign items WITHOUT Elasticsearch dependency
# Run in Rails console: load 'assign_items_no_elasticsearch.rb'

puts "🚀 MenuItemDashboard assign_items Script (No Elasticsearch)"
puts "=" * 60

# Configuration
INGREDIENT_NAMES = ['apple']  # Start with just apple
FORCE_REASSIGN = false
DRY_RUN = false

puts "\n📋 Configuration:"
puts "- Ingredients to process: #{INGREDIENT_NAMES.join(', ')}"
puts "- Force reassign: #{FORCE_REASSIGN}"
puts "- Dry run: #{DRY_RUN}"

# Helper method to check prerequisites
def check_prerequisites
  puts "\n🔍 Checking Prerequisites..."
  
  # Check TimePeriods
  time_periods = TimePeriod.last_year
  if time_periods.empty?
    puts "❌ No TimePeriods found for last year!"
    puts "💡 Run: rails data_fixes:init_time_periods"
    return false
  else
    puts "✅ TimePeriods available: #{time_periods.count} quarters"
    time_periods.each { |tp| puts "   - #{tp.name}" }
  end
  
  # Check Elasticsearch status
  puts "\n🔍 Checking Elasticsearch..."
  begin
    require 'net/http'
    response = Net::HTTP.get_response(URI('http://localhost:9200'))
    puts "✅ Elasticsearch is running"
  rescue => e
    puts "⚠️  Elasticsearch not available: #{e.message}"
    puts "💡 Will use PostgreSQL search instead"
  end
  
  true
end

# Modified assign_items that avoids Elasticsearch issues
def safe_assign_items(dashboard, force: false)
  puts "🔧 Running safe assign_items process..."
  
  # Step 1: Initialize quarters
  puts "📅 Initializing quarters..."
  dashboard.init_quarters
  
  # Step 2: Manual quarter assignment (avoiding worker that might use ES)
  puts "🔄 Processing quarters manually..."
  
  dashboard.recent_year.each do |quarter|
    puts "   Processing #{quarter.time_period.name}..."
    
    begin
      # This calls the quarter's method to set restaurant data
      quarter.set_restaurants_with_items_count
      puts "     ✅ Restaurants and items count set"
    rescue => e
      puts "     ⚠️  Warning: #{e.message}"
    end
  end
  
  # Step 3: Set completion flag
  puts "✅ Setting completion flag..."
  dashboard.is_new_complete = true
  dashboard.save
  
  # Step 4: Compute penetration rates (if method exists)
  puts "📊 Computing penetration rates..."
  begin
    dashboard.compute_segment_penetration_rates if dashboard.respond_to?(:compute_segment_penetration_rates)
    puts "✅ Penetration rates computed"
  rescue => e
    puts "⚠️  Warning computing penetration rates: #{e.message}"
  end
  
  puts "✅ Safe assign_items completed!"
end

# Helper method to process a single ingredient
def process_ingredient(ingredient_name, force: false, dry_run: false)
  puts "\n" + "=" * 30
  puts "🧪 Processing: #{ingredient_name.upcase}"
  puts "=" * 30
  
  # Step 1: Find ingredient
  ingredient = Ingredient.where(name: ingredient_name).first
  if ingredient.nil?
    puts "❌ Ingredient '#{ingredient_name}' not found"
    
    # Try to find similar ingredients
    similar = Ingredient.where("name ILIKE ?", "%#{ingredient_name}%").limit(5)
    if similar.any?
      puts "💡 Similar ingredients found:"
      similar.each { |ing| puts "   - #{ing.name}" }
    end
    return false
  end
  
  puts "✅ Found ingredient: #{ingredient.name} (ID: #{ingredient.id})"
  
  # Step 2: Get or check MenuItemDashboard
  dashboard = ingredient.menu_item_dashboard
  if dashboard.nil?
    puts "❌ No MenuItemDashboard found for #{ingredient_name}"
    puts "💡 You may need to create one first or run the import script"
    return false
  end
  
  puts "✅ MenuItemDashboard found: #{dashboard.name} (ID: #{dashboard.id})"
  puts "   - State: #{dashboard.state}"
  puts "   - Enabled: #{dashboard.enabled}"
  puts "   - Query: #{dashboard.query}"
  puts "   - Is New Complete: #{dashboard.is_new_complete}"
  
  # Step 3: Check if already processed
  if dashboard.is_new_complete && !force
    puts "⚠️  Dashboard already processed (is_new_complete: true)"
    puts "💡 Use force: true to reassign anyway"
    return true
  end
  
  # Step 4: Check current state
  quarters_count = dashboard.quarters.count
  puts "📅 Current quarters: #{quarters_count}"
  
  # Step 5: Check retail insights dashboard
  retail_dashboard = ingredient.retail_insights_dashboard
  if retail_dashboard
    puts "✅ RetailInsightsDashboard exists: #{retail_dashboard.name}"
  else
    puts "⚠️  No RetailInsightsDashboard found"
  end
  
  # Step 6: Run safe assign_items
  puts "\n🎯 Running safe assign_items (avoiding Elasticsearch)..."
  if dry_run
    puts "🔍 DRY RUN: Would run safe_assign_items"
  else
    begin
      safe_assign_items(dashboard, force: force)
      
      # Verify completion
      dashboard.reload
      puts "📊 Results:"
      puts "   - Is New Complete: #{dashboard.is_new_complete}"
      puts "   - Quarters: #{dashboard.quarters.count}"
      
      # Show quarter details
      dashboard.quarters.joins(:time_period).order('time_periods.year, time_periods.quarter').each do |quarter|
        tp = quarter.time_period
        puts "   - #{tp.name}: #{quarter.restaurants_with_items_count || 0} restaurants"
      end
      
    rescue => e
      puts "❌ Error during safe assign_items: #{e.message}"
      puts "Backtrace: #{e.backtrace.first(3).join("\n")}"
      return false
    end
  end
  
  true
end

# Main execution
def main
  puts "\n🚀 Starting safe assign_items process..."
  
  # Check prerequisites
  return unless check_prerequisites
  
  success_count = 0
  total_count = INGREDIENT_NAMES.length
  
  INGREDIENT_NAMES.each do |ingredient_name|
    success = process_ingredient(
      ingredient_name, 
      force: FORCE_REASSIGN, 
      dry_run: DRY_RUN
    )
    
    success_count += 1 if success
    
    # Small delay between ingredients
    sleep(1) unless DRY_RUN
  end
  
  puts "\n" + "=" * 50
  puts "📊 SUMMARY"
  puts "=" * 50
  puts "✅ Successfully processed: #{success_count}/#{total_count} ingredients"
  puts "❌ Failed: #{total_count - success_count}"
  
  if DRY_RUN
    puts "\n🔍 This was a DRY RUN - no actual changes were made"
  else
    puts "\n🎉 Process completed!"
    puts "💡 Check your dashboards at /menu_item_dashboards"
  end
  
  puts "\n🔧 Next Steps:"
  puts "1. Check if data appears in the UI"
  puts "2. Consider starting Elasticsearch for full functionality"
  puts "3. Run cache updates if needed"
end

# Execute the script
main
