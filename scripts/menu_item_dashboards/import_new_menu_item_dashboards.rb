Ingredient.all.each do |i|
  if i.menu_item_dashboard.present?
    m = i.menu_item_dashboard
    m.ingredient_id = i.id
    m.save
  end
end

raise StandardError.new("Set Ingredient Dashbaords First") if MenuItemDashboard.where.not(:ingredient_id => nil).empty?

# Clean up MenuItemDashboards that don't have ingredients
# First delete dependent records to avoid foreign key constraint violations
puts "Cleaning up #{MenuItemDashboard.where(:ingredient_id => nil).count} dashboards without ingredients..."

MenuItemDashboard.where(:ingredient_id => nil).find_each do |dashboard|
  puts "Cleaning up dashboard: #{dashboard.name} (ID: #{dashboard.id})"

  begin
    # Delete dependent records first to avoid foreign key violations
    dashboard.item_dashboards.delete_all if dashboard.respond_to?(:item_dashboards)
    dashboard.ingredient_dashboards.delete_all if dashboard.respond_to?(:ingredient_dashboards)
    dashboard.menu_mentions_region_data.delete_all if dashboard.respond_to?(:menu_mentions_region_data)

    # Now safely destroy the dashboard
    dashboard.destroy
    puts "  ✅ Successfully deleted dashboard #{dashboard.id}"
  rescue => e
    puts "  ❌ Error deleting dashboard #{dashboard.id}: #{e.message}"
    # Continue with next dashboard instead of stopping the entire script
  end
end

puts "Cleanup completed!"

# google_sheet_url = "https://docs.google.com/spreadsheets/d/1RZIR5qMoxSptd4NiQMbGCmAecnt1zE1lterrQXcpkVA/edit?gid=134704392#gid=134704392"
# csv_url = "https://docs.google.com/spreadsheets/d/e/2PACX-1vRy9Zr5VW_Vf2EdrH0OBh6lMXTsGdlIoX_wRltjIXmA-SOTMvmsX4xf1zumvTGL2VXgJKEMVkTYNa6W/pub?gid=134704392&single=true&output=csv"
csv_url = "https://docs.google.com/spreadsheets/d/e/2PACX-1vRy9Zr5VW_Vf2EdrH0OBh6lMXTsGdlIoX_wRltjIXmA-SOTMvmsX4xf1zumvTGL2VXgJKEMVkTYNa6W/pub?gid=134704392&single=true&output=csv"
rows = CSV.parse(URI.open(csv_url).read, :headers => true, :header_converters => lambda{|h|h.to_s.downcase.strip})
rows.each do |row|
  categories = row["category"].to_s.split(",").map{|c|c.to_s.downcase.strip}
  name = row["menu_item"].to_s.downcase.strip
  remove = row["remove"].to_s.downcase.strip
  next if remove == "x"
  m = MenuItemDashboard.find_or_create_by!(:name => name, :query => name, :ingredient_id => nil)
  categories.each do |c|
    if MenuItemDashboard::MEAL_TYPES.include?(c)
      m.meal_type = c
      m.save
    end
    if MenuItemDashboard::CUISINE_TYPES.include?(c)
      m.cuisine_type = c
      m.save
    end
  end
  m.state = "approved"
  m.enabled = true
  m.save
end

MenuItemDashboard.where(:created_at => 1.day.ago..Time.now).find_each do |m|
  MenuItemDashboards::AssignItemsWorker.perform_async(m.id, 'by_script')
end
