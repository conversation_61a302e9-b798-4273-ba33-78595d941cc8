require_relative '../../config/environment'

require 'csv'
require 'open-uri'

prolific_csv_url = 'https://docs.google.com/spreadsheets/d/1-M--NMK4QbHV3BbGHnETpQQcWgCP2sli0BO1jdwCQfY/export?format=csv'
responses_csv_url = 'https://docs.google.com/spreadsheets/d/1AZ2V1nv8kMim3zv50l89u-PsaRDuGuIwo_bAa1UEIkY/export?format=csv'


responses_csv = CSV.parse(URI.open(responses_csv_url).read, headers: true)
prolific_csv  = CSV.parse(URI.open(prolific_csv_url).read, headers: true)

ingredient_column_map = {}

responses_csv.headers.each do |header|
  if match = header.match(/^For the ingredient '(.+?)', please indicate your level of familiarity/)
    ingredient_name = match[1]
    ingredient_column_map[ingredient_name] ||= {}
    ingredient_column_map[ingredient_name][:familiarity] = header
  elsif match = header.match(/^For the ingredient '(.+?)', please rate how much you like it/)
    ingredient_name = match[1]
    ingredient_column_map[ingredient_name] ||= {}
    ingredient_column_map[ingredient_name][:like] = header
  end
end

participant_sex_map = prolific_csv.each_with_object({}) do |row, hash|
  pid = row['Participant id']
  data = {
    sex: row['Sex'],
    age: row['Age'],
    ethnicity: row['Ethnicity simplified']
  }

  hash[pid] = data
end

grouped_by_ingredient = Hash.new { |h, k| h[k] = [] }

responses_csv.each do |row|
  pid = row['prolific_pid']
  next unless pid

  ingredient_column_map.each do |ingredient_name, cols|
    familiarity = row[cols[:familiarity]]
    like = row[cols[:like]]

    next if familiarity.nil? || like.nil?

    grouped_by_ingredient[ingredient_name] << {
      prolific_pid: pid,
      additional_data: participant_sex_map[pid],
      familiarity: familiarity.to_i,
      like: like.to_i
    }
  end
end

grouped_by_ingredient.each do |ingredient_name, responses|
  ingredient = Ingredient.find_or_create_by!(name: ingredient_name)

  responses.each do |response|
    ConsumerSentimentLike.find_or_create_by!(
      ingredient: ingredient,
      score: response[:like],
      familirity_score: response[:familiarity],
      sex: response[:additional_data][:sex].downcase,
      age: response[:additional_data][:age],
      ethnicity: response[:additional_data][:ethnicity]
    )
  end
end

puts "Grouped by ingredients:"
grouped_by_ingredient.each do |ingredient, responses|
  puts "#{ingredient}: #{responses.size} responses"
end
