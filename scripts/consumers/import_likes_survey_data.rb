require_relative '../../config/environment'

require 'csv'
require 'open-uri'

prolific_csv_url = 'https://docs.google.com/spreadsheets/d/1Gui-_qV7f6HQpJZSBMRpUu8thb2Nw3IMJPb0cU0EFNA/export?format=csv'
responses_csv_url = 'https://docs.google.com/spreadsheets/d/1fh2Q3DlIM9ZSIAEqn75I8AL2qWlT3euxPor4Jqu1dnk/export?format=csv'

responses_csv = CSV.parse(URI.open(responses_csv_url).read, headers: true)
prolific_csv  = CSV.parse(URI.open(prolific_csv_url).read, headers: true)

ingredient_columns = responses_csv.headers.select do |header|
  header =~ /^For the ingredient '(.+?)', please indicate your level of familiarity/
end

ingredient_names = ingredient_columns.map do |header|
  header.match(/^For the ingredient '(.+?)', please indicate your level of familiarity/)[1]
end

participant_sex_map = prolific_csv.each_with_object({}) do |row, hash|
  pid = row['Participant id']
  sex = row['Sex']
  hash[pid] = sex
end

grouped_by_ingredient = Hash.new { |h, k| h[k] = [] }

responses_csv.each do |row|
  pid = row['prolific_pid']
  next unless pid
  sex = participant_sex_map[pid]

  ingredient_columns.each_with_index do |col, i|
    ingredient_name = ingredient_names[i]
    familiarity = row[col]

    grouped_by_ingredient[ingredient_name] << {
      prolific_pid: pid,
      sex: sex,
      familiarity: familiarity
    }
  end
end

grouped_by_ingredient.keys.each do |ingredient_name|
  ingredient = Ingredient.find_or_create_by(name: ingredient_name)
  grouped_by_ingredient[ingredient_name].each do |response|
    ConsumerSentimentLike.find_or_create_by!(
      ingredient: ingredient,
      score: response[:familiarity].to_i,
      sex: response[:sex].downcase
    )
  end
end

puts "Grouped by ingredients:"
grouped_by_ingredient.each do |ingredient, responses|
  puts "#{ingredient}: #{responses.size} responses"
end
