require_relative '../../config/environment'

require 'csv'
require 'open-uri'

csv_url = 'https://drive.google.com/uc?export=download&id=16-RWRBOTbEP1lQT040fBoowF1sTj9grU'

AGE_FIELD_MAP = {
  "65+"    => "age_65",
  "55-64"  => "age_55_64",
  "45-54"  => "age_45_54",
  "35-44"  => "age_35_44",
  "25-34"  => "age_25_34",
  "18-24"  => "age_18_24"
}

ETHNICITY_FIELD_MAP = {
  "Asian" => "asian",
  "Black" => "black",
  "White" => "white",
  "Mixed" => "mixed",
  "Other" => "other"
}

csv_text = URI.open(csv_url).read
lines = csv_text.lines.map(&:strip).reject(&:empty?)

section_headers = [
  'AGE BREAKDOWN',
  'ETHNICITY BREAKDOWN'
]

tables = []
current_table = nil
section_name = nil

lines.each_with_index do |line, i|
  if section_headers.include?(line)
    tables << { name: section_name, lines: current_table } if current_table

    section_name = line
    current_table = []
  elsif current_table
    current_table << line
  end
end

tables << { name: section_name, lines: current_table } if current_table

ingredient_age_table = tables.find { |t| t[:name] == 'AGE BREAKDOWN' }
ethnicity_table = tables.find { |t| t[:name] == 'ETHNICITY BREAKDOWN' }

csv = CSV.parse(ingredient_age_table[:lines].join("\n"), headers: true)
ingredients = {}

csv.each do |row|
  group = row['Group']

  row.headers.each do |header|
    if header =~ /(.+?) (Like Score|Familiarity)/
      ingredient = $1.strip
      type = $2

      ingredients[ingredient] ||= []

      group_data = ingredients[ingredient].find { |g| g["Group"] == group }
      if group_data.nil?
        group_data = { "Group" => group }
        ingredients[ingredient] << group_data
      end

      group_data[type] = row[header]
    end
  end
end
ingredients.each do |ingredient, group_data|
  @ingredient = Ingredient.find_by(name: ingredient)
  next unless @ingredient

  age_breakdown = IngredientAgeBreakdown.find_or_initialize_by(ingredient: @ingredient)

  group_data.each do |data|
    age_key = AGE_FIELD_MAP[data['Group']]
    next unless age_key

    like_score = data['Like Score'].to_f
    familiarity = data['Familiarity'].to_f

    age_breakdown["#{age_key}_likes_score"] = like_score
    age_breakdown["#{age_key}_familiarity"] = familiarity
  end

  age_breakdown.save!
  puts "Saved age breakdown for #{ingredient}"
end

####################################################

csv = CSV.parse(ethnicity_table[:lines].join("\n"), headers: true)
ingredients = {}

csv.each do |row|
  group = row['Group']
  group_key = ETHNICITY_FIELD_MAP[group]
  next unless group_key

  row.headers.each do |header|
    if header =~ /(.+?) (Like Score|Familiarity)/
      ingredient = $1.strip
      type = $2

      ingredients[ingredient] ||= {}

      ingredients[ingredient][group_key] ||= {}
      ingredients[ingredient][group_key][type] = row[header]
    end
  end
end

ingredients.each do |ingredient_name, groups|
  ingredient = Ingredient.find_by(name: ingredient_name)
  next unless ingredient

  ethnicity_breakdown = IngredientEthnicityBreakdown.find_or_initialize_by(ingredient: ingredient)

  groups.each do |group_key, scores|
    like_score = scores["Like Score"].to_f
    familiarity = scores["Familiarity"].to_f

    ethnicity_breakdown["#{group_key}_likes_score"] = like_score
    ethnicity_breakdown["#{group_key}_familiarity"] = familiarity
  end

  ethnicity_breakdown.save!
  puts "Saved ethnicity breakdown for #{ingredient_name}"
end

puts "Data imported successfully!"
