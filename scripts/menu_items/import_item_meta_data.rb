file_path = "/home/<USER>/Desktop"
file_name = "gmi_sample.csv"

path = "#{file_path}/#{file_name}"

def parse_array(string)
  begin
    return if string.blank?
    JSON.parse(string.gsub(/\'/, "\""))
  rescue StandardError => error
    pp error.message
    ""
  end
end

rows = CSV.parse(File.read(path), headers: true, header_converters: lambda { |h| h.downcase.to_s })
rows.each do |row|
  item_id = row['item_id']
  gmi = row['gmi']
  score = row['score']
  cuisine_type = parse_array(row['item_cuisine_type'])
  day_part = parse_array(row['day_part'])
  menu_type = parse_array(row['menu_type'])
  dietary_tags = parse_array(row['dietary_tags'])
  taste_profile = parse_array(row['taste_profile'])
  texture = parse_array(row['texture'])
  prep_methods = parse_array(row['preparation_methods'])

  category = row['category']
  sub_category = row['subcategory']

  data = {
    item_id:,
    gmi:,
    score:,
    cuisine_type:,
    day_part:,
    menu_type:,
    dietary_tags:,
    taste_profile:,
    texture:,
    prep_methods:,
    category:,
    sub_category:
  }
  ItemMetaDatum.create(data)
end
