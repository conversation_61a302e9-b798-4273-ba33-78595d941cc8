access_key_id = ENV['AWS_S3_ACCESS_KEY']
secret_access_key = ENV['AWS_S3_SECRET']
region = 'us-east-1'


# Check Credentials
sts = Aws::STS::Client.new(region: 'us-east-1', access_key_id: access_key_id, secret_access_key: secret_access_key )
begin
  resp = sts.get_caller_identity
  puts "Caller identity ARN: #{resp.arn}"
rescue Aws::STS::Errors::ServiceError => e
  puts "Error: #{e.message}"
end

# Assume Role
begin
  resp = sts.assume_role(
    role_arn: 'arn:aws:iam::105167731024:role/access-carc-mule',
    role_session_name: 'test-session'
  )
  puts "Assumed role ARN: #{resp.assumed_role_user.arn}"
  puts "Access Key ID: #{resp.credentials.access_key_id}"
  puts "Session Token: #{resp.credentials.session_token}"
rescue Aws::STS::Errors::ServiceError => e
  puts "Error assuming role: #{e.message}"
end

# Login as Assumed Role
s3 = Aws::S3::Client.new(
  region: 'us-east-1',
  credentials: resp
)

# See Ability to List Objects
begin
  resp = s3.list_objects_v2(
    bucket: 'carc-mule',
    prefix: 'to-carc/'
  )
  puts "Objects in s3://carc-mule/to-carc/:"
  resp.contents.each { |obj| puts obj.key }
rescue Aws::S3::Errors::ServiceError => e
  puts "Error listing objects: #{e.message}"
end

# Upload File
s3 = Aws::S3::Client.new(
  region: 'us-east-1',
  credentials: resp.credentials
)

prefix = 'to-carc'
file_name = 'carbon_arc_wave_2_6_12.zip'
file_path = '/home/<USER>/Desktop/carbon_arc_wave_2_6_12.zip'
key = "#{prefix}/#{file_name}"

File.open(file_path, 'rb') do |file|
  s3.put_object(bucket: 'carc-mule',
                key: key,
                body: file)
end


# Get Presigned URL
s3 = Aws::S3::Resource.new(region: 'us-east-1', credentials: resp)
obj = s3.bucket('carc-mule').object(key)
url = obj.presigned_url(:get, expires_in: 3600)
