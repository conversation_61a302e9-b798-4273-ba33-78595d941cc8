# Script for Open Blue
# Lead Scoring Report Demo

customer = Customer.find_or_create_by(:name => "Open Blue")
seafood_items = %(
cobia
grouper
seabass
halibut
wreckfish
mahi
swordfish
barramundi
snapper
branzino
crudo
ceviche
wild-caught salmon
line-caught tuna
locally sourced seafood
sustainably farmed fish
catch of the day
).split("\n").compact.reject(&:empty?)

list = LeadScoringItemList.find_or_create_by(:customer_id => customer.id)
list.watch_list = seafood_items
list.save

LeadScoringReport::Basic.init_lead_items(customer.id)

cities = City.where('name ILIKE ?', '%charlotte%').first
restaurants = Restaurant.where(:city_id => cities)
menus = Menu.approved.where(:restaurant_id => restaurants)
items = Item.where('name ILIKE ?', '%mahi%').where(:menu_id => menus)

found_menus = Menu.left_joins(:items).where(:items => {:id => items}).distinct
found_menus.each do |m|
  l = LeadScoringReport::Basic.find_or_create_by(:customer_id => customer.id, :restaurant_id => m.restaurant_id, :menu_id => m.id, :type => "LeadScoringReport::Basic")
  l.compute_score
end