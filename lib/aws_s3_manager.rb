class AwsS3Manager

  attr_reader :aws_manager, :client

  def initialize(access_key_id = ENV['AWS_S3_ACCESS_KEY'], secret_access_key = ENV['AWS_S3_SECRET'], region = 'us-west-2')
    @client = Aws::S3::Client.new(access_key_id: access_key_id,
                                  secret_access_key: secret_access_key,
                                  region: region)
  end

  # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/Object.html#put-instance_method
  def store_file(file_name, file_content, bucket_name = ENV['AWS_BUCKET'])
    s3 = Aws::S3::Resource.new(client: @client)
    bucket = s3.bucket(bucket_name)
    obj = bucket.object(file_name)
    obj.put(body: file_content)
    obj
  end

  def get_file(file_name, bucket_name = ENV['AWS_BUCKET'])
    s3 = Aws::S3::Resource.new(client: @client)
    bucket = s3.bucket(bucket_name)
    bucket.object(file_name)
  end

  # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/Presigner.html
  # if you need to pass extra pass, e.g. expire time
  def get_presigned_url(object, params={})
    signer = Aws::S3::Presigner.new(client: @client)
    signed_params = {
      bucket: object.bucket_name,
      key: object.key
    }.merge(params)
    url, _ = signer.presigned_request(:get_object, signed_params)
    url
  end

  def get_bucket(bucket_name)
    s3 = Aws::S3::Resource.new(client: @client)
    s3.bucket(bucket_name)
  end

  def self.key_to_url(key, bucket_name = "hydra-prod-01")
    return if key.to_s.blank?
    aws = AwsS3Manager.new
    obj = aws.get_file(key, bucket_name)
    CloudfrontHelper.signed_url(obj.public_url)
  end

  def self.csv_data_to_file(csv_data, file_name)
    tempfile = Tempfile.new('foo')
    tempfile.write csv_data
    tempfile.rewind

    aws_manager = AwsS3Manager.new
    obj = aws_manager.store_file(file_name, tempfile.read)
    CloudfrontHelper.signed_url(obj.public_url)
  end

  def create_folder(folder_name, bucket_name = ENV['AWS_BUCKET'])
    s3 = Aws::S3::Resource.new(client: @client)
    bucket = s3.bucket(bucket_name)

    folder_key = "#{folder_name}/"

    bucket.object(folder_key).put
    puts "Folder '#{folder_name}' created in bucket '#{bucket_name}'."
  end

  def store_file_in_folder(folder_name, file_name, file_content, bucket_name = ENV['AWS_BUCKET'])
    s3 = Aws::S3::Resource.new(client: @client)
    bucket = s3.bucket(bucket_name)

    # Create folder before saving
    create_folder(folder_name, bucket_name)

    # Prefix the file name with the folder path
    object_key = "#{folder_name}/#{file_name}"

    obj = bucket.object(object_key)

    # Upload the file to the specified folder
    obj.put(body: file_content)

    puts "File '#{file_name}' saved to folder '#{folder_name}' in bucket '#{bucket_name}'."
    obj
  end

  def list_files_in_folder(folder_name, bucket_name = ENV['AWS_BUCKET'])
    response = @client.list_objects_v2(
      bucket: bucket_name,
      prefix: "#{folder_name}/" # Include trailing slash for folder
    )

    if response.contents.empty?
      puts "No files found in folder '#{folder_name}' in bucket '#{bucket_name}'."
      return []
    end

    file_keys = response.contents.map(&:key)
    # puts "Files in folder '#{folder_name}':"
    # file_keys.each { |key| puts "- #{key}" }

    file_keys
  end

  def find_file_by_name(folder_name, file_pattern, bucket_name = ENV['AWS_BUCKET'])
    files = list_files_in_folder(folder_name, bucket_name)

    files.each do |file|
      if File.basename(file).match?(file_pattern)
        puts "Found file in AWS:#{bucket_name}/#{folder_name} file: #{file}"
        return file
      end
    end

    # puts "No file matching '#{file_pattern}' found in folder '#{folder_name}'."
    nil
  end

  def get_bucket_policy(bucket_name = ENV['AWS_BUCKET'])
    response = @client.get_bucket_policy(bucket: bucket_name)
    JSON.parse(response.policy.read)
  rescue Aws::S3::Errors::NoSuchBucketPolicy
    {}
  end

  def check_cloudfront_access?(bucket_name: ENV['AWS_BUCKET'])
    oai_arn = 'arn:aws:iam::cloudfront'
    policy = get_bucket_policy(bucket_name)
    statements = policy['Statement'] || []
    statements.any? do |statement|
      effect = statement['Effect']
      actions = [statement['Action']].flatten
      resources = [statement['Resource']].flatten
      principal = statement['Principal']

      if principal.is_a?(String)
        principals = [principal]
      elsif principal.is_a?(Hash) && principal['AWS']
        principals = [principal['AWS']].flatten
      else
        principals = []
      end

      effect == 'Allow' && actions.include?('s3:GetObject') &&
        principals.any? { |principal| principal.include? oai_arn } &&
        resources.include?("arn:aws:s3:::#{bucket_name}/*")
    end
  end

  def get_cloudfront_url(folder_name, file_pattern, bucket_name = ENV['AWS_BUCKET'])
    file_name = find_file_by_name(folder_name , file_pattern , bucket_name )
    obj = get_file(file_name, bucket_name)
    CloudfrontHelper.signed_url(obj.public_url)
  end
end
