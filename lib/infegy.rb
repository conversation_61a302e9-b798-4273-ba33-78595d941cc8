class Infegy
  API_AGG_ENDPOINT = 'https://starscape.infegy.com/api/query/agg'.freeze
  API_RAW_QUERY_ENDPOINT = 'https://starscape.infegy.com/api/query/raw'.freeze
  API_TOPIC_ENDPOINT = 'https://starscape.infegy.com/api/query/topics'.freeze
  API_AI_SUMMARY_ENDPOINT = 'https://starscape.infegy.com/api/query/ai-summary'.freeze
  API_KEY = ENV['INFEGY_API_KEY']

  class << self
    def social_sentiment_analysis(query, start_date = Date.today - 1.year, end_date = Date.today)
      client = new('ds_gj4u3F40SLa', [query])
      sentiment = client.sentiment_analysis(start_date, end_date)
      conversations = client.monthly_social_conversations_over_time(start_date, end_date)
      topics = client.top_topics(start_date, end_date)

      # Get last two months of conversation data
      sorted_months = conversations.keys.sort
      current_month = conversations[sorted_months.last] || 0
      previous_month = conversations[sorted_months[-2]] || 0
      monthly_growth = previous_month.zero? ? 0 : ((current_month - previous_month) / previous_month.to_f * 100)

      # Format topics data
      formatted_topics = if topics.present?
        total = topics.sum { |_, count| count }
        topics.map do |name, count|
          percentage = (count.to_f / total * 100).round(2)
          "#{name}: #{percentage}% (#{count} mentions)"
        end
      else
        []
      end

      {
        sentiment_analysis: sentiment || { positive_percentage: 0, negative_percentage: 0, neutral_percentage: 0 },
        social_conversations: {
          current_month: "#{current_month} mentions",
          previous_month: "#{previous_month} mentions",
          monthly_growth_rate: "#{monthly_growth.round(2)}%"
        },
        topics: formatted_topics
      }
    end
  end

  def initialize(dataset_id = 'ds_gj4u3F40SLa', values = [])
    @dataset_id = dataset_id
    @values = values
  end

  def monthly_social_conversations_over_time(start_date, end_date)
    query = {
      dataset_id: @dataset_id,
      query: query(start_date, end_date),
      aggs: {
        Histogram: {
          op: :histogram,
          field: :published,
          interval: :month
        }
      }
    }

    result = api_request(query)

    return result if result[:error]

    result['Histogram']['_buckets'].reduce({}) do |acc, bucket|
      acc[bucket['_label']] = bucket['_count']
      acc
    end
  end

  def sentiment_analysis(start_date, end_date)
    query = {
      dataset_id: @dataset_id,
      query: query(start_date, end_date),
      aggs: {
        'sentiment totals': {
          op: 'keyword',
          field: 'sentiment'
        }
      }
    }

    result = api_request(query)

    return result if result[:error]

    # Calculate percentages
    positive_count = result['sentiment totals']['_buckets'].find { |bucket| bucket['_label'] == 'positive' }&.dig('_count') || 0
    negative_count = result['sentiment totals']['_buckets'].find { |bucket| bucket['_label'] == 'negative' }&.dig('_count') || 0
    neutral_count = result['sentiment totals']['_buckets'].find { |bucket| bucket['_label'] == 'neutral' }&.dig('_count') || 0
    total_sentiments = positive_count + negative_count + neutral_count

    return { positive_percentage: 0, negative_percentage: 0, neutral_percentage: 0 } if total_sentiments.zero?

    positive_percentage = (positive_count.to_f / total_sentiments * 100).round(2)
    negative_percentage = (negative_count.to_f / total_sentiments * 100).round(2)
    neutral_percentage = (neutral_count.to_f / total_sentiments * 100).round(2)

    { positive_percentage: positive_percentage, negative_percentage: negative_percentage, neutral_percentage: neutral_percentage }
  end

  def top_topics(start_date, end_date)
    query = {
      dataset_id: @dataset_id,
      query: query(start_date, end_date),
      topics: {
        size: 70
      }
    }

    result = api_request(query, API_TOPIC_ENDPOINT)

    result['topics'].sort { _1['count'] }.map { |topic| [topic['label'], topic['count']] }
  end

  def ai_summarization(start_date = Date.today - 1.year, end_date = Date.today)
    query = {
      dataset_id: @dataset_id,
      query: {
        op: 'contains',
        fields: [
          'title',
          'body'
        ],
        value: 'election'
      }
    }
2
    result = api_request(query, API_AI_SUMMARY_ENDPOINT)

    result['summary']
  end

  def mentions_by_state(ingredient, state = '', start_date = Date.today - 1.year, end_date = Date.today)
    query = {
      dataset_id: @dataset_id,
      query: {
        op: 'and',
        values: [
          { op: 'contains', value: 'en', fields: ['language'] },
          { op: 'contains', field: 'country', value: 'US' },
          { op: 'contains', field: 'body', values: [ingredient] },
          { op: 'range', field: 'published', lower: start_date, upper: end_date },
          { op: 'contains', field: 'us_state', value: state }
        ]
      },
      aggs: {
        popularity: {
          op: 'keyword',
          field: 'country'
        }
      }
    }

    result = api_request(query)

    result['popularity']['_buckets'][0].to_h['_count']
  end

  private

  def api_request(query, url = API_AGG_ENDPOINT)
    uri = URI(url)
    request = Net::HTTP::Post.new(uri, {'Content-Type' => 'application/json', 'Authorization' => "Bearer #{API_KEY}"})
    request.body = query.to_json

    response = Net::HTTP.start(uri.hostname, uri.port, use_ssl: true) do |http|
      http.request(request)
    end

    parse_response(response)
  end

  def parse_response(response)
    return JSON.parse(response.body) if response.is_a?(Net::HTTPSuccess)
    { error: 'Failed to fetch data', status: response.code, body: response.body }
  end

  def query(start_date, end_date)
    {
      op: 'and',
      values: [
        {
          op: 'contains',
          value: 'en',
          fields: [
            'language'
          ],
          labels: []
        },
        {
          op: 'contains',
          fields: [
            'site'
          ],
          labels: [
            'instagram.com',
            'tiktok.com',
            'pinterest.com'
          ],
          values: [
            'instagram.com',
            'tiktok.com',
            'pinterest.com'
          ]
        },
        {
          op: 'contains',
          fields: [
            'body',
            'title',
            'description'
          ],
          labels: [],
          values: @values
        },
        {
          op: 'range',
          field: 'published',
          lower: start_date,
          upper: end_date
        }
      ]
    }
  end
end
