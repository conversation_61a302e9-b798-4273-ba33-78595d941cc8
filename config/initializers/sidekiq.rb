require "sidekiq/throttled"

# Configure Sidekiq to use the same Redis instance
Sidekiq.configure_server do |config|
  config.redis = {
    url: ENV['REDIS_URL'],
    pool: { size: 15 },
    ssl_params: { verify_mode: OpenSSL::SSL::VERIFY_NONE }
  }
  # config.options[:concurrency] = 1
  config.client_middleware do |chain|
    chain.add SidekiqUniqueJobs::Middleware::Client
  end

  config.server_middleware do |chain|
    chain.add SidekiqUniqueJobs::Middleware::Server
  end

  SidekiqUniqueJobs::Server.configure(config)
end

Sidekiq.configure_client do |config|
  config.redis = {
    url: ENV['REDIS_URL'],
    pool: { size: 8 },
    ssl_params: { verify_mode: OpenSSL::SSL::VERIFY_NONE }
  }

  config.client_middleware do |chain|
    chain.add SidekiqUniqueJobs::Middleware::Client
  end
end

# Sidekiq::Throttled.setup!
# Sidekiq.strict_args!

# Sidekiq::Extensions.enable_delay!

Sidekiq::Throttled::Registry.add(
  :throttle_openai,
  :threshold => {
    :limit => (ENV['OPENAI_THROTTLE_LIMIT_MINUTE'].to_i.abs.nonzero? || 59),
    :period => 1.minute
  }
)

Sidekiq::Throttled::Registry.add(
  :web_scraping,
  :threshold => {
    :limit => 12,
    :period => 1.minute
  }
)

Sidekiq::Throttled::Registry.add(
  :product_scraping,
  :threshold => {
    :limit => 12,
    :period => 1.minute
  }
)

Sidekiq::Throttled::Registry.add(
  :items_assigning,
  :concurrency => {
    :limit => (ENV['ASSIGN_ITEMS_CONCURRENCY_LIMIT'].to_i.abs.nonzero? || 5),
  }
)

Sidekiq::Throttled::Registry.add(
  :quarter_products_assigning,
  :concurrency => {
    :limit => 10,
  }
)


Sidekiq::Throttled::Registry.add(
  :import_product_csv,
  :concurrency => {
    :limit => 1,
  }
)

Sidekiq::Throttled::Registry.add(
  :product_category_batch,
  :concurrency => {
    :limit => 3
  }
)
