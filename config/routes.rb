Rails.application.routes.draw do
  resources :limited_time_offers, only: [:index, :update]
  resources :menu_item_dashboards do
    get :data
    resources :items, only: %i[index], controller: 'menu_item_dashboards/items' do
      patch :toggle_excluded
    end
    resources :ingredients, only: %i[index], controller: 'menu_item_dashboards/ingredients' do
      patch :toggle_top_ingredient
      patch :toggle_excluded
    end
    post :set_cover_image
    get :yelp_images
    post :assign_items
  end
  resources :menu_item_dashboards_2, only: [:show] do
    get :menu_mentions, on: :collection
    get :top_cousines, on: :collection
    get :top_chains, on: :collection
    get :top_regions, on: :collection
    get :menu_mentions_table, on: :collection
    get :quarters, on: :collection
  end
  get 'open_ai/new', defaults: { format: :json }
  get 'chat_logs', to: 'chat_logs#index'
  resources :projects do
    resources :lead_scoring_reports, module: 'projects'

    get :restaurants
    get :project_menus_datatable
    get :gpt_ingredients
    post :run_gpt_ingredients
    post :calculate_items
    post :generate_gpt_sheet
    post :add_gpt_corrections
    post :set_live
    post :run_gmi
    post :generate_gmi_sheet
    post :run_wine_items
    post :generate_wine_sheet
    post :generate_recommendations
    post :generate_sku_report
    get :products

    post :set_yelp_urls
    post :generate_yelp_sheet
    post :upload_yelp_corrections

    post :set_scraper_urls
    post :generate_scraper_urls_sheet
    post :upload_scraper_corrections

    post :post_hits
    post :mass_approve
    post :sync_yelp
    post :live_list

    post :categorize
    post :lead_scoring_reports

    post :translate
    post :sync_hours_of_operation

    post :add_food_trends
    post :clear_gpt_ingredients

    post :set_embeddings
    post :set_product_embeddings
    post :calculate_mturk_cost
  end
  resources :mturk_tik_tok_results
  resources :tik_tok_trends
  resources :tik_tok_videos do
    post :approve
    post :repost
    post :set_failed
    post :update_tik_tok_video
  end
  namespace :report do
    resources :saveds, only: %i[index create destroy]
  end
  resources :reports do
    resources :items, only: %i[index], controller: 'reports/items' do
      patch :toggle_exclude
    end
    post 'auto_check_of_items'
    collection do
      get :top_chains
      get :send_plant_based_protein
    end
    resources :yelp_categories, only: %i[index], controller: 'reports/yelp_categories'
  end
  namespace :reports do
    resources :categories, only: %i[update] do
      get 'items'
    end
  end

  resources :signups do
    collection do
      get :contact_alt
    end
  end

  resources :sample

  resources :articles do
    collection do
      get :get_url_info
    end
  end
  resources :brands
  resources :regions

  devise_for :users, controllers: {sessions: 'sessions'}

  resources :users do
    patch 'update_search_options'
  end
  post 'create_user' => 'users#create', as: :create_user
  # For details on the DSL available within this file, see http://guides.rubyonrails.org/routing.html
  root 'welcome#index'

  post '/users/update_flag', to: 'users#update_flag'


  get 'search' => 'search#index'
  get 'items_pagination_search' => 'search#items_pagination'
  # get 'items_categories_search' => 'search#items_categories'
  get 'filter_options_search' => 'search#filter_options'
  get 'search_category_items_count' => 'search#category_items_count'

  resources :restaurants do
    resources :versions, module: 'restaurants'

    post :add_restaurant_to_chain
    post :remove_from_chain
    get :yelp_images
    post :scrap_images
    post :fetch_yelp_dates
    post :map_images
    get :versions
  end
  resources :customers do
    post :set_api_secret
  end

  resources :chains do
    resources :restaurants, only: %i[index create destroy], controller: 'chains/restaurants', defaults: { format: :json } do
      collection do
        # get :edit_locations
        post :update_many
      end
    end
    get 'edit_locations', to: 'chains/restaurants#edit_locations' # /chains/:chain_id/edit_locations
    get :approved_menu_count
    get :dashboard
    get :locations_by_counties
    post :approve_locations, to: 'chains/restaurants#approve_locations'


    collection do
      post :merge
    end
  end

  resources :menus do
    post :repost_menu
    post :delete_all_items
    post :delete_duplicate_items
    post :update_menu_items
    post :update_restaurant_menu
    post :update_all_in_chain
    post :set_as_chain_menu
    post :set_failed
    post :reject
    post :scrap
    post :pulse_extract
    post :lead_score
    post :categorize
    get 'versions' => 'menu_versions#index'
    get :advanced
  end

  resources :chain_menus do
    post :update_menu_items
    post :delete_all_items
  end


  get 'restaurant_menu/:id', to: 'menus#customer_show', as: 'menu_customer_show'

  get "scrap_failed" => "menus#scrap_failed"

  resources :menu_versions do
    post :paste_version
  end

  resources :categories
  resources :items do
    get :predict_tree
  end
  resources :mturk_menu_results
  resources :cities do
    post :post_hits
  end
  resources :mturk_hits do
    post :paste_worker_results
    post :reject_hit
  end
  resources :mturk_workers do
    collection do
      post :ban_worker
    end
    get :mturk_worker_menu_results
  end

  resources :import_restaurants do
    collection do
      post :query
      post :confirm_import
    end
  end

  resources :log_events
  resources :pulse_logs

  resources :recipes
  resources :recipe_categories
  resources :recipe_subcategories

  # require 'sidekiq/web'
  require "sidekiq_unique_jobs/web"
  require 'sidekiq/cron/web'

  authenticate :user, lambda { |u|  u.has_any_role?(:admin) } do
    # Sidekiq::Throttled::Web.enhance_queues_tab!


    mount Sidekiq::Web, at: "/sidekiq"

    resources :dashboard_tools, only: %i[index create]
  end

  resources :item_categories do
    patch :toggle_approved
    collection do
      post :merge
    end
  end
  resources :item_subcategories
  resources :normalized_item do
    collection do
      post :merge
    end
  end
  resources :ingredients do
    collection do
      get :potential_duplicates
      post :merge
    end
  end

  resources :ingredient_menu_categories, only: %i[index]

  resources :gpt_ingredients do
    get :product_lists
  end
  resources :blacklist_ingredients

  post 'change_state' => 'menus#change_state'

  get 'deliveries' => 'admin#deliveries'
  post 'assign_me' => 'admin#add_assing_user'
  get 'weekly_cost' => 'admin#weekly_cost'

  resources :apply_categorizations do
    collection do
      post 'update_items'
    end
  end

  namespace :admin do
    resources :cities, only: %i[index], controller: 'cities', defaults: { format: :json }
    resources :products_l_levels_counts, only: [:index]
  end

  resources :admin do
    collection do
      resources :quarterly_restaurants, module: 'admin' do
        collection do
          post :upload_corrections
        end
      end
      post :update_assigned_user
      get :tools
      post :post_quarterly_menus
      post :post_quarterly_chains
    end
  end

  resources :retailers
  resources :stores
  resources :product_categories
  resources :product_subcategories do
    post :delete_all
    post :scrap
  end
  resources :products
  resources :contents
  resources :scrapper_results
  resources :open_ai_logs
  resources :yelp_images do
    post :check_accessible
  end
  resources :standard_menu_items do
    collection do
      post 'merge'
    end
  end
  resources :ch_w_customers


  namespace :api do
    namespace :cw, defaults: { format: :json} do
      resources :customers
      post '/update_ingredient', to: "/api/cw/ingredients#update_ingredient"
    end
    namespace :partners, defaults: { format: :json} do
      resources :restaurants
    end
    get '/current_user', to: '/api/users#current'
  end

  namespace :menu_api do
    namespace :v1, defaults: { format: :json} do
      devise_for :users, controllers: {
        sessions: 'menu_api/v1/users/sessions',
      }, defaults: { format: :json }

      resources :chat do
        resources :messages, module: 'chat'
      end
      resources :menu_item_dashboards do
        get :fastest_growing_dish_categories, on: :collection
        get :innovations, on: :collection
        get :most_popular_limited_time_offers, on: :collection
        get :fastest_growing_menu_items, on: :collection
        get :most_menu_mentions, on: :collection
      end
      resources :ingredients do
        resources :items, module: 'ingredients'
        get :fastest_growing_ingredients, on: :collection
        get :highest_growing_ingredients, on: :collection
        get :pairing_trends, on: :collection
        get :ingredients_innovation, on: :collection
        get :ingredients_by_stage, on: :collection
        get :ingredient_pairing_trends
        get :menu_types
        get :pen_rate_over_time
        get :consummation_habbits
        get :social_sentiments
        get :top_retailers
        get :top_manufacturers
        get :retails_images
        get :most_common_product_category
        get :highest_growing_ingredient_by_category
        get :retail_growth
        get :products_table
        get :mentions_by_state
        get :consumer_experience
        get :consumed_by_generation
        post :favorite, to: 'favorites#create'
        delete :unfavorite, to: 'favorites#destroy'
        collection do
          get 'customer_insights/most_talked_about', to: 'ingredients/customer_insights#most_talked_about'
          get 'customer_insights/most_liked_ingredients', to: 'ingredients/customer_insights#most_liked_ingredients'
          get 'customer_insights/consumer_favorite_retailers', to: 'ingredients/customer_insights#consumer_favorite_retailers'
        end
      end
      resources :retailers do
        collection do
          get 'top_retailers', to: 'retailers/reports#top_retailers'
        end
      end
      resources :products do
        collection do
            get 'fastest_growing_ingredient_category', to: 'products#fastest_growing_ingredient_category'
          get 'white_space_opportunities', to: 'products#white_space_opportunities'
        end
      end
      get :favorites, to: 'favorites#index'
      resources :menu_items do
        collection do
          get :fastest_growing_dish_categories_chart
          get :fastest_growing_dish_categories_table
          get :menu_items_innovation
          get :popular_limited_time_offers
          get :highest_penetration
        end
      end
      resources :menus
      resources :restaurants
      resources :retail_insights, only: [] do
        member do
          get :images
          get :top_manufacturers
          get :top_retailers
          get :product_categories
          get :growth_categories
          get :retail_growth
          get :top_skus
        end
      end
    end
  end

  post "replicate" => 'replicate#create'

  resources :lead_items

  resources :lead_scoring_reports

  namespace :dole do
    get "avi" => "reports#avi"
    get "analytic_methods" => "reports#analytic_methods"
    get "pt" => "reports#pt"
    get "pmt" => "reports#pmt"
  end

  post "no_bots_allowed" => 'no_bots#no_bots_allowed'

  patch 'geo_price' => 'geo_prices#update'

  resources :mc_dashboards do
    resources :items, module: 'mc_dashboards'
    resources :chains_data_rows, module: 'mc_dashboards'
    resources :limited_time_offers, module: 'mc_dashboards'
    resources :comments, module: 'mc_dashboards'
    resources :social_sentiments, module: 'mc_dashboards', only: %i[] do
      get :top_topics, on: :collection
      get :sentiments, on: :collection
      get :social_conversations, on: :collection
    end

    get :top_flavors
    get :top_ingredients
    get :top_sauces
    get :top_adjectives
    get :top_chains
    get :latest_news, on: :collection
    get :google_trend
    get :menu_mentions
    get :carousel_images
    post :update_chains
    post :run_chicken_surveys
    post :set_chain_menus
    post :update_top_chains_data_rows
    get :pricing_matrix
    get :social_sentiment
    post :run_social_sentiment
    get :top_descriptors
    get :average_cost
    post :run_top_ingredients
    post :run_adjectives
    get :geo_prices

    collection do
      resources :countries, module: 'mc_dashboards'
    end
  end

  resources :menu_category_boards
  resources :menu_subcategory_boards

  namespace :customer do
    resources :chain_menus
  end

  resources :boards, controller: 'mc_dashboards'

  resources :retail_insights, only: %i[index show] do
    get :mentions, on: :collection
    get :top_categories, on: :collection
    get :top_brands, on: :collection
    get :top_retailers, on: :collection
    get :products, on: :collection
  end

  resources :chat do
    resources :messages, module: 'chat'
  end

end
