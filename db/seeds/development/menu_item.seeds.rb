# bin/rake db:seed:development:menu_item

return if !Rails.env.development?

def random_percent
  (rand * 100).round(2)
end

def random_count
  rand(0..1000)
end

# Google Sheet URL https://docs.google.com/spreadsheets/d/14tapFer2p9CG4DS1TgcZLX0DdB54092t2ERp2g38aKU/edit?pli=1&gid=1078236796#gid=1078236796
csv_url = "https://docs.google.com/spreadsheets/d/e/2PACX-1vTsdevWkN_0DIJiEdufdOmo8AZ4yiAj0pdIPQcznYTvyNVtTB0Lyf9_Jy0Z1cpArhdDQZV-6BK29Vsd/pub?gid=1078236796&single=true&output=csv"
rows = CSV.parse(URI.open(csv_url).read, :headers => true, :header_converters => lambda{|h|h.to_s.downcase.strip})
rows.each do |row|
  name = row["gmi"]
  category = row["category"]
  subcategory = row["subcategory"]

  g = GmiCategory.find_or_create_by(:name => category)
  gs = GmiSubcategory.find_or_create_by(:name => subcategory, :gmi_category_id => g.id)
  m = MenuItem.find_or_create_by(:name => name, :category => category, :subcategory => subcategory, :gmi_category_id => g.id, :gmi_subcategory_id => gs.id)
  m.init_quarters

  m.quarters.each do |q|
    q.pen_rate = random_percent
    q.delta_last_period = random_percent
    q.items_count = random_count
    q.save
  end
  q4 = m.quarters.last

  m.pen_rate = q4.pen_rate
  m.foodservice_growth = q4.delta_last_period
  m.foodservice_prediction = (q4.delta_last_period.to_f * 1.05).to_f.clamp(0,100) * [1, -1].sample
  m.menu_mention_count = q4.items_count
  m.save
end


GmiCategory.all.each do |g|
  g.menu_mention_count = g.menu_items.sum(:menu_mention_count)
  g.pen_rate = g.menu_items.average(:pen_rate)
  g.foodservice_growth = g.menu_items.average(:foodservice_growth)
  g.foodservice_prediction = g.menu_items.average(:foodservice_prediction)
  g.save
end

GmiSubcategory.all.each do |g|
  g.menu_mention_count = g.menu_items.sum(:menu_mention_count)
  g.pen_rate = g.menu_items.average(:pen_rate)
  g.foodservice_growth = g.menu_items.average(:foodservice_growth)
  g.foodservice_prediction = g.menu_items.average(:foodservice_prediction)
  g.save
end
