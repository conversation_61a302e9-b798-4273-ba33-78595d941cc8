# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.1].define(version: 2025_06_17_170846) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_trgm"
  enable_extension "plpgsql"
  enable_extension "vector"

  # Custom types defined in this database.
  # Note that some types may not work with other database engines. Be careful if changing database.
  create_enum "report_types", ["lead_generation", "menu_category"]
  create_enum "sex", ["male", "female"]

  create_table "access_locations", force: :cascade do |t|
    t.string "name"
    t.bigint "customer_id"
    t.string "locationable_type"
    t.bigint "locationable_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["customer_id"], name: "index_access_locations_on_customer_id"
    t.index ["locationable_type", "locationable_id"], name: "index_access_locations_on_locationable"
  end

  create_table "activity_logs", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.integer "project_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "adjectives", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "articles", force: :cascade do |t|
    t.string "title"
    t.text "description"
    t.string "url"
    t.bigint "brand_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "source"
    t.index ["brand_id"], name: "index_articles_on_brand_id"
  end

  create_table "auth_secrets", force: :cascade do |t|
    t.string "secret_key"
    t.integer "customer_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "batch_ai_operations", force: :cascade do |t|
    t.string "batch_id"
    t.string "status", default: "pending"
    t.string "service_type"
    t.jsonb "result"
    t.datetime "completed_at"
    t.bigint "open_ai_log_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "results_file_url"
    t.index ["id"], name: "idx_batch_ai_operations_on_results_file_url_not_null", where: "(results_file_url IS NOT NULL)"
    t.index ["open_ai_log_id"], name: "index_batch_ai_operations_on_open_ai_log_id"
    t.index ["service_type"], name: "idx_batch_ai_operations_on_service_type"
    t.index ["status"], name: "idx_batch_ai_operations_on_status"
  end

  create_table "batch_assignments", force: :cascade do |t|
    t.bigint "batch_ai_operation_id", null: false
    t.string "batchable_type", null: false
    t.bigint "batchable_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["batch_ai_operation_id", "batchable_id", "batchable_type"], name: "index_batch_assignments_on_ai_op_and_batchable", unique: true
    t.index ["batch_ai_operation_id"], name: "index_batch_assignments_on_batch_ai_operation_id"
    t.index ["batchable_type", "batchable_id"], name: "index_batch_assignments_on_batchable"
  end

  create_table "beverages", force: :cascade do |t|
    t.string "name"
    t.string "beverage_type"
    t.integer "year"
    t.string "region"
    t.string "color"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.vector "embedding", limit: 1536
    t.index ["embedding"], name: "index_beverages_on_embedding", opclass: :vector_cosine_ops, using: :hnsw
  end

  create_table "blacklist_ingredients", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "brand_projects", force: :cascade do |t|
    t.integer "brand_id"
    t.integer "project_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "brands", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "state"
    t.string "approved_by"
    t.datetime "approved_at"
    t.string "flagged_by"
    t.datetime "flagged_at"
    t.integer "items_count", default: 0
  end

  create_table "carousel_images", force: :cascade do |t|
    t.text "url"
    t.text "caption"
    t.string "business_name"
    t.integer "menu_item_dashboard_id"
    t.integer "chain_id"
    t.integer "order"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "name"
  end

  create_table "categories", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "name"
    t.string "alias"
    t.boolean "tracking"
    t.string "mapped_name"
    t.index ["mapped_name"], name: "index_categories_on_mapped_name"
    t.index ["name"], name: "index_categories_on_name"
  end

  create_table "category_item_categories", force: :cascade do |t|
    t.bigint "category_id"
    t.bigint "item_category_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["category_id"], name: "index_category_item_categories_on_category_id"
    t.index ["item_category_id"], name: "index_category_item_categories_on_item_category_id"
  end

  create_table "ch_w_customer_projects", force: :cascade do |t|
    t.integer "project_id"
    t.integer "ch_w_customer_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "ch_w_customers", force: :cascade do |t|
    t.string "customer_name"
    t.string "customer_id"
    t.string "address"
    t.string "zip"
    t.text "yelp_url"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "state", default: "scanned"
    t.string "approved_by"
    t.datetime "approved_at"
    t.string "failed_by"
    t.datetime "failed_at"
    t.text "notes"
    t.datetime "gpt_reviewed_at"
    t.string "gpt_reviewed_by"
    t.integer "restaurant_id"
    t.string "city_name"
    t.string "state_name"
    t.string "doordash_url"
    t.string "uber_eats_url"
    t.string "open_table_url"
    t.string "grubhub_url"
    t.string "all_menus_url"
    t.text "menu_url"
    t.index ["restaurant_id"], name: "index_ch_w_customers_on_restaurant_id"
  end

  create_table "chains", force: :cascade do |t|
    t.string "name"
    t.text "menu_url"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "corporate_address"
    t.string "corporate_address_city"
    t.string "corporate_address_state"
    t.string "corporate_address_zip"
    t.bigint "city_id"
    t.string "contact_email_address"
    t.integer "total_locations"
    t.string "state_status"
    t.datetime "approved_at"
    t.string "approved_by"
    t.datetime "flagged_at"
    t.string "flagged_by"
    t.integer "country_id"
    t.string "location_url"
    t.datetime "locations_at"
    t.string "locations_by"
    t.datetime "generalized_at"
    t.string "scrap_loc_type"
    t.integer "restaurants_count", default: 0
    t.uuid "guid"
    t.string "instagram_name"
    t.integer "average_review_count"
    t.string "average_price"
    t.float "average_rating"
    t.boolean "pull_quarterly", default: false
    t.index ["city_id"], name: "index_chains_on_city_id"
  end

  create_table "chat_messages", force: :cascade do |t|
    t.text "message"
    t.integer "chat_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "message_type"
  end

  create_table "chats", force: :cascade do |t|
    t.uuid "guid"
    t.text "topic"
    t.integer "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.json "message_context"
    t.integer "response_time"
  end

  create_table "chicken_surveys", force: :cascade do |t|
    t.integer "item_id"
    t.boolean "is_chicken", default: false
    t.string "chicken_kind"
    t.boolean "is_bundle", default: false
    t.boolean "is_boneless", default: false
    t.integer "boneless_count"
    t.string "boneless_chicken_type"
    t.string "seasoning_type"
    t.boolean "is_spicy", default: false
    t.string "characteristics", array: true
    t.string "bundle_items", array: true
    t.boolean "contains_sauce", default: false
    t.string "sauce_type"
    t.string "flavor_type"
    t.boolean "is_innovative", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.float "cost_per_strip"
  end

  create_table "cities", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "name"
    t.integer "assigned_user_id"
    t.integer "state_id"
    t.integer "country_id"
    t.bigint "county_id"
    t.index ["assigned_user_id"], name: "index_cities_on_assigned_user_id"
    t.index ["country_id"], name: "index_cities_on_country_id"
    t.index ["county_id"], name: "index_cities_on_county_id"
    t.index ["state_id", "country_id"], name: "index_cities_on_state_id_and_country_id"
    t.index ["state_id"], name: "index_cities_on_state_id"
  end

  create_table "city_projects", force: :cascade do |t|
    t.integer "city_id"
    t.integer "project_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["city_id", "project_id"], name: "index_city_projects_on_city_id_and_project_id"
  end

  create_table "city_regions", force: :cascade do |t|
    t.bigint "city_id"
    t.bigint "region_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["city_id", "region_id"], name: "index_city_regions_on_city_id_and_region_id"
    t.index ["city_id"], name: "index_city_regions_on_city_id"
    t.index ["region_id"], name: "index_city_regions_on_region_id"
  end

  create_table "consumer_sentiment_familiarities", force: :cascade do |t|
    t.bigint "ingredient_id", null: false
    t.integer "familiar_1"
    t.integer "familiar_2"
    t.integer "familiar_3"
    t.integer "familiar_4"
    t.integer "familiar_5"
    t.float "familiar_score_average"
    t.integer "num_respondents"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["ingredient_id"], name: "index_consumer_sentiment_familiarities_on_ingredient_id"
  end

  create_table "consumer_sentiment_likes", force: :cascade do |t|
    t.bigint "ingredient_id", null: false
    t.integer "score_1"
    t.integer "score_2"
    t.integer "score_3"
    t.integer "score_4"
    t.integer "score_5"
    t.float "like_score_average"
    t.integer "num_respondents"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "score", default: 0, null: false
    t.enum "sex", default: "male", null: false, enum_type: "sex"
    t.integer "familirity_score", default: 0, null: false
    t.integer "age"
    t.string "ethnicity"
    t.index ["ingredient_id"], name: "index_consumer_sentiment_likes_on_ingredient_id"
  end

  create_table "consumers_favorite_restaurant_chains", force: :cascade do |t|
    t.string "restaurant_chain_name"
    t.integer "count"
    t.float "percent"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "consumers_favorite_retailers", force: :cascade do |t|
    t.string "retailer_name"
    t.integer "count"
    t.float "percent"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "contents", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.string "published_date"
    t.string "image_url"
    t.string "report_url"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "counties", force: :cascade do |t|
    t.bigint "state_id"
    t.string "name"
    t.string "fips_code"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["state_id"], name: "index_counties_on_state_id"
  end

  create_table "countries", force: :cascade do |t|
    t.string "abbrev"
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "language"
  end

  create_table "countries_customers", id: false, force: :cascade do |t|
    t.bigint "country_id"
    t.bigint "customer_id"
    t.index ["country_id", "customer_id"], name: "index_countries_customers_on_country_id_and_customer_id"
    t.index ["country_id"], name: "index_countries_customers_on_country_id"
    t.index ["customer_id"], name: "index_countries_customers_on_customer_id"
  end

  create_table "cuisine_types", force: :cascade do |t|
    t.string "name"
    t.vector "embedding", limit: 1536
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["embedding"], name: "index_cuisine_types_on_embedding", opclass: :vector_cosine_ops, using: :hnsw
  end

  create_table "customer_reports", force: :cascade do |t|
    t.bigint "customer_id", null: false
    t.bigint "report_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["customer_id"], name: "index_customer_reports_on_customer_id"
    t.index ["report_id"], name: "index_customer_reports_on_report_id"
  end

  create_table "customers", force: :cascade do |t|
    t.string "name"
    t.string "plan_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "access_features", default: [], array: true
    t.boolean "has_wine_fields"
    t.string "start_path"
    t.string "logo"
    t.text "company_profile"
  end

  create_table "dashboard_chains", force: :cascade do |t|
    t.integer "chain_id"
    t.integer "menu_item_dashboard_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["chain_id", "menu_item_dashboard_id"], name: "index_dashboard_chains_on_chain_id_and_menu_item_dashboard_id", unique: true
  end

  create_table "dashboard_emerging_chains", force: :cascade do |t|
    t.integer "emerging_chain_id"
    t.integer "menu_item_dashboard_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "dole_product_categories", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "dole_product_lines", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "menu_category_id"
    t.integer "dole_product_category_id"
  end

  create_table "favorites", force: :cascade do |t|
    t.integer "user_id"
    t.integer "ingredient_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id", "ingredient_id"], name: "index_favorites_on_user_id_and_ingredient_id", unique: true
  end

  create_table "flavor_ingredients", force: :cascade do |t|
    t.bigint "ingredient_id"
    t.bigint "flavor_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["flavor_id"], name: "index_flavor_ingredients_on_flavor_id"
    t.index ["ingredient_id"], name: "index_flavor_ingredients_on_ingredient_id"
  end

  create_table "flavors", force: :cascade do |t|
    t.string "tag"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "food_categories", force: :cascade do |t|
    t.string "name"
    t.vector "embedding", limit: 1536
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["embedding"], name: "index_food_categories_on_embedding", opclass: :vector_cosine_ops, using: :hnsw
  end

  create_table "food_trends", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "generalized_items", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "geo_prices", force: :cascade do |t|
    t.decimal "chi_price", precision: 10, scale: 2, default: "0.0"
    t.decimal "la_price", precision: 10, scale: 2, default: "0.0"
    t.decimal "nyc_price", precision: 10, scale: 2, default: "0.0"
    t.decimal "miami_price", precision: 10, scale: 2, default: "0.0"
    t.bigint "menu_item_dashboard_id", null: false
    t.bigint "chain_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "emerging_chain_id"
    t.decimal "richmond_price", precision: 10, scale: 2, default: "0.0"
    t.decimal "dallas_price", precision: 10, scale: 2, default: "0.0"
    t.index ["chain_id"], name: "index_geo_prices_on_chain_id"
    t.index ["menu_item_dashboard_id"], name: "index_geo_prices_on_menu_item_dashboard_id"
  end

  create_table "gmi_categories", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.float "pen_rate"
    t.float "foodservice_growth"
    t.float "foodservice_prediction"
    t.integer "menu_mention_count"
    t.index ["name"], name: "index_gmi_categories_on_name", unique: true
  end

  create_table "gmi_subcategories", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "gmi_category_id"
    t.float "pen_rate"
    t.float "foodservice_growth"
    t.float "foodservice_prediction"
    t.integer "menu_mention_count"
    t.index ["name", "gmi_category_id"], name: "index_gmi_subcategories_on_name_and_gmi_category_id", unique: true
  end

  create_table "google_trends", force: :cascade do |t|
    t.bigint "ingredient_id"
    t.bigint "menu_item_dashboard_id"
    t.jsonb "yearly_trends", default: {}, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "menu_category_board_id"
    t.index ["ingredient_id"], name: "index_google_trends_on_ingredient_id"
    t.index ["menu_category_board_id"], name: "index_google_trends_on_menu_category_board_id"
    t.index ["menu_item_dashboard_id"], name: "index_google_trends_on_menu_item_dashboard_id"
    t.index ["yearly_trends"], name: "index_google_trends_on_yearly_trends", using: :gin
  end

  create_table "gpt_ingredient_sheet_menus", force: :cascade do |t|
    t.integer "gpt_ingredient_sheet_id"
    t.integer "menu_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["gpt_ingredient_sheet_id", "menu_id"], name: "idx_on_gpt_ingredient_sheet_id_menu_id_598864ab78", unique: true
  end

  create_table "gpt_ingredient_sheets", force: :cascade do |t|
    t.text "url"
    t.integer "page_number"
    t.integer "project_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "client_name"
  end

  create_table "gpt_ingredients", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "blacklist", default: false
    t.integer "items_count"
    t.vector "embedding", limit: 1536
    t.integer "emb_product_limit"
    t.index ["embedding"], name: "index_gpt_ingredients_on_embedding", opclass: :vector_cosine_ops, using: :hnsw
  end

  create_table "hf_customers", force: :cascade do |t|
    t.string "business_name"
    t.string "customer_id"
    t.integer "restaurant_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "net_yield_code_c"
  end

  create_table "hf_files", force: :cascade do |t|
    t.string "file_name"
    t.text "csv_url"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "hours_of_operations", force: :cascade do |t|
    t.integer "restaurant_id"
    t.boolean "is_overnight"
    t.string "start_time"
    t.string "end_time"
    t.integer "day"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "images", force: :cascade do |t|
    t.text "url"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "product_id"
    t.index ["product_id"], name: "index_images_on_product_id"
  end

  create_table "imported_csv_files", force: :cascade do |t|
    t.string "file_name", null: false
    t.bigint "file_size", null: false
    t.datetime "imported_at"
    t.string "status", default: "in_progress", null: false
    t.string "import_duration"
    t.integer "total_rows", default: 0
    t.integer "skipped_rows", default: [], array: true
    t.jsonb "missing_fields_rows", default: []
    t.text "error_message"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "link_path"
    t.index ["file_name", "file_size", "status"], name: "index_imported_csv_files_on_file_name_and_file_size_and_status", unique: true
  end

  create_table "ingredient_age_breakdowns", force: :cascade do |t|
    t.bigint "ingredient_id", null: false
    t.float "age_65_likes_score", default: 0.0, null: false
    t.float "age_65_familiarity", default: 0.0, null: false
    t.float "age_55_64_likes_score", default: 0.0, null: false
    t.float "age_55_64_familiarity", default: 0.0, null: false
    t.float "age_45_54_likes_score", default: 0.0, null: false
    t.float "age_45_54_familiarity", default: 0.0, null: false
    t.float "age_35_44_likes_score", default: 0.0, null: false
    t.float "age_35_44_familiarity", default: 0.0, null: false
    t.float "age_25_34_likes_score", default: 0.0, null: false
    t.float "age_25_34_familiarity", default: 0.0, null: false
    t.float "age_18_24_likes_score", default: 0.0, null: false
    t.float "age_18_24_familiarity", default: 0.0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["ingredient_id"], name: "index_ingredient_age_breakdowns_on_ingredient_id"
  end

  create_table "ingredient_dashboards", force: :cascade do |t|
    t.bigint "ingredient_id"
    t.bigint "menu_item_dashboard_id"
    t.boolean "excluded", default: false
    t.boolean "top_ingredient", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["ingredient_id"], name: "index_ingredient_dashboards_on_ingredient_id"
    t.index ["menu_item_dashboard_id"], name: "index_ingredient_dashboards_on_menu_item_dashboard_id"
  end

  create_table "ingredient_ethnicity_breakdowns", force: :cascade do |t|
    t.bigint "ingredient_id", null: false
    t.float "asian_likes_score", default: 0.0, null: false
    t.float "asian_familiarity", default: 0.0, null: false
    t.float "black_likes_score", default: 0.0, null: false
    t.float "black_familiarity", default: 0.0, null: false
    t.float "white_likes_score", default: 0.0, null: false
    t.float "white_familiarity", default: 0.0, null: false
    t.float "mixed_likes_score", default: 0.0, null: false
    t.float "mixed_familiarity", default: 0.0, null: false
    t.float "other_likes_score", default: 0.0, null: false
    t.float "other_familiarity", default: 0.0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["ingredient_id"], name: "index_ingredient_ethnicity_breakdowns_on_ingredient_id"
  end

  create_table "ingredient_menu_categories", force: :cascade do |t|
    t.integer "ingredient_id"
    t.integer "menu_category_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "need_state"
    t.text "need_state_reason"
    t.index ["ingredient_id", "menu_category_id"], name: "idx_on_ingredient_id_menu_category_id_4b38dc28cd", unique: true
  end

  create_table "ingredient_pairing_trends", force: :cascade do |t|
    t.integer "ingredient_a_id"
    t.string "ingredient_b_id"
    t.integer "pairing_count"
    t.float "pairing_percent"
    t.float "change"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "name"
    t.string "category"
    t.index ["ingredient_a_id", "ingredient_b_id"], name: "idx_on_ingredient_a_id_ingredient_b_id_0d7e351b2d", unique: true
  end

  create_table "ingredient_updates", force: :cascade do |t|
    t.integer "menu_id"
    t.integer "item_id"
    t.string "current_ingredient_name"
    t.string "new_ingredient_name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "ingredients", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "state"
    t.text "description"
    t.string "approved_by"
    t.datetime "approved_at"
    t.string "flagged_by"
    t.datetime "flagged_at"
    t.vector "embedding", limit: 1536
    t.string "lifecycle"
    t.integer "menu_category_id"
    t.string "food_type"
    t.string "ingredient_category"
    t.text "ingredient_category_reason"
    t.string "snack_need_state"
    t.text "snack_need_state_reason"
    t.string "beverage_need_state"
    t.text "beverage_need_state_reason"
    t.string "frozen_smoothies_need_state"
    t.text "frozen_smoothies_need_state_reason"
    t.string "frozen_novelties_need_state"
    t.text "frozen_novelties_need_state_reason"
    t.string "taste"
    t.string "aroma"
    t.string "texture"
    t.string "food_type_category"
    t.string "mac"
    t.text "appearance"
    t.integer "menu_item_dashboard_id"
    t.string "need_state"
    t.text "need_state_reason"
    t.string "pdi_category"
    t.string "pdi_subcategory"
    t.float "growth_percent"
    t.uuid "guid"
    t.string "menu_adoption"
    t.float "pen_rate"
    t.float "delta_last_period", default: 0.0
    t.boolean "is_pairing_trend", default: false
    t.float "pen_rate_1y", default: 0.0
    t.float "change_1y", default: 0.0
    t.float "growth_prediction", default: 0.0
    t.index ["embedding"], name: "index_ingredients_on_embedding", opclass: :vector_cosine_ops, using: :hnsw
    t.index ["name"], name: "index_ingredients_on_name"
    t.index ["state"], name: "index_ingredients_on_state"
  end

  create_table "instagram_comments", force: :cascade do |t|
    t.string "username"
    t.text "text"
    t.string "external_id"
    t.bigint "instagram_post_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "comment_topic"
    t.integer "sentiment_score"
    t.boolean "is_food_related"
    t.integer "likes_count"
    t.index ["instagram_post_id"], name: "index_instagram_comments_on_instagram_post_id"
  end

  create_table "instagram_posts", force: :cascade do |t|
    t.string "image"
    t.text "caption"
    t.string "username"
    t.integer "likes_count"
    t.integer "comments_count"
    t.string "external_id"
    t.string "code"
    t.bigint "chain_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "instagram_user_id"
    t.index ["chain_id"], name: "index_instagram_posts_on_chain_id"
    t.index ["instagram_user_id"], name: "index_instagram_posts_on_instagram_user_id"
  end

  create_table "instagram_users", force: :cascade do |t|
    t.string "name"
    t.string "external_id"
    t.bigint "chain_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["chain_id"], name: "index_instagram_users_on_chain_id"
  end

  create_table "item_adjectives", force: :cascade do |t|
    t.integer "item_id"
    t.integer "adjective_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "item_blacklist_ingredients", force: :cascade do |t|
    t.integer "item_id"
    t.integer "blacklist_ingredient_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["item_id", "blacklist_ingredient_id"], name: "idx_on_item_id_blacklist_ingredient_id_27e9df64a6", unique: true
  end

  create_table "item_brands", force: :cascade do |t|
    t.bigint "item_id"
    t.bigint "brand_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["brand_id"], name: "index_item_brands_on_brand_id"
    t.index ["item_id", "brand_id"], name: "index_item_brands_on_item_id_and_brand_id"
    t.index ["item_id"], name: "index_item_brands_on_item_id"
  end

  create_table "item_categories", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "name"
    t.boolean "is_approved", default: false
  end

  create_table "item_dashboards", force: :cascade do |t|
    t.bigint "item_id"
    t.bigint "menu_item_dashboard_id"
    t.boolean "excluded", default: false
    t.boolean "filtered", default: false
    t.datetime "run_open_ai_at"
    t.jsonb "open_ai_response", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["excluded"], name: "index_item_dashboards_on_excluded"
    t.index ["item_id", "menu_item_dashboard_id"], name: "index_item_dashboards_on_item_id_and_menu_item_dashboard_id"
    t.index ["item_id"], name: "index_item_dashboards_on_item_id"
    t.index ["menu_item_dashboard_id"], name: "index_item_dashboards_on_menu_item_dashboard_id"
  end

  create_table "item_flavors", force: :cascade do |t|
    t.bigint "item_id"
    t.bigint "flavor_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["flavor_id"], name: "index_item_flavors_on_flavor_id"
    t.index ["item_id"], name: "index_item_flavors_on_item_id"
  end

  create_table "item_food_trends", force: :cascade do |t|
    t.integer "item_id"
    t.integer "food_trend_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["item_id", "food_trend_id"], name: "index_item_food_trends_on_item_id_and_food_trend_id", unique: true
  end

  create_table "item_gpt_ingredients", force: :cascade do |t|
    t.integer "item_id"
    t.integer "gpt_ingredient_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.float "confidence_score"
    t.text "reason"
    t.index ["gpt_ingredient_id"], name: "index_item_gpt_ingredients_on_gpt_ingredient_id"
    t.index ["item_id", "gpt_ingredient_id"], name: "index_item_gpt_ingredients_on_item_id_and_gpt_ingredient_id", unique: true
    t.index ["item_id"], name: "index_item_gpt_ingredients_on_item_id"
  end

  create_table "item_ingredients", force: :cascade do |t|
    t.integer "item_id"
    t.integer "ingredient_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.float "ing_score"
    t.index ["item_id", "ingredient_id"], name: "index_item_ingredients_on_item_id_and_ingredient_id"
  end

  create_table "item_lead_items", force: :cascade do |t|
    t.integer "item_id"
    t.integer "lead_item_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["item_id", "lead_item_id"], name: "index_item_lead_items_on_item_id_and_lead_item_id", unique: true
  end

  create_table "item_meta_data", force: :cascade do |t|
    t.integer "item_id"
    t.float "score"
    t.string "cuisine_type", default: [], array: true
    t.string "day_part", default: [], array: true
    t.string "menu_type", default: [], array: true
    t.string "dietary_tags", default: [], array: true
    t.string "taste_profile", default: [], array: true
    t.string "texture", default: [], array: true
    t.string "prep_methods", default: [], array: true
    t.string "category"
    t.string "sub_category"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "gmi"
    t.index ["item_id"], name: "index_item_meta_data_on_item_id", unique: true
  end

  create_table "item_products", force: :cascade do |t|
    t.string "AddItemProduct"
    t.integer "item_id"
    t.integer "product_id"
    t.float "confidence_score"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "order_idx"
    t.integer "gpt_ingredient_id"
    t.index ["item_id", "product_id", "gpt_ingredient_id"], name: "idx_on_item_id_product_id_gpt_ingredient_id_72d71c793b", unique: true
  end

  create_table "item_projects", force: :cascade do |t|
    t.integer "item_id"
    t.integer "project_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["item_id", "project_id"], name: "index_item_projects_on_item_id_and_project_id", unique: true
  end

  create_table "item_quarters", force: :cascade do |t|
    t.bigint "item_id"
    t.bigint "quarter_id"
    t.boolean "excluded", default: false
    t.boolean "filtered", default: false
    t.datetime "run_open_ai_at"
    t.jsonb "open_ai_response", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["item_id"], name: "index_item_quarters_on_item_id"
    t.index ["quarter_id"], name: "index_item_quarters_on_quarter_id"
  end

  create_table "item_subcategories", force: :cascade do |t|
    t.integer "item_category_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "name"
  end

  create_table "item_yelp_images", force: :cascade do |t|
    t.string "item_yelp_image"
    t.integer "item_id"
    t.integer "yelp_image_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["item_id", "yelp_image_id"], name: "index_item_yelp_images_on_item_id_and_yelp_image_id", unique: true
  end

  create_table "items", force: :cascade do |t|
    t.string "name"
    t.integer "restaurant_id"
    t.integer "menu_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "category"
    t.text "description"
    t.decimal "price"
    t.integer "mturk_menu_result_id"
    t.integer "item_subcategory_id"
    t.integer "item_category_id"
    t.integer "normalized_item_id"
    t.bigint "brand_id"
    t.boolean "is_popular", default: false
    t.jsonb "open_ai_response"
    t.jsonb "open_ai_cat_response"
    t.decimal "innovation_score"
    t.string "innovation_reason"
    t.datetime "pinecone_at"
    t.boolean "is_fusion"
    t.decimal "fusion_score"
    t.string "fusion_reason"
    t.integer "restaurant_chain_count"
    t.bigint "recent_menu_id"
    t.integer "image_id"
    t.string "currency"
    t.boolean "searchable", default: false
    t.string "search_uniq_key"
    t.jsonb "gpt_ingredients"
    t.string "item_category_name"
    t.integer "generalized_item_id"
    t.datetime "generalized_item_at"
    t.integer "standard_menu_item_id"
    t.vector "embedding", limit: 1536
    t.boolean "is_soda", default: false
    t.boolean "is_wine", default: false
    t.boolean "is_beer", default: false
    t.integer "beverage_id"
    t.boolean "is_spirit"
    t.string "wine_category"
    t.string "spirit_category"
    t.integer "beverage_year"
    t.string "beverage_region"
    t.datetime "wine_at"
    t.string "wine_name"
    t.string "beverage_brand"
    t.string "og_category"
    t.string "og_name"
    t.string "og_description"
    t.boolean "is_translated", default: false
    t.string "detect_category"
    t.boolean "is_chicken", default: false
    t.boolean "is_foi_gras", default: false
    t.boolean "is_fish", default: false
    t.boolean "is_ahi", default: false
    t.string "type"
    t.integer "chain_id"
    t.integer "chain_menu_id"
    t.text "menu_image_url"
    t.text "image_url"
    t.datetime "brands_at"
    t.datetime "innovation_score_at"
    t.integer "menu_item_id"
    t.index "((to_tsvector('english'::regconfig, COALESCE((name)::text, ''::text)) || to_tsvector('english'::regconfig, COALESCE(description, ''::text))))", name: "items_pg_search_substrings_idx", using: :gin
    t.index "((to_tsvector('simple'::regconfig, COALESCE((name)::text, ''::text)) || to_tsvector('simple'::regconfig, COALESCE(description, ''::text))))", name: "items_pg_search_simple_idx", using: :gin
    t.index ["brand_id"], name: "index_items_on_brand_id"
    t.index ["chain_id", "chain_menu_id"], name: "index_items_on_chain_id_and_chain_menu_id"
    t.index ["embedding"], name: "index_items_on_embedding", opclass: :vector_cosine_ops, using: :hnsw
    t.index ["innovation_score"], name: "index_items_on_innovation_score"
    t.index ["menu_id"], name: "index_items_on_menu_id"
    t.index ["menu_item_id"], name: "index_items_on_menu_item_id"
    t.index ["normalized_item_id"], name: "index_items_on_normalized_item_id"
    t.index ["recent_menu_id"], name: "index_items_on_recent_menu_id"
    t.index ["search_uniq_key"], name: "index_items_on_search_uniq_key"
    t.index ["standard_menu_item_id"], name: "index_items_on_standard_menu_item_id"
  end

  create_table "jwt_denylist", force: :cascade do |t|
    t.string "jti"
    t.datetime "exp"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["jti"], name: "index_jwt_denylist_on_jti"
  end

  create_table "lead_items", force: :cascade do |t|
    t.string "name"
    t.integer "zero_to_10"
    t.integer "ten_to_20"
    t.integer "twenty_to_30"
    t.integer "over_30"
    t.string "course_category"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "thirty_to_40"
    t.integer "over_40"
    t.integer "zero_to_20"
    t.integer "color_weight"
    t.integer "customer_id"
  end

  create_table "lead_scoring_item_lists", force: :cascade do |t|
    t.integer "customer_id"
    t.string "watch_list", default: [], array: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "lead_scoring_report_items", force: :cascade do |t|
    t.integer "lead_scoring_report_id"
    t.integer "restaurant_id"
    t.integer "item_id"
    t.integer "score"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "course_category"
    t.boolean "is_blacklist", default: false
    t.string "blacklist_names", array: true
    t.index ["item_id", "lead_scoring_report_id", "course_category"], name: "idx_on_item_id_lead_scoring_report_id_course_catego_07c2dae5f2"
  end

  create_table "lead_scoring_reports", force: :cascade do |t|
    t.integer "restaurant_id"
    t.integer "score"
    t.text "report_url"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "total_score"
    t.integer "menu_id"
    t.uuid "guid"
    t.integer "customer_id"
    t.string "type"
    t.integer "subtotal", default: 0
    t.integer "bonus", default: 0
    t.float "bonus_percent", default: 0.0
    t.string "cuisine_types", default: [], array: true
    t.index ["menu_id", "customer_id"], name: "index_lead_scoring_reports_on_menu_id_and_customer_id", unique: true
  end

  create_table "limited_time_offers", force: :cascade do |t|
    t.string "name"
    t.date "start_date"
    t.date "end_date"
    t.string "url"
    t.bigint "item_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "chain_id"
    t.string "cuisine"
    t.string "offer_type"
    t.text "img_url"
    t.text "offer_url"
    t.text "description"
    t.string "date"
    t.string "remarks"
    t.string "meal_type"
    t.string "business_name"
    t.string "lto_type"
    t.string "tags", default: [], array: true
    t.index ["item_id"], name: "index_limited_time_offers_on_item_id"
    t.index ["name", "offer_url"], name: "index_limited_time_offers_on_name_and_offer_url"
  end

  create_table "log_events", force: :cascade do |t|
    t.text "name"
    t.text "description"
    t.string "ip_address"
    t.text "request_params"
    t.text "request_body"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "restaurant_guid"
    t.string "cw_customer_id"
    t.integer "restaurant_id"
    t.integer "ch_w_customer_id"
  end

  create_table "menu_categories", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_menu_categories_on_name"
  end

  create_table "menu_category_boards", force: :cascade do |t|
    t.string "name"
    t.string "description"
    t.string "growth"
    t.integer "google_trends"
    t.string "notification"
    t.integer "customer_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.float "weighted_average_growth"
    t.integer "new_ltos_count", default: 0
  end

  create_table "menu_item_dashboard_projects", force: :cascade do |t|
    t.integer "project_id"
    t.integer "menu_item_dashboard_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "menu_item_dashboards", force: :cascade do |t|
    t.string "name"
    t.string "query"
    t.string "description"
    t.boolean "enabled", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "hover_attributes", default: [], array: true
    t.string "state", default: "itemized"
    t.text "cover_image_url_0"
    t.text "cover_image_url_1"
    t.text "cover_image_url_2"
    t.string "source"
    t.string "source_url"
    t.string "top_cuisine"
    t.bigint "customer_id"
    t.boolean "demo", default: false
    t.integer "standard_menu_item_id"
    t.string "assign_state"
    t.string "s3_file_name"
    t.string "meal_type"
    t.string "cuisine_type"
    t.integer "ingredient_id"
    t.string "type"
    t.uuid "guid"
    t.string "flavor_profile"
    t.float "average_cost_strip"
    t.boolean "public"
    t.string "public_token"
    t.decimal "last_trend_value"
    t.string "survey_type"
    t.string "approved_topics", default: [], array: true
    t.integer "menu_subcategory_board_id"
    t.integer "menu_category_board_id"
    t.integer "menu_mentions_count"
    t.text "other_search_terms", default: [], array: true
    t.boolean "is_new_complete", default: false
    t.float "pen_rate"
    t.boolean "display_pricing_matrix", default: false
    t.text "ai_summary"
    t.text "topics", array: true
    t.float "positive_percentage"
    t.float "negative_percentage"
    t.float "neutral_percentage"
    t.float "yearly_growth"
    t.float "qsr_penetration"
    t.float "fast_casual_penetration"
    t.float "mid_scale_penetration"
    t.float "fine_dining_penetration"
    t.vector "embedding", limit: 1536
    t.integer "menu_item_id"
    t.string "menu_adoption"
    t.float "delta_last_period"
    t.index ["customer_id"], name: "index_menu_item_dashboards_on_customer_id"
    t.index ["embedding"], name: "index_menu_item_dashboards_on_embedding", opclass: :vector_cosine_ops, using: :hnsw
  end

  create_table "menu_items", force: :cascade do |t|
    t.string "name"
    t.string "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.uuid "guid"
    t.string "category"
    t.string "subcategory"
    t.string "gmi_includes", default: [], array: true
    t.string "gmi_excludes", default: [], array: true
    t.integer "gmi_category_id"
    t.integer "gmi_subcategory_id"
    t.float "pen_rate"
    t.float "foodservice_growth"
    t.float "foodservice_prediction"
    t.integer "menu_mention_count"
    t.string "appearance"
    t.string "taste"
    t.string "aroma"
    t.string "texture"
    t.string "menu_adoption"
    t.float "google_trend", default: 0.0
  end

  create_table "menu_mention_data_rows", force: :cascade do |t|
    t.string "time_period_name"
    t.integer "time_period_year"
    t.integer "time_period_quarter"
    t.integer "items_count"
    t.integer "month"
    t.integer "day"
    t.integer "year"
    t.integer "quarter"
    t.integer "menu_item_dashboard_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "menu_mentions_region_data", force: :cascade do |t|
    t.bigint "menu_item_dashboard_id"
    t.bigint "region_id"
    t.integer "menu_mentions_count"
    t.integer "chain_count"
    t.integer "independent_count"
    t.decimal "change", precision: 5, scale: 2
    t.string "location_key"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "menu_mentions_chart_data", default: [], null: false, array: true
    t.jsonb "top_cuisines_chart_data", default: [], null: false, array: true
    t.jsonb "top_regions_chart_data", default: [], null: false, array: true
    t.jsonb "top_chains_chart_data", default: [], null: false, array: true
    t.index ["menu_item_dashboard_id"], name: "index_menu_mentions_region_data_on_menu_item_dashboard_id"
    t.index ["region_id"], name: "index_menu_mentions_region_data_on_region_id"
  end

  create_table "menu_subcategory_boards", force: :cascade do |t|
    t.string "name"
    t.string "description"
    t.string "growth"
    t.integer "google_trends"
    t.integer "customer_id"
    t.integer "menu_category_board_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "menu_surveys", force: :cascade do |t|
    t.integer "menu_id"
    t.integer "lead_scoring_report_id"
    t.float "entree_high_price"
    t.float "entree_low_price"
    t.float "ahi_entree_price"
    t.float "ahi_appetizer_price"
    t.float "salmon_entree_price"
    t.float "scallop_entree_price"
    t.float "scallop_appetizer_price"
    t.integer "fish_appetizers_count"
    t.integer "fish_entrees_count"
    t.boolean "is_chicken", default: false
    t.boolean "is_foi_gras", default: false
    t.boolean "is_ahi", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.float "tuna_appetizer_price"
    t.float "tuna_entree_price"
  end

  create_table "menu_versions", force: :cascade do |t|
    t.integer "menu_id"
    t.string "state"
    t.json "menu_data"
    t.string "event"
    t.integer "mturk_hit_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "menus", force: :cascade do |t|
    t.string "name"
    t.integer "restaurant_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "aasm_state"
    t.string "state"
    t.string "category"
    t.string "failure_reason"
    t.integer "quarter"
    t.integer "year"
    t.text "menu_url"
    t.integer "related_menu_ids", array: true
    t.datetime "approved_at"
    t.string "approved_by"
    t.string "flagged_by"
    t.datetime "flagged_at"
    t.datetime "reposted_at"
    t.string "reposted_by"
    t.integer "scrap_count", default: 0
    t.string "scrapped_by"
    t.datetime "scrapped_at"
    t.datetime "pinecone_at"
    t.datetime "innovation_score_at"
    t.datetime "categorization_at"
    t.datetime "fusion_score_at"
    t.datetime "ingred_brand_at"
    t.integer "scrap_retry_count", default: 0
    t.datetime "gpt_ingredients_at"
    t.datetime "wine_at"
    t.string "type"
    t.integer "chain_id"
    t.integer "original_menu_id"
    t.uuid "guid"
    t.boolean "is_scraping", default: false
    t.datetime "pulse_at"
    t.datetime "es_index_at"
    t.index ["chain_id"], name: "index_menus_on_chain_id"
    t.index ["id"], name: "index_menus_on_id"
    t.index ["quarter", "year"], name: "index_menus_on_quarter_and_year"
    t.index ["restaurant_id"], name: "index_menus_on_restaurant_id"
    t.index ["state"], name: "index_menus_on_state"
  end

  create_table "michelin_restaurants", force: :cascade do |t|
    t.string "business_name"
    t.integer "stars"
    t.boolean "is_new"
    t.string "address"
    t.string "city_name"
    t.string "state_name"
    t.string "zip"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "restaurant_id"
  end

  create_table "mturk_hits", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "hit_id"
    t.string "hit_type"
    t.string "hit_url"
    t.boolean "sandbox"
    t.string "ref_type"
    t.integer "ref_id"
    t.string "ref_status"
    t.integer "assignment_count"
    t.integer "base_pay"
    t.datetime "expired_at", precision: nil
    t.datetime "rejected_at", precision: nil
    t.integer "tik_tok_video_id"
    t.index ["ref_id"], name: "index_mturk_hits_on_ref_id"
    t.index ["tik_tok_video_id"], name: "index_mturk_hits_on_tik_tok_video_id"
  end

  create_table "mturk_menu_results", force: :cascade do |t|
    t.integer "mturk_hit_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.decimal "price"
    t.string "category"
    t.string "name"
    t.string "description"
    t.string "worker_id"
    t.string "assignment_id"
    t.string "hit_id"
    t.integer "menu_id"
    t.integer "mturk_worker_id"
    t.index ["mturk_hit_id"], name: "index_mturk_menu_results_on_mturk_hit_id"
  end

  create_table "mturk_tik_tok_results", force: :cascade do |t|
    t.integer "mturk_hit_id"
    t.integer "views_count"
    t.integer "comments_count"
    t.string "video_type"
    t.string "item_name"
    t.string "sentiment"
    t.string "brands", default: [], array: true
    t.integer "tik_tok_video_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "assignment_id"
    t.string "hit_id"
    t.string "worker_id"
    t.integer "mturk_worker_id"
    t.integer "likes_count"
  end

  create_table "mturk_workers", force: :cascade do |t|
    t.string "worker_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.boolean "is_banned"
    t.text "ban_reason"
  end

  create_table "news", force: :cascade do |t|
    t.string "name"
    t.text "url"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "news_articles", force: :cascade do |t|
    t.string "title"
    t.string "url"
    t.string "description"
    t.text "post_text"
    t.bigint "news_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "vectara_uploaded", default: false
    t.string "scrap_page_url"
    t.index ["news_id"], name: "index_news_articles_on_news_id"
  end

  create_table "normalized_items", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "name"
    t.integer "item_category_id"
    t.integer "item_subcategory_id"
    t.index ["item_category_id"], name: "index_normalized_items_on_item_category_id"
  end

  create_table "open_ai_cost_logs", force: :cascade do |t|
    t.integer "prompt_sum"
    t.integer "completion_sum"
    t.integer "project_id"
    t.string "model_version"
    t.string "activity_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "open_ai_logs", force: :cascade do |t|
    t.text "prompt"
    t.json "response"
    t.string "klass"
    t.integer "item_id"
    t.integer "report_category_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "yelp_image_id"
    t.string "model_version"
    t.integer "prompt_tokens"
    t.integer "completion_tokens"
    t.integer "total_tokens"
    t.integer "report_category_item_id"
    t.integer "product_id"
    t.integer "gpt_ingredient_id"
    t.string "service_type"
    t.string "ext_log_id"
    t.integer "restaurant_id"
    t.integer "instagram_comment_id"
    t.index ["item_id"], name: "index_open_ai_logs_on_item_id"
  end

  create_table "popular_search_queries", force: :cascade do |t|
    t.string "product_name"
    t.integer "cw_item_id"
    t.string "single_search"
    t.string "two_word_search"
    t.text "reason"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "pricing_data_points", force: :cascade do |t|
    t.integer "product_id"
    t.decimal "price"
    t.decimal "qty"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "year"
    t.integer "month"
    t.integer "day"
    t.datetime "pull_date"
    t.integer "quarter"
    t.index ["product_id"], name: "index_pricing_data_points_on_product_id"
  end

  create_table "product_catalog_products", force: :cascade do |t|
    t.integer "product_catalog_id"
    t.integer "product_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["product_catalog_id", "product_id"], name: "idx_pcp_catalog_id_product_id"
    t.index ["product_catalog_id"], name: "idx_pcp_on_catalog_id"
    t.index ["product_id", "product_catalog_id"], name: "idx_on_product_id_product_catalog_id_80ff224cbc", unique: true
  end

  create_table "product_catalogs", force: :cascade do |t|
    t.integer "year"
    t.integer "quarter"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "retailer_id"
    t.integer "store_id"
    t.index ["retailer_id", "quarter", "year"], name: "index_product_catalogs_on_retailer_id_and_quarter_and_year"
  end

  create_table "product_categories", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "url"
    t.integer "retailer_id"
    t.integer "store_id"
    t.index "lower(TRIM(BOTH FROM name))", name: "idx_product_categories_on_lower_trim_name"
  end

  create_table "product_list_products", force: :cascade do |t|
    t.integer "product_list_id"
    t.integer "product_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.float "neighbor_distance"
    t.index ["product_list_id", "product_id"], name: "index_product_list_products_on_product_list_id_and_product_id", unique: true
  end

  create_table "product_lists", force: :cascade do |t|
    t.string "name"
    t.integer "product_ids", default: [], array: true
    t.integer "gpt_ingredient_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "retailer_id"
    t.string "list_type"
  end

  create_table "product_projects", force: :cascade do |t|
    t.integer "product_id"
    t.integer "project_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "product_subcategories", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "retailer_id"
    t.integer "store_id"
    t.text "url"
    t.boolean "is_multipage", default: false
    t.string "state"
    t.string "approved_by"
    t.datetime "approved_at"
    t.string "flagged_by"
    t.datetime "flagged_at"
    t.string "failure_reason"
    t.integer "product_category_id"
    t.boolean "is_tracking", default: false
  end

  create_table "products", force: :cascade do |t|
    t.text "upc"
    t.text "description"
    t.text "brand"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "product_category_id"
    t.integer "product_subcategory_id"
    t.integer "retailer_id"
    t.text "name"
    t.decimal "price"
    t.integer "store_id"
    t.string "slug"
    t.vector "embedding", limit: 1536
    t.string "uom"
    t.string "pack_size"
    t.boolean "is_catch_weight"
    t.float "average_catch_weight"
    t.integer "restaurant_pop_score"
    t.string "standard_ingredient_name"
    t.vector "sim_embedding", limit: 1536
    t.string "ext_item_id"
    t.string "method_of_production"
    t.string "country_of_origin"
    t.string "fda_scientific_name"
    t.text "composition"
    t.text "sku"
    t.string "week_date"
    t.jsonb "additional_fields"
    t.string "listing_url"
    t.string "l0"
    t.string "l1"
    t.string "l2"
    t.integer "year", limit: 2
    t.integer "quarter", limit: 2
    t.index "((to_tsvector('english'::regconfig, COALESCE(name, ''::text)) || to_tsvector('english'::regconfig, COALESCE(description, ''::text))))", name: "products_pg_search_substrings_idx", using: :gin
    t.index "((to_tsvector('simple'::regconfig, COALESCE(name, ''::text)) || to_tsvector('simple'::regconfig, COALESCE(description, ''::text))))", name: "products_pg_search_simple_idx", using: :gin
    t.index ["embedding"], name: "index_products_on_embedding", opclass: :vector_cosine_ops, using: :hnsw
    t.index ["l0"], name: "idx_products_l0_not_null", where: "(l0 IS NOT NULL)"
    t.index ["l0"], name: "idx_products_on_l0"
    t.index ["l1"], name: "idx_products_l1_not_null", where: "(l1 IS NOT NULL)"
    t.index ["l1"], name: "idx_products_on_l1"
    t.index ["l2"], name: "idx_products_l2_not_null", where: "(l2 IS NOT NULL)"
    t.index ["l2"], name: "idx_products_on_l2"
    t.index ["product_category_id"], name: "index_products_on_product_category_id"
    t.index ["product_subcategory_id"], name: "index_products_on_product_subcategory_id"
    t.index ["quarter"], name: "index_products_on_quarter"
    t.index ["sim_embedding"], name: "index_products_on_sim_embedding", opclass: :vector_cosine_ops, using: :hnsw
    t.index ["slug"], name: "index_products_on_slug"
    t.index ["store_id", "product_category_id"], name: "index_products_on_store_id_and_product_category_id"
    t.index ["store_id"], name: "index_products_on_store_id"
    t.index ["upc"], name: "index_products_on_upc"
    t.index ["week_date"], name: "index_products_on_week_date"
    t.index ["year", "quarter"], name: "index_products_on_year_and_quarter"
    t.index ["year"], name: "index_products_on_year"
  end

  create_table "projects", force: :cascade do |t|
    t.string "project_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "csv_url"
    t.integer "assigned_user_id"
    t.boolean "audit_complete", default: false
    t.datetime "released_at"
    t.string "key"
    t.text "note"
    t.integer "quarter"
    t.integer "year"
    t.integer "items_w_ingred_count", default: 0
    t.integer "total_items_count", default: 0
    t.float "gpt_ingred_percent", default: 0.0
    t.string "state"
    t.text "gmi_report_url"
    t.integer "gmi_count", default: 0
    t.float "gmi_percent", default: 0.0
    t.text "wine_report_url"
    t.integer "restaurants_count", default: 0
    t.integer "waiting_for_url_count", default: 0
    t.integer "scanned_count", default: 0
    t.integer "itemized_count", default: 0
    t.integer "approved_count", default: 0
    t.integer "failed_count", default: 0
    t.string "skus_report_url"
    t.integer "current_retailer_id"
    t.string "current_client_name"
    t.text "customer_sheet_url"
    t.text "scraper_sheet_url"
    t.text "live_list_csv"
    t.integer "customer_id"
    t.decimal "itemization_cost"
    t.integer "itemization_hits_count"
    t.decimal "total_mturk_cost"
    t.integer "current_menus_count"
    t.text "lead_scoring_reports_url"
  end

  create_table "pulse_logs", force: :cascade do |t|
    t.json "response"
    t.bigint "menu_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["menu_id"], name: "index_pulse_logs_on_menu_id"
  end

  create_table "quarter_products", force: :cascade do |t|
    t.integer "product_id"
    t.integer "quarter_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["product_id", "quarter_id"], name: "index_quarter_products_on_product_id_and_quarter_id", unique: true
  end

  create_table "quarterly_restaurant_lists", force: :cascade do |t|
    t.text "csv_url"
    t.integer "restaurants_ids", default: [], array: true
    t.integer "additional_ids", default: [], array: true
    t.integer "time_period_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "quarters", force: :cascade do |t|
    t.bigint "menu_item_dashboard_id"
    t.bigint "time_period_id"
    t.integer "items_count"
    t.decimal "average_price", precision: 10, scale: 2
    t.decimal "median_price", precision: 10, scale: 2
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "restaurant_count"
    t.float "percent"
    t.integer "restaurants_with_items_count"
    t.float "pen_rate"
    t.integer "menus_count"
    t.integer "retail_insights_dashboard_id"
    t.float "сalifornia_pen_rate"
    t.float "west_pen_rate"
    t.float "plains_pen_rate"
    t.float "great_lakes_pen_rate"
    t.float "south_central_pen_rate"
    t.float "south_east_pen_rate"
    t.float "mid_south_pen_rate"
    t.float "north_east_pen_rate"
    t.float "qsr_penetration"
    t.float "fast_casual_penetration"
    t.float "mid_scale_penetration"
    t.float "fine_dining_penetration"
    t.string "menu_adoption"
    t.float "delta_last_period"
    t.boolean "is_complete", default: false
    t.integer "quarterable_id"
    t.string "quarterable_type"
    t.integer "pairing_trend_count", default: 0
    t.float "pairing_trend_change", default: 0.0
    t.float "pairing_trend_percent", default: 0.0
    t.float "change_1y", default: 0.0
    t.index ["menu_item_dashboard_id"], name: "index_quarters_on_menu_item_dashboard_id"
    t.index ["quarterable_id", "quarterable_type"], name: "index_quarters_on_quarterable_id_and_quarterable_type"
    t.index ["time_period_id"], name: "index_quarters_on_time_period_id"
  end

  create_table "recipe_categories", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.vector "embedding", limit: 1536
    t.index ["embedding"], name: "index_recipe_categories_on_embedding", opclass: :vector_cosine_ops, using: :hnsw
  end

  create_table "recipe_subcategories", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "recipe_category_id"
    t.string "tree", array: true
    t.text "url"
    t.vector "embedding", limit: 1536
    t.index ["embedding"], name: "index_recipe_subcategories_on_embedding", opclass: :vector_cosine_ops, using: :hnsw
  end

  create_table "recipes", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.vector "embedding", limit: 1536
    t.integer "recipe_category_id"
    t.integer "recipe_subcategory_id"
    t.integer "rating"
    t.index ["embedding"], name: "index_recipes_on_embedding", opclass: :vector_cosine_ops, using: :hnsw
  end

  create_table "regions", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "is_default", default: false
    t.bigint "country_id"
    t.index ["country_id"], name: "index_regions_on_country_id"
  end

  create_table "replicate_logs", force: :cascade do |t|
    t.string "replicate_id"
    t.json "response"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "report_categories", force: :cascade do |t|
    t.string "name", null: false
    t.string "search_terms"
    t.jsonb "excluded_items", default: [], array: true
    t.bigint "report_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "description"
    t.index ["name"], name: "index_report_categories_on_name"
    t.index ["report_id"], name: "index_report_categories_on_report_id"
  end

  create_table "report_category_items", force: :cascade do |t|
    t.bigint "report_category_id"
    t.bigint "item_id"
    t.jsonb "open_ai_determination", default: {}
    t.float "confidence", default: 0.0
    t.boolean "is_correct"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["item_id"], name: "index_report_category_items_on_item_id"
    t.index ["report_category_id"], name: "index_report_category_items_on_report_category_id"
  end

  create_table "report_files", force: :cascade do |t|
    t.string "name"
    t.text "url"
    t.string "report_type"
    t.integer "project_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "service_type"
    t.integer "retailer_id"
    t.string "client_name"
  end

  create_table "report_items", force: :cascade do |t|
    t.bigint "report_id"
    t.bigint "item_id"
    t.jsonb "open_ai_determination", default: {}
    t.float "confidence", default: 0.0
    t.boolean "is_correct"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["item_id"], name: "index_report_items_on_item_id"
    t.index ["report_id"], name: "index_report_items_on_report_id"
  end

  create_table "report_saveds", force: :cascade do |t|
    t.string "term"
    t.string "file_name"
    t.string "file_url"
    t.integer "results_count"
    t.string "region"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "category"
    t.index ["user_id"], name: "index_report_saveds_on_user_id"
  end

  create_table "reports", force: :cascade do |t|
    t.string "name", null: false
    t.boolean "enabled", default: false, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "description"
    t.string "search_term"
    t.jsonb "advanced_options", default: {"exact_match"=>"1"}
    t.jsonb "locations_attrs", default: [], array: true
    t.enum "report_type", default: "lead_generation", null: false, enum_type: "report_types"
    t.jsonb "show_prices", default: [], array: true
    t.text "customer_description"
    t.index ["name"], name: "index_reports_on_name"
  end

  create_table "restaurant_categories", force: :cascade do |t|
    t.integer "restaurant_id"
    t.integer "category_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["restaurant_id", "category_id"], name: "index_restaurant_categories_on_restaurant_id_and_category_id"
  end

  create_table "restaurant_projects", force: :cascade do |t|
    t.integer "restaurant_id"
    t.integer "project_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "restaurant_versions", force: :cascade do |t|
    t.string "event"
    t.json "restaurant_data"
    t.integer "restaurant_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "restaurants", force: :cascade do |t|
    t.text "content"
    t.integer "user_id"
    t.text "address"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "business_name"
    t.integer "city_id"
    t.text "url"
    t.integer "business_search_num"
    t.string "yelp_categories", default: [], array: true
    t.string "city_name"
    t.string "zip_code"
    t.string "country"
    t.string "state_name"
    t.string "display_address", default: [], array: true
    t.float "rating"
    t.text "image_url"
    t.string "alias"
    t.integer "review_count"
    t.text "menu_url"
    t.integer "chain_id"
    t.integer "city_location_count"
    t.string "price"
    t.string "doordash_url"
    t.string "url_by"
    t.string "contact_email_address"
    t.string "domain"
    t.boolean "pull_quarterly", default: false
    t.string "uber_eats_url"
    t.string "open_table_url"
    t.string "grubhub_url"
    t.string "all_menus_url"
    t.float "rating_value"
    t.float "best_rating"
    t.integer "rating_count"
    t.string "phone"
    t.string "cw_customer_id"
    t.string "toast_guid"
    t.text "toast_url"
    t.text "error_reason"
    t.string "scrap_loc_url"
    t.integer "ch_w_customer_id"
    t.string "big_6_url"
    t.string "quick_cart_url"
    t.uuid "guid"
    t.string "krave_url"
    t.integer "michelin_stars"
    t.boolean "is_michelin"
    t.string "external_id"
    t.string "hf_id"
    t.text "address_2"
    t.boolean "ran_google_yelp", default: false
    t.boolean "is_translated", default: false
    t.string "og_business_name"
    t.integer "menus_count", default: 0
    t.datetime "opening_date"
    t.integer "months_open", default: 0
    t.string "restaurant_type"
    t.boolean "quarterly_sample", default: false
    t.index ["business_name"], name: "index_restaurants_on_business_name"
    t.index ["chain_id"], name: "index_restaurants_on_chain_id"
    t.index ["city_id"], name: "index_restaurants_on_city_id"
    t.index ["pull_quarterly"], name: "index_restaurants_on_pull_quarterly"
    t.index ["restaurant_type"], name: "index_restaurants_on_restaurant_type"
  end

  create_table "retail_insights_dashboards", force: :cascade do |t|
    t.string "name"
    t.string "query"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.float "growth_percent"
    t.integer "ingredient_id"
  end

  create_table "retailers", force: :cascade do |t|
    t.string "name"
    t.text "base_uri"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "products_count"
    t.string "scrapper_type"
    t.boolean "customer_enabled", default: false
    t.string "source"
    t.boolean "dashboard_enabled", default: false
  end

  create_table "roles", force: :cascade do |t|
    t.string "name"
    t.string "resource_type"
    t.bigint "resource_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["name", "resource_type", "resource_id"], name: "index_roles_on_name_and_resource_type_and_resource_id"
    t.index ["resource_type", "resource_id"], name: "index_roles_on_resource_type_and_resource_id"
  end

  create_table "scrapper_result_groups", force: :cascade do |t|
    t.integer "product_subcategory_id"
    t.datetime "sync_date"
    t.integer "day"
    t.integer "month"
    t.integer "year"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "scrapper_results", force: :cascade do |t|
    t.json "response"
    t.integer "product_subcategory_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "retailer_id"
    t.integer "page_number"
    t.text "url"
    t.string "klass"
    t.string "action"
    t.integer "restaurant_id"
    t.datetime "sync_date"
    t.integer "year"
    t.integer "month"
    t.integer "day"
    t.integer "scrapper_result_group_id"
    t.string "yelp_category"
    t.string "scrapper_type", default: "menu"
  end

  create_table "search_queries", force: :cascade do |t|
    t.string "query"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["query"], name: "index_search_queries_on_query", unique: true
  end

  create_table "signups", force: :cascade do |t|
    t.string "name"
    t.string "email"
    t.text "notes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "ip_address"
  end

  create_table "social_conversations", force: :cascade do |t|
    t.integer "menu_item_dashboard_id"
    t.integer "month"
    t.integer "year"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "num_conversations"
  end

  create_table "standard_menu_items", force: :cascade do |t|
    t.string "name"
    t.integer "generalized_item_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "items_count", default: 0
    t.vector "embedding", limit: 1536
    t.integer "recipe_id"
    t.index ["embedding"], name: "index_standard_menu_items_on_embedding", opclass: :vector_cosine_ops, using: :hnsw
  end

  create_table "states", force: :cascade do |t|
    t.string "abbrev"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "name"
    t.integer "country_id"
    t.bigint "region_id"
    t.index ["region_id"], name: "index_states_on_region_id"
  end

  create_table "stores", force: :cascade do |t|
    t.string "address"
    t.string "city"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "retailer_id"
    t.string "name"
    t.string "external_id"
    t.string "zip_code"
    t.string "state_name"
    t.string "phone"
    t.float "latitude"
    t.float "longitude"
    t.string "publix_shop_id"
    t.string "publix_loc_id"
    t.integer "store_number"
    t.boolean "customer_enabled", default: false
  end

  create_table "tik_tok_trend_projects", force: :cascade do |t|
    t.integer "project_id"
    t.integer "tik_tok_trend_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "tik_tok_trends", force: :cascade do |t|
    t.string "hash_tag"
    t.integer "views_count"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "tik_tok_videos", force: :cascade do |t|
    t.text "url"
    t.integer "views_count"
    t.integer "comments_count"
    t.string "video_type"
    t.string "item_name"
    t.string "sentiment"
    t.string "brands", default: [], array: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "state"
    t.integer "tik_tok_trend_id"
    t.string "approved_by"
    t.datetime "approved_at"
    t.string "reposted_by"
    t.datetime "reposted_at"
    t.string "failed_by"
    t.datetime "failed_at"
    t.datetime "flagged_at"
    t.string "flagged_by"
    t.string "title"
    t.json "embed_data"
    t.integer "likes_count"
  end

  create_table "time_periods", force: :cascade do |t|
    t.integer "quarter"
    t.integer "year"
    t.boolean "is_approved"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "name"
    t.datetime "start_at"
    t.datetime "end_at"
    t.string "time_period_type"
    t.text "description"
    t.index ["end_at"], name: "index_time_periods_on_end_at"
    t.index ["start_at"], name: "index_time_periods_on_start_at"
    t.index ["time_period_type"], name: "index_time_periods_on_time_period_type"
  end

  create_table "top_chains_data_rows", force: :cascade do |t|
    t.integer "menu_item_dashboard_id"
    t.integer "chain_id"
    t.integer "size"
    t.integer "items_count"
    t.float "average_price"
    t.integer "month"
    t.integer "day"
    t.integer "year"
    t.integer "quarter"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "row_type"
    t.index ["chain_id", "menu_item_dashboard_id"], name: "idx_on_chain_id_menu_item_dashboard_id_3c02ea8c1e"
  end

  create_table "users", force: :cascade do |t|
    t.string "name"
    t.string "email"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at", precision: nil
    t.datetime "remember_created_at", precision: nil
    t.integer "customer_id"
    t.jsonb "search_options", default: {"exact_match"=>"1"}
    t.boolean "seen_on_boarding_modal", default: false
    t.string "added_by"
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
  end

  create_table "users_roles", id: false, force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "role_id"
    t.index ["role_id"], name: "index_users_roles_on_role_id"
    t.index ["user_id", "role_id"], name: "index_users_roles_on_user_id_and_role_id"
    t.index ["user_id"], name: "index_users_roles_on_user_id"
  end

  create_table "weekly_cost_statistics", force: :cascade do |t|
    t.string "week_ending"
    t.datetime "week_ending_datetime"
    t.decimal "itemization_cost"
    t.integer "itemization_hits_count"
    t.decimal "total_cost"
    t.integer "menus_count"
    t.integer "reposts_count"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "week_starting"
    t.datetime "week_starting_datetime"
  end

  create_table "widget_data", force: :cascade do |t|
    t.string "widget_type"
    t.json "data"
    t.integer "retail_insights_dashboard_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "yelp_image_projects", force: :cascade do |t|
    t.integer "yelp_image_id"
    t.integer "project_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "yelp_images", force: :cascade do |t|
    t.text "src"
    t.integer "width"
    t.integer "height"
    t.integer "restaurant_id"
    t.text "alt"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "item_id"
    t.string "state"
    t.string "yelp_photo_id"
    t.string "yelp_date"
    t.datetime "yelp_datetime"
    t.string "approved_by"
    t.datetime "approved_at"
    t.string "flagged_by"
    t.datetime "flagged_at"
    t.string "failure_reason"
    t.string "yelp_category"
    t.index ["item_id"], name: "index_yelp_images_on_item_id"
    t.index ["restaurant_id"], name: "index_yelp_images_on_restaurant_id"
  end

  create_table "zip_codes", force: :cascade do |t|
    t.string "code"
    t.bigint "city_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "lat"
    t.string "lng"
    t.index ["city_id", "code"], name: "index_zip_codes_on_city_id_and_code", unique: true
    t.index ["city_id"], name: "index_zip_codes_on_city_id"
  end

  add_foreign_key "access_locations", "customers"
  add_foreign_key "articles", "brands"
  add_foreign_key "batch_ai_operations", "open_ai_logs"
  add_foreign_key "batch_assignments", "batch_ai_operations"
  add_foreign_key "category_item_categories", "categories"
  add_foreign_key "category_item_categories", "item_categories"
  add_foreign_key "chains", "cities"
  add_foreign_key "cities", "counties"
  add_foreign_key "city_regions", "cities"
  add_foreign_key "city_regions", "regions"
  add_foreign_key "consumer_sentiment_familiarities", "ingredients"
  add_foreign_key "consumer_sentiment_likes", "ingredients"
  add_foreign_key "counties", "states"
  add_foreign_key "customer_reports", "customers"
  add_foreign_key "customer_reports", "reports"
  add_foreign_key "flavor_ingredients", "flavors"
  add_foreign_key "flavor_ingredients", "ingredients"
  add_foreign_key "geo_prices", "chains"
  add_foreign_key "geo_prices", "menu_item_dashboards"
  add_foreign_key "google_trends", "ingredients"
  add_foreign_key "google_trends", "menu_category_boards"
  add_foreign_key "google_trends", "menu_item_dashboards"
  add_foreign_key "ingredient_age_breakdowns", "ingredients"
  add_foreign_key "ingredient_dashboards", "ingredients"
  add_foreign_key "ingredient_dashboards", "menu_item_dashboards"
  add_foreign_key "ingredient_ethnicity_breakdowns", "ingredients"
  add_foreign_key "instagram_comments", "instagram_posts"
  add_foreign_key "instagram_posts", "chains"
  add_foreign_key "instagram_posts", "instagram_users"
  add_foreign_key "instagram_users", "chains"
  add_foreign_key "item_brands", "brands"
  add_foreign_key "item_brands", "items"
  add_foreign_key "item_dashboards", "items"
  add_foreign_key "item_dashboards", "menu_item_dashboards"
  add_foreign_key "item_flavors", "flavors"
  add_foreign_key "item_flavors", "items"
  add_foreign_key "item_quarters", "items"
  add_foreign_key "item_quarters", "quarters"
  add_foreign_key "items", "brands"
  add_foreign_key "items", "menus", column: "recent_menu_id"
  add_foreign_key "limited_time_offers", "items"
  add_foreign_key "menu_item_dashboards", "customers"
  add_foreign_key "menu_mentions_region_data", "menu_item_dashboards"
  add_foreign_key "menu_mentions_region_data", "regions"
  add_foreign_key "news_articles", "news"
  add_foreign_key "pulse_logs", "menus"
  add_foreign_key "quarters", "menu_item_dashboards"
  add_foreign_key "quarters", "time_periods"
  add_foreign_key "regions", "countries"
  add_foreign_key "report_categories", "reports"
  add_foreign_key "report_category_items", "items"
  add_foreign_key "report_category_items", "report_categories"
  add_foreign_key "report_items", "items"
  add_foreign_key "report_items", "reports"
  add_foreign_key "report_saveds", "users"
  add_foreign_key "states", "regions"
  add_foreign_key "zip_codes", "cities"

  create_view "products_l_levels_counts_by_quarters", sql_definition: <<-SQL
      WITH producthierarchy AS (
           SELECT 'l0'::text AS level,
              p.l0 AS name,
              NULL::character varying AS parent_l0,
              NULL::text AS parent_l1,
              pc.year,
              pc.quarter,
              count(*) AS count
             FROM ((products p
               JOIN product_catalog_products pcp ON ((pcp.product_id = p.id)))
               JOIN product_catalogs pc ON ((pc.id = pcp.product_catalog_id)))
            WHERE (p.l0 IS NOT NULL)
            GROUP BY pc.year, pc.quarter, p.l0
          UNION ALL
           SELECT 'l1'::text AS level,
              p.l1 AS name,
              p.l0 AS parent_l0,
              NULL::text AS parent_l1,
              pc.year,
              pc.quarter,
              count(*) AS count
             FROM ((products p
               JOIN product_catalog_products pcp ON ((pcp.product_id = p.id)))
               JOIN product_catalogs pc ON ((pc.id = pcp.product_catalog_id)))
            WHERE (p.l1 IS NOT NULL)
            GROUP BY pc.year, pc.quarter, p.l0, p.l1
          UNION ALL
           SELECT 'l2'::text AS level,
              p.l2 AS name,
              p.l0 AS parent_l0,
              p.l1 AS parent_l1,
              pc.year,
              pc.quarter,
              count(*) AS count
             FROM ((products p
               JOIN product_catalog_products pcp ON ((pcp.product_id = p.id)))
               JOIN product_catalogs pc ON ((pc.id = pcp.product_catalog_id)))
            WHERE (p.l2 IS NOT NULL)
            GROUP BY pc.year, pc.quarter, p.l0, p.l1, p.l2
          )
   SELECT level,
      name,
      parent_l0,
      parent_l1,
      year,
      quarter,
      count
     FROM producthierarchy
    ORDER BY COALESCE(parent_l0, name), parent_l1, level, name, year, quarter;
  SQL
end
