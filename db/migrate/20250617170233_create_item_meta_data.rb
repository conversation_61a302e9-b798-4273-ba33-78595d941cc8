class CreateItemMetaData < ActiveRecord::Migration[7.1]
  def change
    create_table :item_meta_data do |t|
      t.integer :item_id
      t.float :score
      t.string :cuisine_type, array: true, default: []
      t.string :day_part, array: true, default: []
      t.string :menu_type, array: true, default: []
      t.string :dietary_tags, array: true, default: []
      t.string :taste_profile, array: true, default: []
      t.string :texture, array: true, default: []
      t.string :prep_methods, array: true, default: []
      t.string :category
      t.string :sub_category

      t.timestamps
    end
  end
end
