class AddIngredientEthnicityBreakdowns < ActiveRecord::Migration[7.1]
  def change
    create_table :ingredient_ethnicity_breakdowns do |t|
      t.references :ingredient, null: false, foreign_key: true
      t.float :asian_likes_score, null: false, default: 0.0
      t.float :asian_familiarity, null: false, default: 0.0
      t.float :black_likes_score, null: false, default: 0.0
      t.float :black_familiarity, null: false, default: 0.0
      t.float :white_likes_score, null: false, default: 0.0
      t.float :white_familiarity, null: false, default: 0.0
      t.float :mixed_likes_score, null: false, default: 0.0
      t.float :mixed_familiarity, null: false, default: 0.0
      t.float :other_likes_score, null: false, default: 0.0
      t.float :other_familiarity, null: false, default: 0.0

      t.timestamps
    end
  end
end
