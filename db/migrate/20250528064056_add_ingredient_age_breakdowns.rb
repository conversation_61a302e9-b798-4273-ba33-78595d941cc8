class AddIngredientAgeBreakdowns < ActiveRecord::Migration[7.1]
  def change
    create_table :ingredient_age_breakdowns do |t|
      t.references :ingredient, null: false, foreign_key: true
      t.float :age_65_likes_score, null: false, default: 0.0
      t.float :age_65_familiarity, null: false, default: 0.0
      t.float :age_55_64_likes_score, null: false, default: 0.0
      t.float :age_55_64_familiarity, null: false, default: 0.0
      t.float :age_45_54_likes_score, null: false, default: 0.0
      t.float :age_45_54_familiarity, null: false, default: 0.0
      t.float :age_35_44_likes_score, null: false, default: 0.0
      t.float :age_35_44_familiarity, null: false, default: 0.0
      t.float :age_25_34_likes_score, null: false, default: 0.0
      t.float :age_25_34_familiarity, null: false, default: 0.0
      t.float :age_18_24_likes_score, null: false, default: 0.0
      t.float :age_18_24_familiarity, null: false, default: 0.0

      t.timestamps
    end
  end
end
