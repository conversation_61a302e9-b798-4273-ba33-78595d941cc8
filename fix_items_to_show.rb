#!/usr/bin/env ruby

# Script to fix items_to_show by properly populating quarter items
# Run in Rails console: load 'fix_items_to_show.rb'

puts "🔧 Fix items_to_show for MenuItemDashboards"
puts "=" * 50

# Configuration
INGREDIENT_NAME = 'spaghett'  # Change this to your ingredient
DRY_RUN = false  # Set to true to see what would happen

puts "\n📋 Configuration:"
puts "- Ingredient: #{INGREDIENT_NAME}"
puts "- Dry run: #{DRY_RUN}"

def fix_items_for_dashboard(ingredient_name, dry_run: false)
  puts "\n🔧 Fixing items for: #{ingredient_name.upcase}"
  puts "=" * 40
  
  # Step 1: Find ingredient and dashboard
  ingredient = Ingredient.where(name: ingredient_name).first
  if ingredient.nil?
    puts "❌ Ingredient not found"
    return false
  end
  
  dashboard = ingredient.menu_item_dashboard
  if dashboard.nil?
    puts "❌ No dashboard found"
    return false
  end
  
  puts "✅ Found dashboard: #{dashboard.name} (ID: #{dashboard.id})"
  puts "   Query: '#{dashboard.query}'"
  
  # Step 2: Check current state
  puts "\n📊 Current State:"
  puts "   Quarters: #{dashboard.quarters.count}"
  puts "   items_to_show: #{dashboard.items_to_show.count}"
  
  if dashboard.quarters.empty?
    puts "❌ No quarters found! Need to run assign_items first"
    return false
  end
  
  # Step 3: Check what items should be found
  puts "\n🔍 Searching for items with query: '#{dashboard.query}'"
  
  # Search for items using PostgreSQL (similar to what assign_items should do)
  query = dashboard.query.downcase
  
  # Get time periods for the quarters
  time_periods = dashboard.quarters.joins(:time_period).pluck('time_periods.year', 'time_periods.quarter')
  puts "   Time periods: #{time_periods.map { |y, q| "#{y} Q#{q}" }.join(', ')}"
  
  total_found_items = 0
  
  dashboard.quarters.joins(:time_period).order('time_periods.year, time_periods.quarter').each do |quarter|
    tp = quarter.time_period
    puts "\n   📅 #{tp.name}:"
    
    # Search for items in this quarter/year that match the query
    matching_items = Item.joins(:menu)
                         .where(menus: { quarter: tp.quarter, year: tp.year, state: 'approved' })
                         .where("LOWER(items.name) LIKE ? OR LOWER(items.description) LIKE ?", 
                                "%#{query}%", "%#{query}%")
    
    current_items = quarter.items.count
    found_items = matching_items.count
    
    puts "     Current items in quarter: #{current_items}"
    puts "     Found matching items: #{found_items}"
    
    if found_items > 0
      puts "     Sample matches:"
      matching_items.limit(3).each do |item|
        restaurant_name = item.menu.restaurant.name rescue 'N/A'
        puts "       - #{item.name} (#{restaurant_name})"
      end
      
      # Add items to quarter if not in dry run mode
      if !dry_run && current_items == 0
        puts "     🔧 Adding #{found_items} items to quarter..."
        
        begin
          # Clear existing items first
          quarter.items.clear
          
          # Add the matching items
          quarter.items = matching_items
          quarter.save!
          
          puts "     ✅ Added #{quarter.items.count} items to quarter"
          total_found_items += quarter.items.count
          
        rescue => e
          puts "     ❌ Error adding items: #{e.message}"
        end
      elsif current_items > 0
        puts "     ✅ Quarter already has items"
        total_found_items += current_items
      else
        puts "     🔍 DRY RUN: Would add #{found_items} items"
        total_found_items += found_items
      end
    else
      puts "     ⚠️  No matching items found for this quarter"
    end
  end
  
  # Step 4: Verify results
  puts "\n📊 Results:"
  if dry_run
    puts "   🔍 DRY RUN: Would have #{total_found_items} total items"
  else
    dashboard.reload
    new_items_count = dashboard.items_to_show.count
    puts "   ✅ items_to_show now: #{new_items_count}"
    
    if new_items_count > 0
      puts "   🎉 SUCCESS! Charts should now work"
      
      # Test top chains
      begin
        top_chains_count = dashboard.top_chains.limit(5).count
        puts "   📊 Top chains available: #{top_chains_count}"
        
        if top_chains_count > 0
          puts "   🏪 Sample chains:"
          dashboard.top_chains.limit(3).each do |chain|
            puts "     - #{chain.name}"
          end
        end
      rescue => e
        puts "   ⚠️  Error testing top chains: #{e.message}"
      end
    else
      puts "   ❌ Still no items_to_show. Check if:"
      puts "     1. Items exist in the database for these time periods"
      puts "     2. Query '#{dashboard.query}' matches item names/descriptions"
      puts "     3. Menus are marked as 'approved'"
    end
  end
  
  true
end

# Helper function to check database state
def check_database_state
  puts "\n🔍 Database State Check:"
  
  # Check if we have items at all
  total_items = Item.count
  puts "   Total items in database: #{total_items}"
  
  # Check if we have menus
  total_menus = Menu.count
  approved_menus = Menu.where(state: 'approved').count
  puts "   Total menus: #{total_menus}"
  puts "   Approved menus: #{approved_menus}"
  
  # Check time periods
  time_periods = TimePeriod.last_year
  puts "   Time periods available: #{time_periods.count}"
  time_periods.each { |tp| puts "     - #{tp.name}" }
  
  # Check if menus exist for these time periods
  if time_periods.any?
    recent_tp = time_periods.last
    menus_in_period = Menu.where(quarter: recent_tp.quarter, year: recent_tp.year, state: 'approved').count
    puts "   Menus in #{recent_tp.name}: #{menus_in_period}"
    
    items_in_period = Item.joins(:menu).where(menus: { quarter: recent_tp.quarter, year: recent_tp.year, state: 'approved' }).count
    puts "   Items in #{recent_tp.name}: #{items_in_period}"
  end
end

# Main execution
def main
  puts "\n🚀 Starting items_to_show fix..."
  
  # Check database state first
  check_database_state
  
  # Fix the specific ingredient
  success = fix_items_for_dashboard(INGREDIENT_NAME, dry_run: DRY_RUN)
  
  if success
    puts "\n🎯 Next Steps:"
    if DRY_RUN
      puts "1. Set DRY_RUN = false to apply the fix"
      puts "2. Check the results"
    else
      ingredient = Ingredient.where(name: INGREDIENT_NAME).first
      dashboard_id = ingredient.menu_item_dashboard.id
      puts "1. Check dashboard: /menu_item_dashboards_2/#{dashboard_id}"
      puts "2. Test API: /menu_item_dashboards_2/#{dashboard_id}/top_chains"
      puts "3. Verify charts are now populated"
    end
  else
    puts "\n❌ Could not fix the issue. Check the errors above."
  end
end

# Execute
main
