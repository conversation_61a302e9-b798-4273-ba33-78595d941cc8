#!/usr/bin/env ruby

# Simple Updated FlashDocs Worker Test
# Run in Rails console: load 'simple_updated_test.rb'

puts "=== Updated FlashDocs Worker Test ==="

# Create a worker instance
worker = Ai::FlashdocsWorker.new

# Enhanced test data with more content for better placeholder extraction
tool_name = 'social_trends_by_query'
chat_id = 1  # Make sure this chat exists, or create one

# Updated prompt with richer content
prompt = <<~TEXT
Coffee consumption has increased by 15% in urban areas. Analysis shows 8,500 social media posts with 87% positive sentiment.

Popular trends include cold brew (35% growth), oat milk lattes (28% growth), and specialty single-origin beans (22% growth).

Demographics: 25-34 age group (42% of consumers), urban areas (68% of consumption), high-income households (55% of premium purchases).

Trending post: "Love the new cold brew at @CoffeeShop! Perfect morning fuel with sustainable sourcing ☕ #coldbrew #sustainable"

Key insights: consumers prioritize quality over price, 73% willing to pay premium for ethically sourced beans.

Marketing opportunities: target urban millennials, emphasize sustainability, partner with local roasters, develop subscription services.
TEXT

query = 'Coffee Trends Test'

puts "\nTest Parameters:"
puts "- Tool: #{tool_name}"
puts "- Chat ID: #{chat_id}"
puts "- Query: #{query}"
puts "- Enhanced prompt with: statistics, demographics, social posts, trends"

# Quick test of content extraction
puts "\n=== Testing Content Extraction ==="
begin
  worker.instance_variable_set(:@query, query)
  
  stats = worker.send(:extract_statistics, prompt)
  puts "📊 Statistics: #{stats[0..80]}..."
  
  demographics = worker.send(:extract_demographics, prompt)
  puts "👥 Demographics: #{demographics[0..80]}..."
  
  posts = worker.send(:extract_social_posts, prompt)
  puts "📱 Social Posts: #{posts[0..80]}..."
  
  puts "✅ Content extraction working!"
rescue => e
  puts "❌ Content extraction error: #{e.message}"
end

# Main test - uncomment to run full API test
puts "\n=== Main Worker Test ==="
puts "Uncomment the code below to run full API test with new placeholders"

=begin
# Run synchronously - this will show all output and errors immediately
begin
  puts "🚀 Starting FlashDocs API call with enhanced placeholders..."
  start_time = Time.now
  
  result = worker.perform(tool_name, chat_id, prompt, query)
  
  end_time = Time.now
  puts "✅ Success! Presentation URL: #{result}"
  puts "⏱️  Duration: #{(end_time - start_time).round(2)} seconds"
  puts "\n🎯 The presentation now includes:"
  puts "   - Extracted statistics in title slide"
  puts "   - Demographics in dedicated slide"
  puts "   - Social media post examples"
  puts "   - Marketing opportunities"
  puts "   - All content organized with proper placeholders"
  
rescue => e
  puts "❌ Error: #{e.message}"
  puts "Backtrace: #{e.backtrace.first(10).join("\n")}"
  puts "\nTroubleshooting:"
  puts "- Check if Chat ID #{chat_id} exists: Chat.find(#{chat_id})"
  puts "- Verify API key is valid and not expired"
  puts "- Check network connectivity"
end
=end

puts "\n=== Instructions ==="
puts "1. To test content extraction only: script already ran above ✅"
puts "2. To test full API call: uncomment the 'Main Worker Test' section"
puts "3. Make sure Chat.find(#{chat_id}) exists in your database"
puts "4. The new version uses text_placeholder_manual_insertions for better control"
